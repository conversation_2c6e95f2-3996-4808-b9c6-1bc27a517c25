/* =====================
   教师管理专用样式（teacher_manage.css）
   ===================== */

/* ========== 1. 通用组件样式 ========== */
/* 基础按钮样式 */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* 基础徽章样式 */
.badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    border-radius: 1rem;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* ========== 2. 学生列表 ========== */
/* 学生列表容器 */
#studentList {
    max-height: 60vh;
}

/* 学生列表项基础样式 */
#studentList .list-group-item {
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
    padding: 0.75rem 1.25rem;
}

/* 学生列表项激活和悬停状态 */
#studentList .list-group-item.active,
#studentList .list-group-item:hover {
    border-left-color: var(--primary-color);
    background-color: #f8f9fa;
}

/* 学生列表项圆角处理 */
#studentList .list-group-item:first-child {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

#studentList .list-group-item:last-child {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* 学生列表徽章样式 */
#studentList .badge {
    font-size: 0.8em;
    padding: 0.3em 0.5em;
}

/* ========== 3. 留言系统 ========== */
/* 学生留言基础样式 */
#realNameMessages .alert,
#anonymousMessages .alert {
    background: #f8fafc;
    border-left: 4px solid var(--primary-color);
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px rgba(82,122,175,0.06);
    margin-bottom: 0.75rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

/* 实名留言样式 */
#realNameMessages .alert {
    border-left-color: var(--primary-color);
}

/* 匿名留言样式 */
#anonymousMessages .alert {
    border-left-color: var(--secondary-color);
    background: #f4f6f8;
}

/* 留言悬停效果 */
#realNameMessages .alert:hover,
#anonymousMessages .alert:hover {
    box-shadow: 0 4px 16px rgba(82,122,175,0.13);
    transform: translateY(-1px);
}

/* 留言文本样式 */
#realNameMessages .student-message-text,
#anonymousMessages .student-message-text {
    font-size: 1rem;
    color: #333;
    margin-top: 0.25rem;
    line-height: 1.6;
}

/* 留言删除按钮 */
#realNameMessages .btn-danger,
#anonymousMessages .btn-danger {
    font-size: 0.85em;
    padding: 0.2em 0.7em;
    border-radius: 0.375rem;
}

/* 留言时间样式 */
#realNameMessages small.text-muted,
#anonymousMessages small.text-muted {
    font-size: 0.85em;
}

/* 教师留言区域 */
#teacherMessageBox .alert-primary {
    background: linear-gradient(135deg, #e3eefd 60%, #f8fafc 100%);
    border-left: 4px solid var(--primary-color);
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px rgba(82,122,175,0.08);
    margin-bottom: 0.75rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

#teacherMessageBox .alert-primary:hover {
    box-shadow: 0 4px 16px rgba(82,122,175,0.15);
    transform: translateY(-1px);
}

#teacherMessageBox .btn-danger {
    font-size: 0.85em;
    padding: 0.2em 0.7em;
    border-radius: 0.375rem;
}

#teacherMessageBox .form-control {
    border-radius: 0.5rem;
    font-size: 1em;
    transition: all 0.3s ease;
}

#teacherMessageBox .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(82,122,175,0.25);
}

#teacherMessageBox form {
    margin-bottom: 1.2rem;
    border-bottom: 1px dashed #b6c6e3;
    padding-bottom: 0.7rem;
}

#teacherMessageBox .text-muted {
    font-size: 0.85em;
}

/* 卡片间距 */
#studentInfoPanel > .card {
    margin-bottom: 1.5rem;
}

/* 学生留言卡片 */
.student-msg-card {
    background: #f8fafc;
    border-radius: 0.75rem;
    box-shadow: 0 2px 12px rgba(82,122,175,0.08);
    border-left: 5px solid var(--primary-color, #4f8cff);
    transition: box-shadow 0.2s, border-color 0.2s;
    position: relative;
    min-height: 64px;
}
.student-msg-card:hover {
    box-shadow: 0 6px 24px rgba(82,122,175,0.16);
    border-left-color: #2b6cb0;
}
.student-msg-card .msg-content {
    word-break: break-all;
    white-space: pre-line;
}
.student-msg-card .msg-author {
    color: #2563eb;
    font-weight: 600;
    font-size: 1.08em;
}
.student-msg-card .msg-time {
    font-size: 0.92em;
}
.student-msg-card .student-message-text {
    font-size: 1.05em;
    color: #222;
    margin-top: 0.25rem;
    line-height: 1.7;
}
.student-msg-card .msg-actions {
    display: flex;
    align-items: flex-start;
    height: 100%;
}
.student-msg-card .msg-delete-btn {
    transition: opacity 0.2s;
    opacity: 0;
    margin-top: 2px;
}
.student-msg-card:hover .msg-delete-btn {
    display: inline-block;
    opacity: 1;
}
#realNameMessages, #anonymousMessages {
    min-height: 60px;
}
#realNameMessages .text-muted, #anonymousMessages .text-muted {
    margin: 1.5rem 0;
    text-align: center;
    display: block;
}

/* ========== 4. 请假审批面板 ========== */
/* 搜索输入框样式 */
#studentLeavePanel .input-group {
    max-width: 400px;
    margin: 0 auto 1.5rem auto;
    box-shadow: 0 2px 8px rgba(82,122,175,0.08);
    border-radius: 2rem;
    background: #f8fafc;
    padding: 0.5rem 1rem;
}
#studentLeavePanel .form-control {
    border-radius: 2rem 0 0 2rem;
    font-size: 1.05em;
    box-shadow: none;
}
#studentLeavePanel .input-group .btn {
    border-radius: 0 2rem 2rem 0;
    font-weight: 600;
    font-size: 1.05em;
}

#studentLeavePanel .btn-outline-secondary:hover {
    background: var(--primary-color, #4f8cff);
    color: #fff;
}

#studentLeavePanel .table {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(82,122,175,0.10);
    background: #fff;
}
#studentLeavePanel .table thead th {
    background: #f4f8fc;
    font-weight: 600;
    font-size: 1.08em;
    border-bottom: 2px solid #e3eefd;
}
#studentLeavePanel .table tbody tr {
    transition: background 0.18s;
}
#studentLeavePanel .table tbody tr:hover {
    background: #f0f6ff;
}
#studentLeavePanel .table .btn {
    margin: 0 2px;
    min-width: 70px;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}
#studentLeavePanel .btn-info {
    color: #fff;
    background: linear-gradient(90deg,#4fdcff 0,#4f8cff 100%);
    border: none;
}
#studentLeavePanel .btn-info:hover {
    background: linear-gradient(90deg,#4f8cff 0,#4fdcff 100%);
    color: #fff;
}
#studentLeavePanel .btn-success {
    background: linear-gradient(90deg,#5cb85c 0,#4f8cff 100%);
    border: none;
}
#studentLeavePanel .btn-success:hover {
    background: linear-gradient(90deg,#4f8cff 0,#5cb85c 100%);
}
#studentLeavePanel .btn-danger {
    background: linear-gradient(90deg,#ff6b6b 0,#ffb347 100%);
    border: none;
}
#studentLeavePanel .btn-danger:hover {
    background: linear-gradient(90deg,#ffb347 0,#ff6b6b 100%);
}
#studentLeavePanel .text-muted {
    color: #b0b8c9 !important;
}
#studentLeavePanel .table-responsive {
    border-radius: 1rem;
    overflow-x: auto;
}
#studentLeavePanel .spinner-border {
    vertical-align: middle;
}
#studentLeavePanel .empty-state {
    text-align: center;
    color: #b0b8c9;
    padding: 2.5rem 0;
    font-size: 1.1em;
}
#studentLeavePanel .empty-state i {
    font-size: 2.5em;
    margin-bottom: 0.5rem;
    display: block;
}

/* 请假审批操作按钮样式 */
#studentLeavePanel .approve-btn,
#studentLeavePanel .reject-btn,
#studentLeavePanel .cancel-btn {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    border: none;
    min-width: 80px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#studentLeavePanel .approve-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

#studentLeavePanel .approve-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40,167,69,0.3);
}

#studentLeavePanel .reject-btn {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
}

#studentLeavePanel .reject-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #e55a00 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220,53,69,0.3);
}

#studentLeavePanel .cancel-btn {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    color: white;
}

#studentLeavePanel .cancel-btn:hover {
    background: linear-gradient(135deg, #138496 0%, #5a32a3 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(23,162,184,0.3);
}

/* 处理中状态 */
#studentLeavePanel .btn.processing {
    opacity: 0.7;
    cursor: not-allowed;
}

/* 请假表格行样式 */
#studentLeavePanel .leave-row {
    transition: all 0.3s ease;
}

#studentLeavePanel .leave-row:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

/* 请假原因单元格样式 */
#studentLeavePanel .leave-reason-cell {
    max-width: 300px;
    min-width: 200px;
    position: relative;
    cursor: pointer;
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 10px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

#studentLeavePanel .leave-reason-cell:hover {
    background: #e9ecef;
    border-color: #007bff;
    box-shadow: 0 3px 10px rgba(0, 123, 255, 0.15);
    transform: translateY(-2px);
}

#studentLeavePanel .leave-reason-cell.clickable {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    position: relative;
}

#studentLeavePanel .leave-reason-cell.clickable::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 12px 12px 0;
    border-color: transparent #007bff transparent transparent;
    border-radius: 0 8px 0 0;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

#studentLeavePanel .leave-reason-cell.clickable:hover::after {
    opacity: 1;
}

#studentLeavePanel .leave-reason-cell.clickable:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #2196f3;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.2);
}

#studentLeavePanel .leave-reason-text {
    display: block;
    max-width: 100%;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-word;
    line-height: 1.5;
    min-height: 1.5em;
    max-height: 4.5em; 
    overflow: hidden;
    position: relative;
    color: #495057;
    font-size: 0.95em;
    font-weight: 400;
}

#studentLeavePanel .leave-reason-text.truncated::after {
    content: '...';
    position: absolute;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent 0%, #f8f9fa 40%);
    padding-left: 12px;
    color: #6c757d;
    font-weight: 600;
    font-size: 1.2em;
    line-height: 1;
}

/* 完整显示请假原因的模态框样式 */
.leave-reason-modal .modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    transform: scale(0.8);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.leave-reason-modal.show .modal-content {
    transform: scale(1);
    opacity: 1;
}

/* 模态框背景动画 */
.leave-reason-modal .modal-backdrop {
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
}

/* 模态框内容进入动画 */
.leave-reason-modal .modal-header,
.leave-reason-modal .modal-body,
.leave-reason-modal .modal-footer {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.5s ease;
    transition-delay: 0.1s;
}

.leave-reason-modal.show .modal-header,
.leave-reason-modal.show .modal-body,
.leave-reason-modal.show .modal-footer {
    opacity: 1;
    transform: translateY(0);
}

.leave-reason-modal .modal-body {
    transition-delay: 0.2s;
}

.leave-reason-modal .modal-footer {
    transition-delay: 0.3s;
}

.leave-reason-modal .modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-bottom: none;
    padding: 2rem 2.5rem;
    position: relative;
    overflow: hidden;
}

.leave-reason-modal .modal-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
}

.leave-reason-modal .modal-title {
    font-weight: 700;
    font-size: 1.3em;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    z-index: 1;
}

.leave-reason-modal .modal-body {
    max-height: 65vh;
    overflow-y: auto;
    padding: 2.5rem;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
}

.leave-reason-modal .reason-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.9;
    font-size: 1.15em;
    color: #2c3e50;
    background: white;
    padding: 2rem;
    border-radius: 16px;
    border-left: 8px solid #007bff;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: all 0.3s ease;
}

.leave-reason-modal .reason-content:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.leave-reason-modal .reason-content::before {
    content: '"';
    position: absolute;
    top: -20px;
    left: 30px;
    font-size: 5em;
    color: #007bff;
    opacity: 0.15;
    font-family: serif;
    line-height: 1;
    font-weight: 300;
}

.leave-reason-modal .reason-content::after {
    content: '"';
    position: absolute;
    bottom: -20px;
    right: 30px;
    font-size: 5em;
    color: #007bff;
    opacity: 0.15;
    font-family: serif;
    line-height: 1;
    font-weight: 300;
}

.leave-reason-modal .modal-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    padding: 1.5rem 2.5rem;
}

.leave-reason-modal .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border: none;
    border-radius: 12px;
    padding: 0.875rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.2);
}

.leave-reason-modal .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
    background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
}

/* ========== 5. 状态徽章样式 ========== */

/* 成功状态徽章 */
.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
}

/* 危险状态徽章 */
.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%) !important;
    color: white !important;
}

/* 警告状态徽章 */
.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffca2c 100%) !important;
    color: #212529 !important;
}

/* 信息状态徽章 */
.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    color: white !important;
}

/* 次要状态徽章 */
.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    color: white !important;
}

/* 表格单元格内容布局 */
#studentLeavePanel .table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

#studentLeavePanel .table td .d-flex.flex-column {
    gap: 0.25rem;
}

/* 空状态样式优化 */
#studentLeavePanel .table .text-center.py-5 {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    margin: 1rem 0;
}

#studentLeavePanel .table .text-center.py-5 .bi {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* 加载动画 */
#studentLeavePanel .spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 搜索高亮样式 */
#studentLeavePanel .highlight {
    background: linear-gradient(135deg, #ffc107 0%, #ffca2c 100%) !important;
    color: #212529 !important;
    padding: 0.1rem 0.3rem;
    border-radius: 4px;
    font-weight: 600;
    box-shadow: 0 1px 2px rgba(255,193,7,0.3);
}

/* 过滤状态样式 */
#studentLeavePanel .leave-row.filtered-out {
    display: none !important;
}

/* 无结果提示样式 */
#studentLeavePanel .no-results {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    margin: 1rem 0;
}

#studentLeavePanel .no-results .bi {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 搜索框样式优化 */
#studentLeavePanel .input-group {
    position: relative;
    transition: all 0.3s ease;
}

#studentLeavePanel .input-group:focus-within {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(82,122,175,0.15);
}

#studentLeavePanel .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* 搜索图标动画 */
#studentLeavePanel .input-group-text i {
    transition: transform 0.3s ease;
}

#studentLeavePanel .input-group:focus-within .input-group-text i {
    transform: scale(1.1);
    color: #007bff;
}

/* 请假统计卡片样式 */
/* 统计卡片基础样式 */
#leaveStats .card {
    border-radius: 15px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

/* 统计卡片悬停效果 */
#leaveStats .card:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: rgba(255,255,255,0.3);
}

/* 统计卡片微光效果 */
#leaveStats .card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

#leaveStats .card:hover::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 待审核状态卡片 */
#leaveStats .card.bg-warning {
    background: linear-gradient(135deg, rgba(255,193,7,0.15) 0%, rgba(255,202,44,0.1) 100%) !important;
    border-color: rgba(255,193,7,0.3);
}

#leaveStats .card.bg-warning:hover {
    background: linear-gradient(135deg, rgba(255,193,7,0.2) 0%, rgba(255,202,44,0.15) 100%) !important;
    box-shadow: 0 8px 25px rgba(255,193,7,0.3);
}

/* 已通过状态卡片 */
#leaveStats .card.bg-success {
    background: linear-gradient(135deg, rgba(40,167,69,0.15) 0%, rgba(32,201,151,0.1) 100%) !important;
    border-color: rgba(40,167,69,0.3);
}

#leaveStats .card.bg-success:hover {
    background: linear-gradient(135deg, rgba(40,167,69,0.2) 0%, rgba(32,201,151,0.15) 100%) !important;
    box-shadow: 0 8px 25px rgba(40,167,69,0.3);
}

/* 已拒绝状态卡片 */
#leaveStats .card.bg-danger {
    background: linear-gradient(135deg, rgba(220,53,69,0.15) 0%, rgba(253,126,20,0.1) 100%) !important;
    border-color: rgba(220,53,69,0.3);
}

#leaveStats .card.bg-danger:hover {
    background: linear-gradient(135deg, rgba(220,53,69,0.2) 0%, rgba(253,126,20,0.15) 100%) !important;
    box-shadow: 0 8px 25px rgba(220,53,69,0.3);
}

/* 总计状态卡片 */
#leaveStats .card.bg-info {
    background: linear-gradient(135deg, rgba(23,162,184,0.15) 0%, rgba(111,66,193,0.1) 100%) !important;
    border-color: rgba(23,162,184,0.3);
}

#leaveStats .card.bg-info:hover {
    background: linear-gradient(135deg, rgba(23,162,184,0.2) 0%, rgba(111,66,193,0.15) 100%) !important;
    box-shadow: 0 8px 25px rgba(23,162,184,0.3);
}

/* 统计卡片内容区域 */
#leaveStats .card-body {
    padding: 1.25rem;
    position: relative;
    z-index: 1;
}

/* 统计数字样式 */
#leaveStats .fw-bold {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

#leaveStats .card:hover .fw-bold {
    transform: scale(1.05);
}

/* 统计图标样式 */
#leaveStats .bi {
    font-size: 2rem;
    opacity: 0.8;
    transition: all 0.3s ease;
}

#leaveStats .card:hover .bi {
    opacity: 1;
    transform: scale(1.1) rotate(5deg);
}

/* 统计数字动画效果 */
#leaveCount,
#pendingCount,
#approvedCount,
#rejectedCount,
#cancelledCount {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-block;
}

/* 数字计数动画 */
@keyframes countUp {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    50% {
        opacity: 0.7;
        transform: translateY(-5px) scale(1.1);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 数字更新时的动画 */
.stat-number-update {
    animation: countUp 0.6s ease-out;
}

/* 数字脉冲效果 */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.stat-number-pulse {
    animation: pulse 1s ease-in-out infinite;
}

/* ========== 6. 响应式设计 ========== */
/* 平板设备 (991.98px 及以下) */
@media (max-width: 991.98px) {
    #studentList {
        max-height: none; /* 在较小屏幕上取消高度限制 */
    }
}

/* 手机设备 (768px 及以下) */
@media (max-width: 768px) {
    /* 请假审批面板 */
    #studentLeavePanel .input-group {
        max-width: 100%;
        padding: 0.5rem 0.5rem;
    }

    #studentLeavePanel .table-responsive {
        border-radius: 0.5rem;
    }

    /* 请假原因单元格 */
    #studentLeavePanel .leave-reason-cell {
        max-width: 150px;
        min-width: 120px;
        padding: 8px 10px;
    }

    #studentLeavePanel .leave-reason-text {
        max-height: 3em; /* 移动端最多显示2行 */
        font-size: 0.9em;
    }

    /* 请假原因模态框 */
    .leave-reason-modal .modal-dialog {
        margin: 1rem;
    }

    .leave-reason-modal .modal-body {
        padding: 1rem;
    }

    .leave-reason-modal .reason-content {
        padding: 1.2rem;
        font-size: 1em;
    }

    /* 统计卡片 */
    #leaveStats .card-body {
        padding: 0.75rem;
    }

    #leaveStats .fw-bold {
        font-size: 1.25rem;
    }

    #leaveStats .bi {
        font-size: 1.5rem;
    }

    /* 按钮适配 */
    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }

    /* 徽章适配 */
    .badge {
        font-size: 0.75rem;
        padding: 0.3rem 0.5rem;
    }
}
