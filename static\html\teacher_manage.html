<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生管理系统</title>
    <link rel="stylesheet" href="/css/public.css">
    <link rel="stylesheet" href="/css/top.css">
    <link rel="stylesheet" href="/css/teacher_manage.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
    .sidebar-sticky {
        position: sticky;
        top: 80px; /* 适配你的导航栏高度 */
        z-index: 100;
    }

    @media (max-width: 991.98px) {
        .sidebar-sticky {
            position: static;
            top: auto;
        }
    }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="#">
                <i class="bi bi-people-fill me-2"></i>
                <span>学生管理分析系统</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#"><i class="bi bi-house-door me-1"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="helpBtn"><i class="bi bi-question-circle me-1"></i> 帮助</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right me-1"></i> 退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        <!-- 欢迎信息 -->
        <div class="welcome-section text-center mb-5 animate__animated animate__fadeIn">
            <h1 class="display-5 fw-bold text-primary">学生管理分析系统</h1>
            <p class="lead text-muted">选择对应功能，管理学生或者分析学生成绩</p>
        </div>

        <div class="row g-4">
            <!-- 左侧控制面板 -->
            <div class="col-lg-3 d-flex flex-column">
                <!-- 新增：班级切换（不固定） -->
                <div class="card shadow-sm animate__animated animate__fadeInLeft mb-3">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0 d-flex align-items-center">
                            <i class="bi bi-person-workspace me-2"></i> 我的班级
                        </h5>
                    </div>
                    <div class="card-body p-3">
                        <div id="class-switch-container" class="d-grid gap-2">
                            <!-- 班级按钮将在这里动态生成 -->
                            <p class="text-muted text-center">正在加载班级...</p>
                        </div>
                    </div>
                </div>
                <!-- sticky部分：学生管理面板+成绩分析面板+学生列表面板 -->
                <div class="sidebar-sticky">
                    <!-- 学生管理面板 -->
                    <div class="card shadow-sm animate__animated animate__fadeInLeft mb-3">
                        <div class="card-header bg-light">
                            <button class="btn btn-link p-0 d-flex align-items-center text-decoration-none w-100" type="button" data-bs-toggle="collapse" data-bs-target="#studentManagePanelCollapse" aria-expanded="true" aria-controls="studentManagePanelCollapse">
                                <h5 class="card-title mb-0 d-flex align-items-center">
                                    <i class="bi bi-person-gear me-2"></i> 学生管理面板
                                </h5>
                                <i class="bi bi-chevron-down ms-auto"></i>
                            </button>
                        </div>
                        <div id="studentManagePanelCollapse" class="collapse show">
                            <div class="card-body p-3">
                                <!-- 学生信息管理子面板 -->
                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="bi bi-person-lines-fill me-2 text-primary"></i>
                                        <span class="fw-semibold">学生信息管理</span>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary w-100 active" id="studentInfoTabBtn">
                                            <i class="bi bi-people-fill me-1"></i> 进入学生信息管理
                                        </button>
                                    </div>
                                </div>
                                <!-- 学生请假管理子面板 -->
                                <div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="bi bi-calendar-check me-2 text-success"></i>
                                        <span class="fw-semibold">学生请假管理</span>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-success w-100" id="studentLeaveTabBtn">
                                            <i class="bi bi-check-circle me-1"></i> 进入请假审批
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 成绩分析面板 -->
                    <div class="card shadow-sm animate__animated animate__fadeInLeft mb-3">
                        <div class="card-header bg-light">
                            <a href="/html/teacher_analyze.html" class="btn btn-link p-0 d-flex align-items-center text-decoration-none w-100" type="button">
                                <h5 class="card-title mb-0 d-flex align-items-center">
                                    <i class="bi bi-sliders me-2"></i> 成绩分析面板
                                </h5>
                                <i class="bi bi-chevron-right ms-auto"></i>
                            </a>
                        </div>
                    </div>
                    <!-- 学生列表面板 -->
                    <div class="card shadow-sm animate__animated animate__fadeInLeft mb-3">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-people-fill me-2"></i> 学生列表
                            </h5>
                            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#studentListCollapse">
                                <i class="bi bi-chevron-down"></i>
                            </button>
                        </div>
                        <div class="collapse show" id="studentListCollapse">
                            <div class="card-body p-3">
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control form-control-sm" id="studentListSearch" placeholder="搜索学生...">
                                    <button class="btn btn-outline-secondary btn-sm" type="button">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                                <div id="studentList" class="list-group list-group-flush" style="max-height: calc(100vh - 400px); overflow-y: auto;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧主内容区 -->
            <div class="col-lg-9">
                <div id="error-container" class="alert alert-danger d-none animate__animated animate__fadeIn" role="alert"></div>
                <!-- 右侧tab切换：学生信息/请假审批 -->
                <div id="studentInfoPanel">
                    <!-- 学生留言卡片 -->
                    <div class="card shadow-sm animate__animated animate__fadeInRight mb-4">
                        <div class="card-header bg-light d-flex align-items-center">
                            <h5 class="card-title mb-0"><i class="bi bi-chat-dots me-1"></i> 学生留言</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-2 text-secondary small">实名留言</div>
                                    <div id="realNameMessages"></div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-2 text-secondary small">匿名留言</div>
                                    <div id="anonymousMessages"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 学生详细信息卡片 -->
                    <div class="card shadow-sm animate__animated animate__fadeInRight mb-4">
                        <div class="card-header bg-light d-flex align-items-center">
                            <h5 class="card-title mb-0"><i class="bi bi-person-vcard me-2"></i> 学生详细信息</h5>
                        </div>
                        <div class="card-body">
                            <div id="studentDetailPanel" style="min-height: 300px;">
                                <div class="col-12 text-center py-5 h-100 d-flex flex-column justify-content-center align-items-center">
                                    <i class="bi bi-info-circle text-muted fs-3"></i>
                                    <p class="mt-2 text-muted">请从左侧列表中选择一个学生以查看详细信息</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 教师留言卡片 -->
                    <div class="card shadow-sm animate__animated animate__fadeInRight mb-4">
                        <div class="card-header bg-light d-flex align-items-center">
                            <h5 class="card-title mb-0"><i class="bi bi-person-lines-fill me-1"></i> 教师留言</h5>
                        </div>
                        <div class="card-body">
                            <div id="teacherMessageBox"></div>
                        </div>
                    </div>
                </div>
                <div id="studentLeavePanel" style="display:none;">
                    <!-- 请假审批面板整体移到这里 -->
                    <div class="card shadow-sm animate__animated animate__fadeInRight">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <h5 class="card-title mb-0 d-flex align-items-center">
                                    <i class="bi bi-calendar-check me-2"></i> 学生请假审批
                                </h5>
                            </div>
                            <!-- 班级展示，右上角 badge -->
                            <span id="leaveClassDisplay" class="badge bg-primary fs-6 px-3 py-2" style="letter-spacing:1px;"></span>
                        </div>
                        <div class="card-body p-3">
                            <!-- 统计信息 -->
                            <div class="row g-3 mb-4" id="leaveStats">
                                <div class="col-md-3">
                                    <div class="card bg-warning bg-opacity-10 border-warning">
                                        <div class="card-body text-center py-2">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <i class="bi bi-hourglass-split text-warning fs-4 me-2"></i>
                                                <div>
                                                    <div class="fw-bold text-warning" id="pendingCount">0</div>
                                                    <small class="text-muted">待审批</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success bg-opacity-10 border-success">
                                        <div class="card-body text-center py-2">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <i class="bi bi-check-circle text-success fs-4 me-2"></i>
                                                <div>
                                                    <div class="fw-bold text-success" id="approvedCount">0</div>
                                                    <small class="text-muted">已批准</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-danger bg-opacity-10 border-danger">
                                        <div class="card-body text-center py-2">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <i class="bi bi-x-circle text-danger fs-4 me-2"></i>
                                                <div>
                                                    <div class="fw-bold text-danger" id="rejectedCount">0</div>
                                                    <small class="text-muted">已拒绝</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info bg-opacity-10 border-info">
                                        <div class="card-body text-center py-2">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <i class="bi bi-arrow-repeat text-info fs-4 me-2"></i>
                                                <div>
                                                    <div class="fw-bold text-info" id="cancelledCount">0</div>
                                                    <small class="text-muted">已销假</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 搜索本班学生输入框 -->
                            <div class="input-group mb-4 justify-content-center" style="max-width:400px;margin:auto;">
                                <input type="text" class="form-control form-control-sm" id="leaveStudentSearchInput" placeholder="搜索学生姓名、学号或请假原因..." style="border-radius: 20px 0 0 20px;">
                                <span class="input-group-text bg-white" style="border-radius: 0 20px 20px 0;"><i class="bi bi-search"></i></span>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover align-middle text-center">
                                    <thead class="table-light">
                                        <tr>
                                            <th>学生</th>
                                            <th>请假时间</th>
                                            <th>原因</th>
                                            <th>状态</th>
                                            <th>申请时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="leaveApproveTableBody">
                                        <tr><td colspan="6" class="text-muted">请输入班级并查询</td></tr>
                                    </tbody>
                                </table>
                            </div>
                            <!-- Toast提示 -->
                            <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1055;">
                                <div id="leaveApproveToast" class="toast align-items-center text-bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                                    <div class="d-flex">
                                        <div class="toast-body">
                                            操作成功！
                                        </div>
                                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑学生模态框 -->
    <div class="modal fade" id="studentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="studentModalTitle">添加学生</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="studentForm">
                        <input type="hidden" id="studentDbId">
                        <div class="mb-3">
                            <label for="studentName" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="studentName" required>
                        </div>
                        <div class="mb-3">
                            <label for="studentId" class="form-label">学号</label>
                            <input type="text" class="form-control" id="studentId" required>
                        </div>
                        <div class="mb-3">
                            <label for="studentClass" class="form-label">班级</label>
                            <input type="text" class="form-control" id="studentClass" required>
                        </div>
                        <div class="mb-3">
                            <label for="studentUsername" class="form-label">账号</label>
                            <input type="text" class="form-control" id="studentUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="studentPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="studentPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="teacherMessage" class="form-label">教师留言</label>
                            <input type="text" class="form-control" id="teacherMessage">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">保存</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-question-circle me-2"></i>使用帮助</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">基本操作</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="bi bi-1-circle me-2"></i>选择班级或搜索学生</li>
                                <li class="mb-2"><i class="bi bi-2-circle me-2"></i>点击添加、编辑或删除学生</li>
                                <li class="mb-2"><i class="bi bi-3-circle me-2"></i>填写或修改学生信息</li>
                                <li class="mb-2"><i class="bi bi-4-circle me-2"></i>保存后自动刷新列表</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">功能说明</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>支持班级筛选和学生搜索</li>
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>支持学生信息的增删改查</li>
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>支持教师留言管理</li>
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>支持批量导入导出（可后续扩展）</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script src="/js/public.js"></script>
    <script src="/js/top.js"></script>
    <script src="/js/teacher_manage.js"></script>
</body>
</html>