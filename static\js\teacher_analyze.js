/**
 * ========================================
 * 教师分析页面功能模块 (teacher_analyze.js)
 * ========================================
 * 专门处理教师分析页面的所有功能：
 * 1. 页面初始化和数据加载
 * 2. 考试和班级数据管理
 * 3. 图表生成和显示
 * 4. 学生分析和成绩编辑
 * 5. 搜索和筛选功能
 * 6. 数据导出功能
 *
 * 依赖：public.js（通用工具函数）、top.js（导航和认证）
 * ========================================
 */

// ========================================
// 1. 全局变量和页面初始化模块
// ========================================

// 全局状态变量
let currentExam = '';
let currentClass = '';
let currentData = null; // 用于存储当前查询的完整数据

/**
 * 页面初始化入口函数
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeTeacherAnalyzePage();
});

/**
 * 初始化教师分析页面
 */
function initializeTeacherAnalyzePage() {
    // 检查登录状态（由 top.js 提供）
    if (!checkLogin()) {
        return;
    }

    // 初始化页面组件
    initializePageComponents();

    // 加载基础数据
    loadExamAndClassData();

    // 设置搜索功能
    setupSearch();

    // 绑定事件监听器
    bindEventListeners();
}

/**
 * 初始化页面组件
 */
function initializePageComponents() {
    // 检查是否为班主任，显示相应的管理面板
    const isHeadTeacher = sessionStorage.getItem('is_head_teacher') === 'true';
    const studentManagePanel = document.getElementById('studentManagePanel');
    if (studentManagePanel && isHeadTeacher) {
        studentManagePanel.style.display = 'block';
    }
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 生成图表按钮
    const generateBtn = document.getElementById('generateBtn');
    if (generateBtn) {
        generateBtn.addEventListener('click', generateCharts);
    }

    // 刷新数据按钮
    const refreshOverallBtn = document.getElementById('refreshOverallBtn');
    if (refreshOverallBtn) {
        refreshOverallBtn.addEventListener('click', refreshAllData);
    }

    // 导出数据按钮
    const exportDataBtn = document.getElementById('exportDataBtn');
    if (exportDataBtn) {
        exportDataBtn.addEventListener('click', handleExportDataClick);
    }

    // 帮助按钮
    const helpBtn = document.getElementById('helpBtn');
    if (helpBtn) {
        helpBtn.addEventListener('click', showHelpModal);
    }
}

/**
 * 处理导出数据按钮点击
 */
function handleExportDataClick() {
    // 检查是否选择了考试和班级
    const examSelect = document.getElementById('examSelect');
    const classSelect = document.getElementById('classSelect');
    const selectedExam = examSelect.value;
    const selectedClass = classSelect.value;

    if (!selectedExam || !selectedClass) {
        Swal.fire({
            title: '导出失败',
            text: '请先选择考试和班级',
            icon: 'error'
        });
        return;
    }

    // 更新全局变量以确保一致性
    currentExam = selectedExam;
    currentClass = selectedClass;

    // 调用导出函数
    handleExportData();
}

/**
 * 显示帮助模态框
 */
function showHelpModal() {
    const helpModal = new bootstrap.Modal(document.getElementById('helpModal'));
    helpModal.show();
}

// ========================================
// 2. 考试和班级数据管理模块
// ========================================

/**
 * 设置生成按钮的加载状态
 * @param {boolean} isLoading - 是否处于加载状态
 */
function setLoading(isLoading) {
    const generateBtn = document.getElementById('generateBtn');
    if (!generateBtn) {
        console.error('找不到生成按钮元素');
        return;
    }

    if (isLoading) {
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="bi bi-spinner bi-spin me-1"></i> 生成中...';
    } else {
        generateBtn.disabled = !currentExam || !currentClass;
        generateBtn.innerHTML = '<i class="bi bi-graph-up me-1"></i> 生成分析图表';
    }
}

/**
 * 加载考试和班级数据，并实现联动
 */
async function loadExamAndClassData() {
    // 使用 public.js 中的 Toast 函数
    showLoadingToast();

    try {
        const grade = sessionStorage.getItem('grade');
        if (!grade) {
            showErrorToast('未获取到年级信息，请重新登录');
            return;
        }

        // 并行获取考试和班级数据
        const [examsData, classesData] = await Promise.all([
            fetchExamData(grade),
            fetchClassData(grade)
        ]);

        // 填充下拉框
        populateExamSelect(examsData.exams);
        populateClassSelect(classesData.classes);

        // 设置考试和班级联动
        await setupExamClassLinkage(grade, examsData.exams, classesData.classes);

    } catch (error) {
        console.error('加载数据失败:', error);
        showErrorToast(error.message || '加载考试和班级数据失败，请检查网络连接');
    } finally {
        hideLoadingToast();
        setLoading(false);
    }
}

/**
 * 获取考试数据
 * @param {string} grade - 年级
 * @returns {Promise<Object>} 考试数据
 */
async function fetchExamData(grade) {
    const response = await fetch(`/api/grade-exams?grade=${encodeURIComponent(grade)}`);
    const data = await response.json();
    if (data.error) throw new Error(data.error);
    return data;
}

/**
 * 获取班级数据
 * @param {string} grade - 年级
 * @returns {Promise<Object>} 班级数据
 */
async function fetchClassData(grade) {
    const teacherUsername = sessionStorage.getItem('username');
    const response = await fetch(`/api/grade-classes?grade=${encodeURIComponent(grade)}&teacher_username=${encodeURIComponent(teacherUsername)}`);
    const data = await response.json();
    if (data.error) throw new Error(data.error);
    return data;
}

/**
 * 填充考试选择下拉框
 * @param {Array} exams - 考试列表
 */
function populateExamSelect(exams) {
    const examSelect = document.getElementById('examSelect');
    if (!examSelect) return;

    examSelect.innerHTML = '<option value="">请选择考试</option>';
    if (exams && Array.isArray(exams)) {
        exams.forEach(exam => {
            const option = document.createElement('option');
            option.value = exam;
            option.textContent = exam;
            examSelect.appendChild(option);
        });
    }
}

/**
 * 填充班级选择下拉框
 * @param {Array} classes - 班级列表
 */
function populateClassSelect(classes) {
    const classSelect = document.getElementById('classSelect');
    if (!classSelect) return;

    classSelect.innerHTML = '<option value="">请选择班级</option>';
    if (classes && Array.isArray(classes)) {
        classes.forEach(className => {
            const option = document.createElement('option');
            option.value = className;
            option.textContent = className;
            classSelect.appendChild(option);
        });
    }
}

/**
 * 设置考试和班级联动功能
 * @param {string} grade - 年级
 * @param {Array} allExams - 所有考试列表
 * @param {Array} allClasses - 所有班级列表
 */
async function setupExamClassLinkage(grade, allExams, allClasses) {
    const teacherUsername = sessionStorage.getItem('username');
    const examSelect = document.getElementById('examSelect');
    const classSelect = document.getElementById('classSelect');

    if (!examSelect || !classSelect) return;

    // 预加载每个考试对应的班级映射
    const examClassMap = await buildExamClassMap(grade, allExams, teacherUsername);

    // 设置考试选择变化监听器
    examSelect.addEventListener('change', function() {
        currentExam = this.value;
        handleExamSelectionChange(currentExam, examClassMap, allClasses);
        setLoading(false);
    });

    // 设置班级选择变化监听器
    classSelect.addEventListener('change', function() {
        currentClass = this.value;
        handleClassSelectionChange(currentClass, examClassMap, allExams);
        setLoading(false);
    });
}

/**
 * 构建考试-班级映射关系
 * @param {string} grade - 年级
 * @param {Array} allExams - 所有考试列表
 * @param {string} teacherUsername - 教师用户名
 * @returns {Promise<Object>} 考试-班级映射对象
 */
async function buildExamClassMap(grade, allExams, teacherUsername) {
    const examClassMap = {};

    for (const exam of allExams) {
        try {
            const response = await fetch(`/api/grade-classes?grade=${encodeURIComponent(grade)}&exam=${encodeURIComponent(exam)}&teacher_username=${encodeURIComponent(teacherUsername)}`);
            const data = await response.json();
            examClassMap[exam] = data.classes || [];
        } catch (error) {
            console.warn(`获取考试 ${exam} 的班级数据失败:`, error);
            examClassMap[exam] = [];
        }
    }

    return examClassMap;
}

/**
 * 处理考试选择变化
 * @param {string} selectedExam - 选中的考试
 * @param {Object} examClassMap - 考试-班级映射
 * @param {Array} allClasses - 所有班级列表
 */
function handleExamSelectionChange(selectedExam, examClassMap, allClasses) {
    const classSelect = document.getElementById('classSelect');
    if (!classSelect) return;

    if (selectedExam) {
        // 根据选中的考试过滤班级选项
        const availableClasses = examClassMap[selectedExam] || [];
        populateClassSelect(availableClasses);

        // 如果当前选中的班级不在可用班级中，清空班级选择
        if (currentClass && !availableClasses.includes(currentClass)) {
            currentClass = '';
            classSelect.value = '';
        }
    } else {
        // 如果没有选择考试，显示教师任教的所有班级
        populateClassSelect(allClasses);
    }
}

/**
 * 处理班级选择变化
 * @param {string} selectedClass - 选中的班级
 * @param {Object} examClassMap - 考试-班级映射
 * @param {Array} allExams - 所有考试列表
 */
function handleClassSelectionChange(selectedClass, examClassMap, allExams) {
    const examSelect = document.getElementById('examSelect');
    if (!examSelect) return;

    if (selectedClass) {
        // 根据选中的班级过滤考试选项
        const availableExams = getAvailableExamsForClass(selectedClass, examClassMap);

        // 如果当前选中的考试不在可用考试中，清空考试选择
        if (currentExam && !availableExams.includes(currentExam)) {
            currentExam = '';
            examSelect.value = '';
        }
    } else {
        // 如果没有选择班级，显示所有考试
        populateExamSelect(allExams);
    }
}

/**
 * 获取指定班级可用的考试列表
 * @param {string} className - 班级名称
 * @param {Object} examClassMap - 考试-班级映射
 * @returns {Array} 可用考试列表
 */
function getAvailableExamsForClass(className, examClassMap) {
    const availableExams = [];
    for (const [exam, classes] of Object.entries(examClassMap)) {
        if (classes.includes(className)) {
            availableExams.push(exam);
        }
    }
    return availableExams;
}


// ========================================
// 3. 图表生成和显示模块
// ========================================

/**
 * 生成分析图表
 */
async function generateCharts() {
    const grade = sessionStorage.getItem('grade');

    // 验证必要参数
    if (!validateChartGenerationParams(grade)) {
        return;
    }

    showLoadingToast();
    setLoading(true);

    try {
        const chartData = await requestChartGeneration(grade);

        // 保存数据并更新显示
        currentData = chartData;
        updateCharts(chartData);

        showSuccessToast('图表生成成功');
    } catch (error) {
        showErrorToast(error.message || '生成图表失败，请重试');
    } finally {
        hideLoadingToast();
        setLoading(false);
    }
}

/**
 * 验证图表生成参数
 * @param {string} grade - 年级
 * @returns {boolean} 参数是否有效
 */
function validateChartGenerationParams(grade) {
    if (!currentExam || !currentClass || !grade) {
        showErrorToast('请选择考试和班级');
        return false;
    }
    return true;
}

/**
 * 请求后端生成图表数据
 * @param {string} grade - 年级
 * @returns {Promise<Object>} 图表数据
 */
async function requestChartGeneration(grade) {
    const response = await fetch('/generate_charts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            grade: grade,
            exam: currentExam,
            class_name: currentClass
        })
    });

    const data = await response.json();
    if (data.error) {
        throw new Error(data.error);
    }

    return data;
}

/**
 * 更新图表显示
 * @param {Object} data - 图表数据
 */
function updateCharts(data) {
    const chartsContainer = document.getElementById('chartsContainer');
    const individualCharts = document.getElementById('individual-charts');
    const classStatsInfo = document.getElementById('classStatsInfo');

    // 清空容器
    chartsContainer.innerHTML = '';

    // 验证数据有效性
    if (!data.charts) {
        displayNoChartsMessage(chartsContainer);
        return;
    }

    // 显示班级总体图表
    displayClassCharts(data.charts, chartsContainer);

    // 更新学生列表和个人图表
    updateStudentDisplays(data, individualCharts);

    // 渲染班级统计信息
    renderClassStatistics(data, classStatsInfo);
}

/**
 * 显示无图表数据的消息
 * @param {HTMLElement} container - 容器元素
 */
function displayNoChartsMessage(container) {
    container.innerHTML = `
        <div class="col-12 text-center py-5">
            <i class="bi bi-exclamation-triangle text-warning fs-3"></i>
            <p class="mt-2 text-muted">未找到图表数据。</p>
        </div>
    `;
}

/**
 * 显示班级总体图表
 * @param {Object} charts - 图表数据
 * @param {HTMLElement} container - 容器元素
 */
function displayClassCharts(charts, container) {
    const examName = currentExam;
    const className = currentClass;

    if (charts.class_radar && charts.class_bar && charts.class_table) {
        // 使用 public.js 中的 createChartCard 函数
        const classRadarCard = createChartCard(charts.class_radar, '班级总体雷达图', 'radar', {
            examName, className, studentName: '班级平均分', chartType: '雷达图', useServerDownload: true
        });
        const classBarCard = createChartCard(charts.class_bar, '班级总体柱状图', 'bar', {
            examName, className, studentName: '班级平均分', chartType: '柱状图', useServerDownload: true
        });
        const classTableCard = createChartCard(charts.class_table, '班级总体表格', 'table', {
            examName, className, studentName: '班级平均分', chartType: '表格图', useServerDownload: true
        });

        container.append(classRadarCard, classBarCard, classTableCard);
    }
}

/**
 * 更新学生显示（列表和个人图表）
 * @param {Object} data - 完整数据
 * @param {HTMLElement} individualCharts - 个人图表容器
 */
function updateStudentDisplays(data, individualCharts) {
    if (data.student_info) {
        updateStudentList(data.student_info);
        displayTopStudentsCharts(data, individualCharts);
    } else {
        displayNoStudentMessage(individualCharts);
    }
}

/**
 * 显示前3名学生的图表
 * @param {Object} data - 完整数据
 * @param {HTMLElement} container - 容器元素
 */
function displayTopStudentsCharts(data, container) {
    const topStudents = Object.entries(data.student_info)
        .map(([studentId, info]) => ({ id: studentId, rank: info.排名 }))
        .sort((a, b) => a.rank - b.rank)
        .slice(0, 3);

    container.innerHTML = '';
    topStudents.forEach(student => {
        container.appendChild(createStudentAnalysisCard(student.id, data));
    });
}

/**
 * 显示无学生信息的消息
 * @param {HTMLElement} container - 容器元素
 */
function displayNoStudentMessage(container) {
    container.innerHTML = `
        <div class="col-12 text-center py-5">
            <i class="bi bi-info-circle text-muted fs-3"></i>
            <p class="mt-2 text-muted">从左侧学生列表或上方搜索框选择学生后显示个人成绩分析图表</p>
        </div>
    `;
}

/**
 * 渲染班级统计信息
 * @param {Object} data - 数据对象
 * @param {HTMLElement} container - 容器元素
 */
function renderClassStatistics(data, container) {
    if (data.class_avg_data && data.class_stats) {
        const statisticsHtml = generateClassStatisticsHtml(data.class_avg_data, data.class_stats);
        container.innerHTML = statisticsHtml;
    } else {
        container.innerHTML = '';
    }
}
/**
 * 生成班级统计信息HTML
 * @param {Object} avg - 平均分数据
 * @param {Object} stats - 统计数据
 * @returns {string} HTML字符串
 */
function generateClassStatisticsHtml(avg, stats) {
    const subjects = ['语文', '数学', '英语', '物理', '化学', '生物'];
    const fullMarks = currentData && currentData.full_marks ? currentData.full_marks : {};

    return `
        <div class="class-stats-cards row g-3 mb-3">
            <div class="col-6 col-md-2"><div class="stat-card stat-blue"><div class="stat-label">班级人数</div><div class="stat-value">${stats.total_students ?? '-'}</div></div></div>
            <div class="col-6 col-md-2"><div class="stat-card stat-green"><div class="stat-label">合格率</div><div class="stat-value">${stats.pass_rate ?? '-'}%</div></div></div>
            <div class="col-6 col-md-2"><div class="stat-card"><div class="stat-label">平均总分</div><div class="stat-value">${avg['总分'] ?? '-'}</div></div></div>
            <div class="col-6 col-md-2"><div class="stat-card"><div class="stat-label">最高分</div><div class="stat-value">${stats.highest_score ?? '-'}</div></div></div>
            <div class="col-6 col-md-2"><div class="stat-card"><div class="stat-label">最低分</div><div class="stat-value">${stats.lowest_score ?? '-'}</div></div></div>
            <div class="col-6 col-md-2"><div class="stat-card"><div class="stat-label">标准差</div><div class="stat-value">${stats.score_std ?? '-'}</div></div></div>
        </div>
        <div class="mb-3">
            <h6 class="mb-3 d-flex align-items-center">
                <i class="bi bi-bar-chart-line me-2"></i>
                班级各科平均分
            </h6>
            <div class="row g-3">
                ${generateSubjectScoresHtml(subjects, avg, fullMarks)}
                ${generateTotalScoreHtml(avg, fullMarks)}
            </div>
        </div>
    `;
}

/**
 * 生成各科成绩HTML
 * @param {Array} subjects - 科目列表
 * @param {Object} avg - 平均分数据
 * @param {Object} fullMarks - 满分配置
 * @returns {string} HTML字符串
 */
function generateSubjectScoresHtml(subjects, avg, fullMarks = {}) {
    return subjects.map(subject => {
        const score = avg[subject] ?? 0;
        const maxScore = fullMarks[subject] || 100;
        const { scoreClass, levelLabel, displayVal, percent } = calculateScoreMetrics(score, maxScore);

        return `
            <div class="col-md-4 col-sm-6">
                <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                        <span class="fw-semibold">${subject}</span><br>
                        <small class="text-muted">${levelLabel}</small>
                    </div>
                    <div class="text-end">
                        <span class="fs-5 ${scoreClass}">${displayVal} / ${maxScore} <span style='font-size:0.9em;'>(${percent}%)</span></span>
                        <br>
                        <small class="text-muted">分</small>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

/**
 * 生成总分HTML
 * @param {Object} avg - 平均分数据
 * @param {Object} fullMarks - 满分配置
 * @returns {string} HTML字符串
 */
function generateTotalScoreHtml(avg, fullMarks = {}) {
    const totalFullMark = Object.values(fullMarks).reduce((sum, mark) => sum + mark, 600);
    return `
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center p-3 border rounded bg-light">
                <div>
                    <span class="fw-bold fs-6">总平均分</span><br>
                    <small class="text-muted">班级整体水平</small>
                </div>
                <div class="text-end">
                    <span class="fs-4 fw-bold text-primary">${avg['总分'] ? avg['总分'].toFixed(1) : '-'} / ${totalFullMark}</span>
                    <br>
                    <small class="text-muted">分</small>
                </div>
            </div>
        </div>
    `;
}

/**
 * 计算分数相关指标
 * @param {number} score - 分数
 * @param {number} maxScore - 满分
 * @returns {Object} 分数指标对象
 */
function calculateScoreMetrics(score, maxScore) {
    let scoreClass = '';
    let levelLabel = '';

    if (typeof score === 'number' && score > 0) {
        const percent = Math.round((score / maxScore) * 1000) / 10;
        if (percent < 30) {
            scoreClass = 'text-danger fw-bold';
            levelLabel = '需努力';
        } else if (percent < 60) {
            scoreClass = 'text-warning fw-bold';
            levelLabel = '及格';
        } else if (percent < 80) {
            scoreClass = 'text-primary fw-bold';
            levelLabel = '良好';
        } else {
            scoreClass = 'text-success fw-bold';
            levelLabel = '优秀';
        }
    } else {
        scoreClass = 'text-muted';
        levelLabel = '暂无数据';
    }

    const displayVal = typeof score === 'number' ? score.toFixed(1) : '-';
    const percent = typeof score === 'number' && score > 0 ? Math.round((score / maxScore) * 1000) / 10 : 0;

    return { scoreClass, levelLabel, displayVal, percent };
}

// ========================================
// 4. 学生分析和成绩编辑模块
// ========================================

/**
 * 创建学生分析卡片
 * @param {string} studentId - 学生ID
 * @param {Object} data - 完整数据
 * @returns {DocumentFragment} 文档片段
 */
function createStudentAnalysisCard(studentId, data) {
    if (!data || !data.student_info || !data.student_info[studentId]) {
        return document.createElement('div');
    }

    const studentInfo = data.student_info[studentId];
    const charts = data.charts;
    const studentNo = studentInfo.学号;
    const fragment = document.createDocumentFragment();

    // 创建学生信息标题
    const titleElement = createStudentTitleElement(studentInfo);
    fragment.appendChild(titleElement);

    // 创建学生图表
    const chartElements = createStudentChartElements(charts, studentNo, studentInfo);
    chartElements.forEach(element => fragment.appendChild(element));

    return fragment;
}

/**
 * 创建学生标题元素
 * @param {Object} studentInfo - 学生信息
 * @returns {HTMLElement} 标题元素
 */
function createStudentTitleElement(studentInfo) {
    const title = document.createElement('div');
    title.className = 'col-12 mb-3';
    title.innerHTML = `
        <div class="d-flex align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-person-video2 me-2"></i>
                ${studentInfo.姓名} (排名: ${studentInfo.排名}, 总分: ${studentInfo.总分 ?? '-'})
            </h5>
        </div>
    `;
    return title;
}

/**
 * 创建学生图表元素
 * @param {Object} charts - 图表数据
 * @param {string} studentNo - 学号
 * @param {Object} studentInfo - 学生信息
 * @returns {Array} 图表元素数组
 */
function createStudentChartElements(charts, studentNo, studentInfo) {
    const chartElements = [];

    if (charts[`radar_${studentNo}`] && charts[`bar_${studentNo}`] && charts[`table_${studentNo}`]) {
        const chartConfigs = [
            { key: `radar_${studentNo}`, title: '个人雷达图', type: 'radar', chartType: '雷达图' },
            { key: `bar_${studentNo}`, title: '个人柱状图', type: 'bar', chartType: '柱状图' },
            { key: `table_${studentNo}`, title: '个人成绩表', type: 'table', chartType: '表格图' }
        ];

        chartConfigs.forEach(config => {
            const chartCard = createChartCard(
                charts[config.key],
                config.title,
                config.type,
                {
                    examName: currentExam,
                    className: currentClass,
                    studentName: studentInfo.姓名,
                    chartType: config.chartType,
                    useServerDownload: true
                }
            );
            chartElements.push(chartCard);
        });
    }

    return chartElements;
}

/**
 * 显示单个学生的详细分析
 * @param {string} studentId - 学生ID
 */
async function showStudentAnalysis(studentId) {
    // 验证数据有效性
    if (!validateStudentAnalysisData(studentId)) {
        return;
    }

    const studentInfo = currentData.student_info[studentId];
    const grade = sessionStorage.getItem('grade');

    // 验证必要参数
    if (!validateAnalysisParams(grade)) {
        return;
    }

    showLoadingToast();

    try {
        // 请求学生详细数据
        const studentData = await requestStudentAnalysisData(grade, studentInfo.学号);

        // 显示学生分析
        displayStudentAnalysis(studentData, studentInfo);

    } catch (error) {
        showErrorToast(error.message || '图表生成失败');
    } finally {
        hideLoadingToast();
    }
}

/**
 * 验证学生分析数据
 * @param {string} studentId - 学生ID
 * @returns {boolean} 数据是否有效
 */
function validateStudentAnalysisData(studentId) {
    if (!currentData || !currentData.student_info || !currentData.student_info[studentId]) {
        showErrorToast('无法获取学生信息，请先生成班级分析。');
        return false;
    }
    return true;
}

/**
 * 验证分析参数
 * @param {string} grade - 年级
 * @returns {boolean} 参数是否有效
 */
function validateAnalysisParams(grade) {
    if (!grade || !currentExam || !currentClass) {
        showErrorToast('年级、考试或班级信息缺失');
        return false;
    }
    return true;
}

/**
 * 请求学生分析数据
 * @param {string} grade - 年级
 * @param {string} studentNo - 学号
 * @returns {Promise<Object>} 学生数据
 */
async function requestStudentAnalysisData(grade, studentNo) {
    const response = await fetch('/generate_charts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            grade: grade,
            exam: currentExam,
            class_name: currentClass,
            student_id: studentNo
        })
    });

    const data = await response.json();
    if (!data.success) {
        throw new Error(data.error || '图表生成失败');
    }

    return data;
}

/**
 * 显示学生分析结果
 * @param {Object} data - 学生数据
 * @param {Object} studentInfo - 学生信息
 */
function displayStudentAnalysis(data, studentInfo) {
    const individualCharts = document.getElementById('individual-charts');
    individualCharts.innerHTML = '';

    // 显示学生成绩卡片
    if (data.student_data) {
        displayStudentScoreCard(data, studentInfo, individualCharts);
    }

    // 显示学生图表
    displayStudentCharts(data, studentInfo, individualCharts);
}

/**
 * 显示学生成绩卡片
 * @param {Object} data - 学生数据
 * @param {Object} studentInfo - 学生信息
 * @param {HTMLElement} container - 容器元素
 */
function displayStudentScoreCard(data, studentInfo, container) {
    const scoreCard = document.createElement('div');
    scoreCard.className = 'col-12 mb-4';

    const subjects = ['语文', '数学', '英语', '物理', '化学', '生物'];
    const fullMarks = data.full_marks || { '语文': 100, '数学': 100, '英语': 100, '物理': 100, '化学': 100, '生物': 100 };

    const scoreHtml = generateStudentScoreCardHtml(data.student_data, studentInfo, subjects, fullMarks);
    scoreCard.innerHTML = scoreHtml;
    container.appendChild(scoreCard);

    // 添加编辑功能事件监听
    setupScoreEditHandlers(studentInfo.学号, data.student_data);
}

/**
 * 生成学生成绩卡片HTML
 * @param {Object} studentData - 学生成绩数据
 * @param {Object} studentInfo - 学生信息
 * @param {Array} subjects - 科目列表
 * @param {Object} fullMarks - 满分配置
 * @returns {string} HTML字符串
 */
function generateStudentScoreCardHtml(studentData, studentInfo, subjects, fullMarks) {
    const subjectScoresHtml = generateStudentSubjectScoresHtml(studentData, subjects, fullMarks);
    const totalScoreHtml = generateStudentTotalScoreHtml(studentData, studentInfo);

    return `
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0 d-flex align-items-center">
                    <i class="bi bi-person-video2 me-2"></i>
                    ${studentInfo.姓名} 各科成绩详情
                </h6>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-primary" id="editScoresBtn">
                        <i class="bi bi-pencil me-1"></i>编辑成绩
                    </button>
                    <button type="button" class="btn btn-sm btn-success d-none" id="saveScoresBtn">
                        <i class="bi bi-check me-1"></i>保存
                    </button>
                    <button type="button" class="btn btn-sm btn-secondary d-none" id="cancelEditBtn">
                        <i class="bi bi-x me-1"></i>取消
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form id="scoreEditForm">
                    <div class="row g-3">
                        ${subjectScoresHtml}
                        ${totalScoreHtml}
                    </div>
                </form>
            </div>
        </div>
    `;
}

/**
 * 生成学生各科成绩HTML
 * @param {Object} studentData - 学生成绩数据
 * @param {Array} subjects - 科目列表
 * @param {Object} fullMarks - 满分配置
 * @returns {string} HTML字符串
 */
function generateStudentSubjectScoresHtml(studentData, subjects, fullMarks) {
    return subjects.map(subject => {
        let score = studentData[subject] || 0;
        if (score < 0) score = 0;

        const maxScore = fullMarks[subject] || 100;
        if (score > maxScore) score = maxScore;

        const { scoreClass, levelLabel, percent } = calculateScoreMetrics(score, maxScore);

        return `
            <div class="col-md-4 col-sm-6">
                <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                        <span class="fw-semibold">${subject}</span><br>
                        <small class="text-muted">${levelLabel}</small>
                    </div>
                    <div class="text-end">
                        <span class="score-display fs-5 ${scoreClass}" data-subject="${subject}">${score} / ${maxScore} <span style='font-size:0.9em;'>(${percent}%)</span></span>
                        <input type="number" class="form-control score-input d-none" name="${subject}" value="${score}" min="0" max="${maxScore}" step="0.1" data-subject="${subject}">
                        <br>
                        <small class="text-muted">分</small>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

/**
 * 生成学生总分HTML
 * @param {Object} studentData - 学生成绩数据
 * @param {Object} studentInfo - 学生信息
 * @returns {string} HTML字符串
 */
function generateStudentTotalScoreHtml(studentData, studentInfo) {
    const totalScore = studentData['总分'] || 0;

    return `
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center p-3 border rounded bg-light">
                <div>
                    <span class="fw-bold fs-5">总分</span>
                    <br>
                    <small class="text-muted">排名: ${studentInfo.排名 || '未知'}</small>
                </div>
                <div class="text-end">
                    <span class="fs-4 fw-bold text-primary" id="totalScoreDisplay">${totalScore}</span>
                    <br>
                    <small class="text-muted">分</small>
                </div>
            </div>
        </div>
    `;
}

/**
 * 显示学生图表
 * @param {Object} data - 学生数据
 * @param {Object} studentInfo - 学生信息
 * @param {HTMLElement} container - 容器元素
 */
function displayStudentCharts(data, studentInfo, container) {
    const studentNo = studentInfo.学号;

    if (data.charts[`radar_${studentNo}`] && data.charts[`bar_${studentNo}`] && data.charts[`table_${studentNo}`]) {
        const chartConfigs = [
            { key: `radar_${studentNo}`, title: '个人雷达图', type: 'radar', chartType: '雷达图' },
            { key: `bar_${studentNo}`, title: '个人柱状图', type: 'bar', chartType: '柱状图' },
            { key: `table_${studentNo}`, title: '个人成绩表', type: 'table', chartType: '表格图' }
        ];

        chartConfigs.forEach(config => {
            const chartCard = createChartCard(
                data.charts[config.key],
                config.title,
                config.type,
                {
                    examName: currentExam,
                    className: currentClass,
                    studentName: studentInfo.姓名,
                    chartType: config.chartType,
                    useServerDownload: true
                }
            );
            container.appendChild(chartCard);
        });
    } else {
        container.innerHTML += `
            <div class="col-12 text-center py-4">
                <i class="bi bi-exclamation-triangle text-warning fs-3"></i>
                <p class="mt-2 text-muted">该学生暂无图表数据</p>
            </div>
        `;
    }
}

// 设置成绩编辑功能的事件处理器
function setupScoreEditHandlers(studentId, originalData) {
    const editBtn = document.getElementById('editScoresBtn');
    const saveBtn = document.getElementById('saveScoresBtn');
    const cancelBtn = document.getElementById('cancelEditBtn');
    const form = document.getElementById('scoreEditForm');
    const scoreDisplays = document.querySelectorAll('.score-display');
    const scoreInputs = document.querySelectorAll('.score-input');
    const totalScoreDisplay = document.getElementById('totalScoreDisplay');
    
    let originalScores = { ...originalData };
    
    // 编辑按钮点击事件
    editBtn.addEventListener('click', function() {
        editBtn.classList.add('d-none');
        saveBtn.classList.remove('d-none');
        cancelBtn.classList.remove('d-none');
        
        // 显示输入框，隐藏显示文本
        scoreDisplays.forEach(display => {
            display.classList.add('d-none');
        });
        scoreInputs.forEach(input => {
            input.classList.remove('d-none');
        });
        
        // 为输入框添加实时计算总分的事件
        scoreInputs.forEach(input => {
            input.addEventListener('input', calculateTotalScore);
        });
    });
    
    // 保存按钮点击事件
    saveBtn.addEventListener('click', async function() {
        const formData = new FormData(form);
        const scores = {};
        const subjects = ['语文', '数学', '英语', '物理', '化学', '生物'];
        
        subjects.forEach(subject => {
            const value = parseFloat(formData.get(subject));
            if (!isNaN(value)) {
                scores[subject] = value;
            }
        });
        
        // 计算总分
        const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
        scores['总分'] = totalScore;
        
        try {
            showLoadingToast();
            const response = await fetch('/api/update_student_score', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    grade: sessionStorage.getItem('grade'),
                    exam: currentExam,
                    class_name: currentClass,
                    student_id: studentId,
                    scores: scores
                })
            });
            
            const result = await response.json();
            if (result.success) {
                showSuccessToast('成绩更新成功');
                // 退出编辑模式
                exitEditMode();
                // 刷新该学生的数据并重新生成可视化
                await refreshStudentData(studentId);
            } else {
                showError(result.error || '成绩更新失败');
            }
        } catch (error) {
            showError('成绩更新失败，请重试');
        } finally {
            hideLoadingToast();
        }
    });
    
    // 取消按钮点击事件
    cancelBtn.addEventListener('click', function() {
        // 恢复原始数据
        scoreInputs.forEach(input => {
            const subject = input.dataset.subject;
            input.value = originalScores[subject] || 0;
        });
        exitEditMode();
    });
    
    // 退出编辑模式
    function exitEditMode() {
        editBtn.classList.remove('d-none');
        saveBtn.classList.add('d-none');
        cancelBtn.classList.add('d-none');
        
        // 隐藏输入框，显示文本
        scoreDisplays.forEach(display => {
            display.classList.remove('d-none');
        });
        scoreInputs.forEach(input => {
            input.classList.add('d-none');
        });
    }
    
    // 计算总分
    function calculateTotalScore() {
        let total = 0;
        scoreInputs.forEach(input => {
            const value = parseFloat(input.value);
            if (!isNaN(value)) {
                total += value;
            }
        });
        totalScoreDisplay.textContent = total.toFixed(1);
    }
    

}

// 完全刷新所有数据
async function refreshAllData() {
    try {
        showLoadingToast();
        
        // 1. 重新生成班级总体分析
        await generateCharts();
        
        // 2. 如果当前有选中的学生，重新显示该学生的分析
        const activeStudentItem = document.querySelector('#studentList .list-group-item.active');
        if (activeStudentItem) {
            // 从当前数据中获取学生ID
            const studentName = activeStudentItem.textContent.trim().split('(')[0].trim();
            const studentId = Object.keys(currentData.student_info).find(id => 
                currentData.student_info[id].姓名 === studentName
            );
            
            if (studentId) {
                await showStudentAnalysis(studentId);
            }
        }
        
        showSuccessToast('数据刷新完成');
    } catch (error) {
        showError('数据刷新失败，请重试');
        console.error('刷新数据失败:', error);
    } finally {
        hideLoadingToast();
    }
}

// 刷新指定学生的数据并重新生成可视化
async function refreshStudentData(studentId) {
    try {
        showLoadingToast();
        
        // 1. 重新生成班级总体分析（更新排名等信息）
        await generateCharts();
        
        // 2. 重新生成该学生的可视化
        await showStudentAnalysis(studentId);
        
        // 3. 确保学生列表中的该学生保持选中状态
        const studentInfo = currentData.student_info[studentId];
        if (studentInfo) {
            const items = document.querySelectorAll('#studentList .list-group-item');
            items.forEach(item => {
                item.classList.remove('active');
                if (item.textContent.includes(studentInfo.姓名)) {
                    item.classList.add('active');
                    item.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
        }
        
        showSuccessToast('学生数据刷新完成');
    } catch (error) {
        showError('学生数据刷新失败，请重试');
        console.error('刷新学生数据失败:', error);
    } finally {
        hideLoadingToast();
    }
}

// 更新学生列表
function updateStudentList(studentInfo) {
    const studentListContainer = document.getElementById('studentList');
    if (!studentListContainer) return;

    studentListContainer.innerHTML = '';
    
    const students = Object.entries(studentInfo).map(([studentId, info]) => ({
        id: studentId,
        name: info.姓名,
        rank: info.排名,
        studentNo: info.学号,
        total: info.总分
    })).sort((a, b) => a.rank - b.rank);

    students.forEach(student => {
        // 优化布局：badge、姓名、学号、总分间距更舒适
        const listItem = document.createElement('a');
        listItem.href = '#';
        listItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center px-3 py-2 mb-2 rounded-3 student-list-item';
        let badgeClass = '';
        // 前3名红色，4-10黄，11-20蓝，21-40粉色，其余绿色
        if (student.rank <= 3) {
            badgeClass = 'bg-danger';
        } else if (student.rank <= 10) {
            badgeClass = 'bg-warning text-dark';
        } else if (student.rank <= 20) {
            badgeClass = 'bg-primary';
        } else if (student.rank <= 40) {
            badgeClass = 'bg-pink text-white'; // 需要在css中自定义 .bg-pink
        } else {
            badgeClass = 'bg-success';
        }
        listItem.innerHTML = `
            <div class="d-flex align-items-center flex-wrap gap-2">
                <span class="badge ${badgeClass} me-3" style="width: 38px;min-width:38px;font-size:1.08em;">${student.rank}</span>
                <span class="fw-semibold me-3" style="font-size:1.08em;">${student.name}</span>
                <small class="text-muted me-3" style="font-size:0.98em;">(${student.studentNo})</small>
                <span class="text-success" style="font-size:0.98em;">总分: ${student.total ?? '-'}</span>
            </div>
            <i class="bi bi-chevron-right"></i>`;
        // 点击高亮并显示分析
        listItem.addEventListener('click', (e) => {
            e.preventDefault();
            studentListContainer.querySelectorAll('.list-group-item').forEach(item => item.classList.remove('active'));
            listItem.classList.add('active');
            showStudentAnalysis(student.id);
        });
        // hover效果
        listItem.addEventListener('mouseenter', () => {
            listItem.classList.add('shadow-sm');
        });
        listItem.addEventListener('mouseleave', () => {
            listItem.classList.remove('shadow-sm');
        });
        studentListContainer.appendChild(listItem);
    });
}

// ========================================
// 5. 搜索和筛选功能模块
// ========================================

/**
 * 设置搜索功能
 */
function setupSearch() {
    setupStudentListSearch();
    setupStudentNameSearch();
}

/**
 * 设置学生列表搜索功能
 */
function setupStudentListSearch() {
    const studentListSearch = document.getElementById('studentListSearch');
    if (!studentListSearch) return;

    studentListSearch.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        filterStudentList(searchTerm);
    });
}

/**
 * 过滤学生列表
 * @param {string} searchTerm - 搜索关键词
 */
function filterStudentList(searchTerm) {
    const items = document.querySelectorAll('#studentList .list-group-item');
    items.forEach(item => {
        const name = item.textContent.toLowerCase();
        item.style.display = name.includes(searchTerm) ? '' : 'none';
    });
}

/**
 * 设置学生姓名搜索功能
 */
function setupStudentNameSearch() {
    const searchStudentBtn = document.getElementById('searchStudentBtn');
    if (!searchStudentBtn) return;

    searchStudentBtn.addEventListener('click', handleStudentNameSearch);
}

/**
 * 处理学生姓名搜索
 */
function handleStudentNameSearch() {
    const studentNameInput = document.getElementById('studentNameInput');
    const studentName = studentNameInput.value.trim();

    // 验证输入
    if (!validateStudentNameInput(studentName)) {
        return;
    }

    // 查找学生
    const student = findStudentByName(studentName);

    if (student) {
        showStudentAnalysis(student[0]);
        highlightStudentInList(studentName);
    } else {
        showErrorToast(`未在本班找到名为 "${studentName}" 的学生`);
    }
}

/**
 * 验证学生姓名输入
 * @param {string} studentName - 学生姓名
 * @returns {boolean} 输入是否有效
 */
function validateStudentNameInput(studentName) {
    if (!studentName) {
        showErrorToast('请输入学生姓名');
        return false;
    }

    if (!currentData || !currentData.student_info) {
        showErrorToast('请先生成班级分析图表');
        return false;
    }

    return true;
}

/**
 * 根据姓名查找学生
 * @param {string} studentName - 学生姓名
 * @returns {Array|null} 学生数据数组或null
 */
function findStudentByName(studentName) {
    return Object.entries(currentData.student_info).find(([, info]) => info.姓名 === studentName);
}

/**
 * 在学生列表中高亮指定学生
 * @param {string} studentName - 学生姓名
 */
function highlightStudentInList(studentName) {
    const items = document.querySelectorAll('#studentList .list-group-item');
    items.forEach(item => {
        item.classList.remove('active');
        if (item.textContent.includes(studentName)) {
            item.classList.add('active');
            item.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    });
}


// ========================================
// 6. 数据导出功能模块
// ========================================

/**
 * 处理数据导出
 */
function handleExportData() {
    // 验证导出条件
    if (!validateExportConditions()) {
        return;
    }

    // 显示格式选择对话框
    showExportFormatDialog();
}

/**
 * 验证导出条件
 * @returns {boolean} 条件是否满足
 */
function validateExportConditions() {
    if (!currentExam || !currentClass) {
        Swal.fire({
            title: '导出失败',
            text: '请先选择考试和班级',
            icon: 'error'
        });
        return false;
    }
    return true;
}

/**
 * 显示导出格式选择对话框
 */
function showExportFormatDialog() {
    Swal.fire({
        title: '导出数据',
        text: '请选择要导出的数据格式',
        icon: 'question',
        showCancelButton: true,
        showDenyButton: true,
        confirmButtonText: 'Excel',
        denyButtonText: 'CSV',
        cancelButtonText: '取消'
    }).then((result) => {
        if (result.isConfirmed || result.isDenied) {
            const format = result.isConfirmed ? 'xlsx' : 'csv';
            executeDataExport(format);
        }
    });
}

/**
 * 执行数据导出
 * @param {string} format - 导出格式
 */
async function executeDataExport(format) {
    // 显示导出进度
    Swal.fire({
        title: '正在导出',
        text: '请稍候...',
        allowOutsideClick: false,
        didOpen: () => Swal.showLoading()
    });

    try {
        console.log('开始导出数据，参数：', {
            grade: sessionStorage.getItem('grade'),
            exam: currentExam,
            class_name: currentClass,
            format: format
        });

        const exportData = await requestDataExport(format);
        console.log('导出响应：', exportData);

        if (exportData.success) {
            Swal.fire({
                title: '导出成功',
                html: `文件已保存至：<br><b>${exportData.filepath}</b>`,
                icon: 'success'
            });
        } else {
            throw new Error(exportData.error || '导出失败');
        }
    } catch (error) {
        console.error('导出错误：', error);
        Swal.fire({
            title: '导出失败',
            text: error.message,
            icon: 'error'
        });
    }
}

/**
 * 请求数据导出
 * @param {string} format - 导出格式
 * @returns {Promise<Object>} 导出结果
 */
async function requestDataExport(format) {
    const requestData = {
        grade: sessionStorage.getItem('grade'),
        exam: currentExam,
        class_name: currentClass,
        format: format
    };

    console.log('发送导出请求：', requestData);

    const response = await fetch('/export_data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
    });

    console.log('响应状态：', response.status, response.statusText);

    if (!response.ok) {
        const errorText = await response.text();
        console.error('响应错误：', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json();
    console.log('解析后的响应：', result);
    return result;
}
