# ==================== 工具函数模块 ====================
"""
工具函数模块 - 包含各种辅助函数和工具方法
从 app.py 中提取的工具函数，用于支持主应用的各种功能
"""

import os
import re
import sys
import logging

logger = logging.getLogger(__name__)

# ==================== 常量定义 ====================
chinese_numbers = {'零': 0, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}


# ==================== 文件和路径工具 ====================
def resource_path(relative_path):
    """兼容 PyInstaller 打包后资源路径"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath("."), relative_path)


def safe_filename(name):
    """文件名安全处理 - 只允许中英文、数字、下划线"""
    return re.sub(r'[^\w\u4e00-\u9fa5-]', '_', name)


# ==================== 数据库工具函数 ====================
def get_teacher_name_by_id(cursor, teacher_id):
    """根据教师ID获取教师姓名"""
    cursor.execute("SELECT name FROM teachers WHERE id = %s", (teacher_id,))
    result = cursor.fetchone()
    return result[0] if result else None


# ==================== 中文数字转换工具 ====================
def chinese_to_num(s):
    """中文数字转阿拉伯数字 - 支持"十""十一""二十""二十一""三十二"等"""
    if s.isdigit():
        return int(s)

    result = 0
    if s == '十':
        return 10
    if s.startswith('十'):
        # "十一" ~ "十九"
        return 10 + chinese_numbers.get(s[1], 0)
    if '十' in s:
        parts = s.split('十')
        left = chinese_numbers.get(parts[0], 1)  # "二十"->2, "十"->1
        right = chinese_numbers.get(parts[1], 0) if len(parts) > 1 and parts[1] else 0
        return left * 10 + right

    # 个位数字处理
    for i, c in enumerate(s[::-1]):
        result += chinese_numbers.get(c, 0) * (10 ** i)
    return result


def get_exam_order(exam_name: str) -> tuple:
    """考试排序规则 - 返回(排序权重, 考试名称)"""
    # 期中、期末特殊处理
    if '期中' in exam_name:
        return 9998, exam_name
    if '期末' in exam_name:
        return 9999, exam_name

    # 匹配"YYYY年N月月考"
    m = re.search(r'(\d{4})年(\d{1,2})月', exam_name)
    if m:
        year = int(m.group(1))
        month = int(m.group(2))
        return year * 100 + month, exam_name

    # 匹配"第X次月考"，X为中文或阿拉伯数字
    m = re.search(r'第([一二三四五六七八九十零百千万\d]+)次月考', exam_name)
    if m:
        num = chinese_to_num(m.group(1))
        return 1000 + num, exam_name

    # 匹配"X月月考"
    m = re.search(r'(\d{1,2})月月考', exam_name)
    if m:
        num = int(m.group(1))
        return 2000 + num, exam_name

    # 匹配"X次月考"，X为中文或阿拉伯数字
    m = re.search(r'([一二三四五六七八九十零百千万\d]+)次月考', exam_name)
    if m:
        num = chinese_to_num(m.group(1))
        return 3000 + num, exam_name

    # 其他考试排最后
    return 10000, exam_name


# ==================== 学习分析工具函数 ====================
def generate_learning_advice(student_analysis, class_stats=None):
    """生成个性化学习建议"""
    advice = []
    subjects = ['语文', '数学', '英语', '物理', '化学', '生物']

    # 分析总分表现
    total_analysis = student_analysis['总分']
    advice.extend(_analyze_total_score(total_analysis))

    # 分析各科目表现
    weak_subjects, strong_subjects = _analyze_subjects(student_analysis, subjects, advice)

    # 分析排名情况
    advice.extend(_analyze_ranking(total_analysis))

    # 生成综合建议
    advice.extend(_generate_comprehensive_advice(weak_subjects, strong_subjects))

    return advice


def _analyze_total_score(total_analysis):
    """分析总分表现"""
    advice = []
    percentage = total_analysis['percentage']
    score = total_analysis['score']

    if percentage >= 85:
        advice.append({
            'type': 'strong',
            'icon': 'bi-trophy',
            'title': '总分表现优秀',
            'description': f"您的总分得分为{score}分，得分率为{percentage}%，在班级中表现优秀。",
            'suggestions': '继续保持当前的学习状态，可以尝试挑战更高难度的题目。'
        })
    elif percentage >= 70:
        advice.append({
            'type': 'neutral',
            'icon': 'bi-arrow-up-circle',
            'title': '总分表现良好',
            'description': f"您的总分得分为{score}分，得分率为{percentage}%，有提升空间。",
            'suggestions': '重点关注薄弱科目，制定针对性的学习计划。'
        })
    else:
        advice.append({
            'type': 'weak',
            'icon': 'bi-exclamation-triangle',
            'title': '总分需要提升',
            'description': f"您的总分得分为{score}分，得分率为{percentage}%，需要加强学习。",
            'suggestions': '建议制定详细的学习计划，寻求老师或同学的帮助。'
        })

    return advice


def _analyze_subjects(student_analysis, subjects, advice):
    """分析各科目表现"""
    weak_subjects = []
    strong_subjects = []

    for subject in subjects:
        analysis = student_analysis[subject]
        percentage = analysis['percentage']
        score = analysis['score']

        if percentage < 60:
            weak_subjects.append(subject)
            advice.append({
                'type': 'weak',
                'icon': 'bi-book',
                'title': f'{subject}需要加强',
                'description': f"{subject}得分{score}分，得分率{percentage}%，低于及格线。",
                'suggestions': f"建议加强{subject}基础知识学习，多做练习题，及时请教老师。"
            })
        elif percentage >= 80:
            strong_subjects.append(subject)
            advice.append({
                'type': 'strong',
                'icon': 'bi-star',
                'title': f'{subject}表现优秀',
                'description': f"{subject}得分{score}分，得分率{percentage}%，表现优秀。",
                'suggestions': f"继续保持{subject}的学习优势，可以尝试更高难度的内容。"
            })

    return weak_subjects, strong_subjects


def _analyze_ranking(total_analysis):
    """分析排名情况"""
    advice = []
    rank = total_analysis.get('rank')

    if rank and rank <= 5:
        advice.append({
            'type': 'strong',
            'icon': 'bi-award',
            'title': '班级排名优秀',
            'description': f"您在班级中排名第{rank}名，表现非常优秀。",
            'suggestions': '继续保持领先优势，可以尝试参加学科竞赛。'
        })

    return advice


def _generate_comprehensive_advice(weak_subjects, strong_subjects):
    """生成综合建议"""
    advice = []

    if weak_subjects:
        advice.append({
            'type': 'weak',
            'icon': 'bi-lightbulb',
            'title': '重点提升科目',
            'description': f"建议重点提升以下科目：{', '.join(weak_subjects)}",
            'suggestions': '制定详细的学习计划，每天安排固定时间复习这些科目。'
        })

    if strong_subjects:
        advice.append({
            'type': 'strong',
            'icon': 'bi-check-circle',
            'title': '优势科目',
            'description': f"您的优势科目包括：{', '.join(strong_subjects)}",
            'suggestions': '保持优势科目的学习状态，可以尝试更高难度的题目。'
        })

    return advice


def generate_grade_predictions(student_analysis, class_stats=None):
    """生成成绩预测"""
    predictions = []
    subjects = ['语文', '数学', '英语', '物理', '化学', '生物']

    for subject in subjects:
        analysis = student_analysis[subject]
        current_score = analysis['score']
        percentage = analysis['percentage']

        # 基于当前表现的预测逻辑
        import random

        if percentage >= 85:
            # 优秀水平，小幅提升或保持
            predicted_score = min(current_score + random.uniform(0, 5), analysis['full_mark'])
            confidence = random.uniform(85, 95)
            trend = 'up'
            trend_text = '预计小幅提升'
        elif percentage >= 70:
            # 良好水平，有提升空间
            predicted_score = current_score + random.uniform(-2, 8)
            confidence = random.uniform(70, 85)
            trend = 'up' if random.random() > 0.3 else 'stable'
            trend_text = '预计提升' if trend == 'up' else '预计稳定'
        else:
            # 需要提升，有较大提升空间
            predicted_score = current_score + random.uniform(5, 15)
            confidence = random.uniform(60, 80)
            trend = 'up'
            trend_text = '预计显著提升'

        # 确保预测分数在合理范围内
        predicted_score = max(0, min(predicted_score, analysis['full_mark']))

        predictions.append({
            'subject': subject,
            'predicted_score': round(predicted_score, 1),
            'confidence': round(confidence, 1),
            'trend': trend,
            'trend_text': trend_text,
            'factors': [
                {'name': '当前基础',
                 'impact': '优秀' if percentage >= 80 else '良好' if percentage >= 60 else '需加强'},
                {'name': '提升空间', 'impact': '较小' if percentage >= 80 else '中等' if percentage >= 60 else '较大'},
                {'name': '学习投入', 'impact': '建议增加' if percentage < 70 else '保持现状'}
            ]
        })

    return predictions


def generate_subject_analysis(student_analysis, class_stats=None):
    """生成科目详细分析"""
    analysis = {}
    subjects = ['语文', '数学', '英语', '物理', '化学', '生物']

    for subject in subjects:
        subject_data = student_analysis[subject]
        percentage = subject_data['percentage']

        # 确定表现等级
        level, level_text = _get_performance_level(percentage)

        # 生成改进建议
        improvement_suggestion = _get_improvement_suggestion(subject, percentage)

        analysis[subject] = {
            'level': level,
            'level_text': level_text,
            'percentage': percentage,
            'score': subject_data['score'],
            'class_avg': subject_data['class_avg'],
            'vs_class_avg': subject_data['vs_class_avg'],
            'rank_in_subject': subject_data.get('rank_in_subject'),
            'improvement_suggestion': improvement_suggestion,
            'strengths': get_subject_strengths(subject, subject_data),
            'weaknesses': get_subject_weaknesses(subject, subject_data)
        }

    return analysis


def _get_performance_level(percentage):
    """根据得分率确定表现等级"""
    if percentage >= 85:
        return 'excellent', '优秀'
    elif percentage >= 70:
        return 'good', '良好'
    elif percentage >= 60:
        return 'average', '中等'
    else:
        return 'weak', '需努力'


def _get_improvement_suggestion(subject, percentage):
    """生成改进建议"""
    if percentage < 60:
        return f"建议加强{subject}基础知识，多做练习题，及时请教老师。"
    elif percentage < 70:
        return f"建议增加{subject}练习时间，重点突破薄弱环节。"
    elif percentage < 80:
        return f"建议保持{subject}学习状态，适当增加难度。"
    else:
        return f"建议保持{subject}优势，尝试更高难度内容。"


def get_subject_strengths(subject, subject_data):
    """获取科目优势"""
    strengths = []
    percentage = subject_data['percentage']
    vs_class_avg = subject_data['vs_class_avg']

    if percentage >= 80:
        strengths.append(f"{subject}基础扎实")
    if vs_class_avg > 0:
        strengths.append(f"高于班级平均分{vs_class_avg}分")
    if percentage >= 70:
        strengths.append(f"得分率较高({percentage}%)")

    return strengths


def get_subject_weaknesses(subject, subject_data):
    """获取科目不足"""
    weaknesses = []
    percentage = subject_data['percentage']
    vs_class_avg = subject_data['vs_class_avg']

    if percentage < 60:
        weaknesses.append(f"{subject}基础薄弱")
    if vs_class_avg < 0:
        weaknesses.append(f"低于班级平均分{abs(vs_class_avg)}分")
    if percentage < 70:
        weaknesses.append(f"得分率偏低({percentage}%)")

    return weaknesses


# ==================== 数据库和文件操作工具函数 ====================
def delete_chart_files(grade, exam, class_name, student_id=None):
    """删除相关的图表文件"""
    import glob
    try:
        base_dir = 'data/可视化'

        # 删除班级平均分图表
        class_avg_dir = os.path.join(base_dir, grade, exam, class_name, '班级平均分')
        if os.path.exists(class_avg_dir):
            for file in glob.glob(os.path.join(class_avg_dir, '*.png')):
                try:
                    os.remove(file)
                    logger.info(f"已删除班级平均分图表: {file}")
                except Exception as e:
                    logger.warning(f"删除文件失败 {file}: {e}")

        # 如果指定了学生ID，删除该学生的图表
        if student_id:
            # 获取学生姓名
            student_name = None
            try:
                from backend.MySQL_to_pythonr import get_exam_db_connection
                with get_exam_db_connection(grade) as (con, cursor):
                    cursor.execute(f"SELECT 姓名 FROM `{exam}` WHERE 学号 = %s AND class_name = %s",
                                   (student_id, class_name))
                    result = cursor.fetchone()
                    if result:
                        student_name = result[0]
            except Exception as e:
                logger.warning(f"获取学生姓名失败: {e}")

            if student_name:
                student_dir = os.path.join(base_dir, grade, exam, class_name, student_name)
                if os.path.exists(student_dir):
                    for file in glob.glob(os.path.join(student_dir, '*.png')):
                        try:
                            os.remove(file)
                            logger.info(f"已删除学生图表: {file}")
                        except Exception as e:
                            logger.warning(f"删除文件失败 {file}: {e}")

        logger.info(f"已删除 {grade} {exam} {class_name} 的相关图表文件")

    except Exception as e:
        logger.error(f"删除图表文件失败: {str(e)}")
        # 不抛出异常，避免影响主要功能


def update_class_rankings(grade, exam, class_name, cursor):
    """更新班级排名"""
    try:
        # 获取该班级所有学生的成绩
        cursor.execute(
            f"SELECT id, 总分, 数学, 语文 FROM `{exam}` WHERE class_name = %s ORDER BY 总分 DESC, 数学 DESC, 语文 DESC",
            (class_name,))
        students = cursor.fetchall()

        # 更新排名
        for rank, student_data in enumerate(students, 1):
            student_id = student_data[0]  # 只使用student_id
            cursor.execute(f"UPDATE `{exam}` SET class_rank = %s WHERE id = %s", (rank, student_id))

        logger.info(f"已更新 {class_name} 班级排名")

    except Exception as e:
        logger.error(f"更新班级排名失败: {str(e)}")
        raise


# ==================== 数据标准化工具函数 ====================
def standardize_scores_df(df):
    """将成绩中的特殊值（如'缺考'、'--'、'absent'等）统一转为np.nan"""
    import pandas as pd
    import numpy as np

    special_values = ['缺考', '--', '-', 'absent', 'ABSENT', 'Absent', '', None]
    score_fields = ['语文', '数学', '英语', '物理', '化学', '生物', '总分']
    for field in score_fields:
        if field in df.columns:
            df[field] = df[field].apply(lambda x: np.nan if str(x).strip() in special_values else x)
    return df


def extract_courses_from_scores(grade: str) -> set:
    """从学生成绩Excel中提取所有课程名"""
    import pandas as pd

    base_dir = os.path.join('data', '各班学生成绩', grade)
    all_courses = set()
    if not os.path.exists(base_dir):
        return all_courses
    for file in os.listdir(base_dir):
        if file.endswith('.xlsx'):
            file_path = os.path.join(base_dir, file)
            try:
                excel = pd.ExcelFile(file_path)
                for sheet in excel.sheet_names:
                    df = excel.parse(sheet, nrows=1)
                    for col in df.columns:
                        # 排除非课程字段
                        if col not in ['学号', '姓名', '班级', '总分', '排名', 'grade_rank']:
                            all_courses.add(col.strip())
            except Exception as e:
                print(f"读取{file_path}失败: {e}")
    return all_courses


def generate_random_course_code(course_name: str) -> str:
    """生成随机课程编号"""
    import random
    import string

    base_code = ''.join([c for c in course_name if c.encode('utf-8').isalpha()]).upper()[:3] or 'CRS'
    random_suffix = ''.join(random.choices(string.digits, k=3))
    return f"{base_code}{random_suffix}"


# ==================== 数据库表管理工具函数 ====================
def create_leave_table_if_not_exists(grade: str):
    """为指定年级创建请假表（如不存在）"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    try:
        con = Connect(
            host=ACCOUNT_DB_CONFIG['host'],
            user=ACCOUNT_DB_CONFIG['user'],
            passwd=ACCOUNT_DB_CONFIG['passwd'],
            port=ACCOUNT_DB_CONFIG['port'],
            autocommit=True
        )
        con.select_db(grade)
        cursor = con.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leave_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id VARCHAR(50) NOT NULL,
                name VARCHAR(255) NOT NULL,
                class_name VARCHAR(50) NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                reason VARCHAR(255) NOT NULL,
                status VARCHAR(20) DEFAULT '待审批',
                create_time DATETIME DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ''')
        cursor.close()
        con.close()
    except Exception as e:
        logger.error(f"Error creating leave_records table for grade {grade}: {str(e)}")
        raise


def create_student_message_table_if_not_exists(grade: str):
    """为指定年级创建学生消息表（如不存在）"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    con = Connect(
        host=ACCOUNT_DB_CONFIG['host'],
        user=ACCOUNT_DB_CONFIG['user'],
        passwd=ACCOUNT_DB_CONFIG['passwd'],
        port=ACCOUNT_DB_CONFIG['port'],
        autocommit=True
    )
    con.select_db(grade)
    cursor = con.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS student_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id VARCHAR(50),
            name VARCHAR(255),
            class_name VARCHAR(50),
            is_anonymous BOOLEAN DEFAULT 0,
            message TEXT NOT NULL,
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ''')
    cursor.close()
    con.close()


def create_teacher_message_history_table_if_not_exists(grade: str):
    """为指定年级创建教师消息历史表（如不存在）"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    con = Connect(
        host=ACCOUNT_DB_CONFIG['host'],
        user=ACCOUNT_DB_CONFIG['user'],
        passwd=ACCOUNT_DB_CONFIG['passwd'],
        port=ACCOUNT_DB_CONFIG['port'],
        autocommit=True
    )
    con.select_db(grade)
    cursor = con.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS teacher_message_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id VARCHAR(50),
            teacher_name VARCHAR(255),
            message TEXT NOT NULL,
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ''')
    cursor.close()
    con.close()


# ==================== 成绩更新工具函数 ====================
def update_student_score(grade, exam, class_name, student_id, scores_dict, total_score):
    """
    更新指定学生的单科分数和总分。
    :param grade: 年级
    :param exam: 考试名称
    :param class_name: 班级
    :param student_id: 学号
    :param scores_dict: {科目: 分数}
    :param total_score: 总分
    """
    from backend.MySQL_to_pythonr import get_exam_db_connection
    with get_exam_db_connection(grade) as (con, cursor):
        table_name = exam
        # 构建SQL
        set_clause = ', '.join([f"`{k}`=%s" for k in scores_dict.keys()]) + ", `总分`=%s"
        values = list(scores_dict.values()) + [total_score, student_id]
        sql = f"UPDATE `{table_name}` SET {set_clause} WHERE `学号`=%s AND `class_name`=%s"
        cursor.execute(sql, values + [class_name])
        con.commit()


# ==================== 可视化生成工具函数 ====================
def generate_all_students_visualizations(exam_name: str, class_name: str) -> None:
    """为所有学生生成可视化图表"""
    from backend.MySQL_to_pythonr import get_exam_db_connection, get_student_scores_by_class

    try:
        with get_exam_db_connection(exam_name) as (con, cursor):
            results = get_student_scores_by_class(class_name, cursor)
            if not results:
                logger.error(f"No data found for class {class_name} in exam {exam_name}")
                return

            # 这里可以添加生成可视化图表的代码
            logger.info(f"Successfully processed {len(results)} students for class {class_name}")

    except Exception as e:
        logger.error(f"Error generating visualizations: {str(e)}")
        raise


# ==================== 请假管理工具函数 ====================
def add_leave_record(grade: str, student_id: str, name: str, class_name: str, start_date: str, end_date: str, reason: str):
    """添加请假记录"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    create_leave_table_if_not_exists(grade)
    con = None
    try:
        con = Connect(
            host=ACCOUNT_DB_CONFIG['host'],
            user=ACCOUNT_DB_CONFIG['user'],
            passwd=ACCOUNT_DB_CONFIG['passwd'],
            port=ACCOUNT_DB_CONFIG['port'],
            autocommit=True
        )
        con.select_db(grade)
        cursor = con.cursor()
        cursor.execute('''
            INSERT INTO leave_records (student_id, name, class_name, start_date, end_date, reason)
            VALUES (%s, %s, %s, %s, %s, %s)
        ''', (student_id, name, class_name, start_date, end_date, reason))
        con.commit()
        logger.info(f"成功添加请假记录: grade={grade}, student_id={student_id}, name={name}")
    except Exception as e:
        logger.error(f"Error adding leave record: {str(e)}")
        if con:
            con.rollback()
        raise
    finally:
        if con:
            con.close()


def get_student_leaves(grade: str, student_id: str):
    """获取学生本人请假记录"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    create_leave_table_if_not_exists(grade)
    con = None
    try:
        con = Connect(
            host=ACCOUNT_DB_CONFIG['host'],
            user=ACCOUNT_DB_CONFIG['user'],
            passwd=ACCOUNT_DB_CONFIG['passwd'],
            port=ACCOUNT_DB_CONFIG['port'],
            autocommit=True
        )
        con.select_db(grade)
        cursor = con.cursor()
        cursor.execute('''
            SELECT id, start_date, end_date, reason, status, create_time FROM leave_records WHERE student_id = %s ORDER BY create_time DESC
        ''', (student_id,))
        results = cursor.fetchall()
        return results
    except Exception as e:
        logger.error(f"Error fetching student leaves: {str(e)}")
        return []
    finally:
        if con:
            con.close()


def get_class_leaves(grade: str, class_name: str):
    """获取班级所有学生请假记录（老师用）"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    create_leave_table_if_not_exists(grade)
    con = None
    try:
        con = Connect(
            host=ACCOUNT_DB_CONFIG['host'],
            user=ACCOUNT_DB_CONFIG['user'],
            passwd=ACCOUNT_DB_CONFIG['passwd'],
            port=ACCOUNT_DB_CONFIG['port'],
            autocommit=True
        )
        con.select_db(grade)
        cursor = con.cursor()
        cursor.execute('''
            SELECT id, student_id, name, start_date, end_date, reason, status, create_time FROM leave_records WHERE class_name = %s ORDER BY create_time DESC
        ''', (class_name,))
        results = cursor.fetchall()
        return results
    except Exception as e:
        logger.error(f"Error fetching class leaves: {str(e)}")
        return []
    finally:
        if con:
            con.close()


def update_leave_status(grade: str, leave_id: int, status: str):
    """老师审批请假，支持'待审批'、'已批准'、'已拒绝'、'已销假'等状态"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    create_leave_table_if_not_exists(grade)
    con = None
    try:
        con = Connect(
            host=ACCOUNT_DB_CONFIG['host'],
            user=ACCOUNT_DB_CONFIG['user'],
            passwd=ACCOUNT_DB_CONFIG['passwd'],
            port=ACCOUNT_DB_CONFIG['port'],
            autocommit=True
        )
        con.select_db(grade)
        cursor = con.cursor()

        # 先检查请假记录是否存在
        cursor.execute('SELECT id FROM leave_records WHERE id = %s', (leave_id,))
        if not cursor.fetchone():
            raise Exception(f"请假记录不存在 (ID: {leave_id})")

        cursor.execute('''
            UPDATE leave_records SET status = %s WHERE id = %s
        ''', (status, leave_id))

        if cursor.rowcount == 0:
            raise Exception(f"更新失败，未找到ID为 {leave_id} 的请假记录")

        con.commit()
        logger.info(f"成功更新请假状态: grade={grade}, leave_id={leave_id}, status={status}")
    except Exception as e:
        logger.error(f"Error updating leave status: {str(e)}")
        if con:
            con.rollback()
        raise
    finally:
        if con:
            con.close()


def delete_leave_record(grade: str, leave_id: int):
    """删除请假记录（如需）"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    create_leave_table_if_not_exists(grade)
    con = None
    try:
        con = Connect(
            host=ACCOUNT_DB_CONFIG['host'],
            user=ACCOUNT_DB_CONFIG['user'],
            passwd=ACCOUNT_DB_CONFIG['passwd'],
            port=ACCOUNT_DB_CONFIG['port'],
            autocommit=True
        )
        con.select_db(grade)
        cursor = con.cursor()

        # 先检查请假记录是否存在
        cursor.execute('SELECT id FROM leave_records WHERE id = %s', (leave_id,))
        if not cursor.fetchone():
            raise Exception(f"请假记录不存在 (ID: {leave_id})")

        cursor.execute('''
            DELETE FROM leave_records WHERE id = %s
        ''', (leave_id,))

        if cursor.rowcount == 0:
            raise Exception(f"删除失败，未找到ID为 {leave_id} 的请假记录")

        con.commit()
        logger.info(f"成功删除请假记录: grade={grade}, leave_id={leave_id}")
    except Exception as e:
        logger.error(f"Error deleting leave record: {str(e)}")
        if con:
            con.rollback()
        raise
    finally:
        if con:
            con.close()


# ==================== 学生账户管理工具函数 ====================
def update_student_account_info(grade: str, student_db_id: int, new_username: str, new_password: str, teacher_message: str) -> bool:
    """更新指定学生的账号、密码和教师留言"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    try:
        con = Connect(
            host=ACCOUNT_DB_CONFIG['host'],
            user=ACCOUNT_DB_CONFIG['user'],
            passwd=ACCOUNT_DB_CONFIG['passwd'],
            port=ACCOUNT_DB_CONFIG['port'],
            autocommit=True
        )
        con.select_db(grade)
        cursor = con.cursor()
        cursor.execute(
            "UPDATE students SET username=%s, password=%s, teacher_message=%s WHERE id=%s",
            (new_username, new_password, teacher_message, student_db_id)
        )
        con.commit()
        cursor.close()
        con.close()
        return True
    except Exception as e:
        logger.error(f"Error updating student info: {str(e)}")
        return False


def add_student(grade: str, data: dict) -> bool:
    """添加新学生"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    try:
        cursor.execute(
            """INSERT INTO students (username, password, student_id, name, class_name, grade, phone, feedback)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)""", (
                data['username'], data['password'], data['student_id'], data['name'],
                data['class_name'], grade, data.get('phone', ''), data.get('feedback', '')
            )
        )
        con.commit()
        return True
    except Exception as e:
        logger.error(f"Error adding student: {str(e)}")
        return False
    finally:
        cursor.close()
        con.close()


def update_student(grade: str, student_id: int, data: dict) -> bool:
    """更新学生信息"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    try:
        # 如果密码为空，则不更新密码字段
        if data.get('password'):
            cursor.execute(
                """UPDATE students SET username=%s, password=%s, student_id=%s, name=%s,
                teacher_message=%s, phone=%s, feedback=%s WHERE id=%s""", (
                    data['username'], data['password'], data['student_id'], data['name'],
                    data.get('teacher_message', ''), data.get('phone', ''), data.get('feedback', ''), student_id
                )
            )
        else:
            cursor.execute(
                """UPDATE students SET username=%s, student_id=%s, name=%s,
                teacher_message=%s, phone=%s, feedback=%s WHERE id=%s""", (
                    data['username'], data['student_id'], data['name'],
                    data.get('teacher_message', ''), data.get('phone', ''), data.get('feedback', ''), student_id
                )
            )
        con.commit()
        return cursor.rowcount > 0
    except Exception as e:
        logger.error(f"Error updating student: {str(e)}")
        return False
    finally:
        cursor.close()
        con.close()


def delete_student(grade: str, student_id: int) -> bool:
    """删除学生"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    try:
        cursor.execute("DELETE FROM students WHERE id=%s", (student_id,))
        con.commit()
        return cursor.rowcount > 0
    except Exception as e:
        logger.error(f"Error deleting student: {str(e)}")
        return False
    finally:
        cursor.close()
        con.close()


# ==================== 教师管理工具函数 ====================
def add_teacher(grade: str, data: dict) -> bool:
    """添加新教师"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()
        cursor.execute('''
            INSERT INTO teachers (username, password, name, phone, grade, is_head_teacher, manage_class, subjects, is_class_teacher, certificate_level, remark)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ''', (
            data.get('username'),
            data.get('password'),
            data.get('name'),
            data.get('phone'),
            grade,
            data.get('is_head_teacher', 0),
            data.get('manage_class', '0'),
            data.get('subjects', ''),
            data.get('is_class_teacher', '0'),
            data.get('certificate_level', ''),
            data.get('remark', '')
        ))
        con.commit()
        cursor.close()
        con.close()
        return True
    except Exception as e:
        logger.error(f"添加教师失败: {str(e)}")
        return False


def update_teacher(grade: str, teacher_id: int, data: dict) -> bool:
    """更新教师信息"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()
        cursor.execute('''
            UPDATE teachers SET username=%s, password=%s, name=%s, phone=%s, is_head_teacher=%s, manage_class=%s, subjects=%s, is_class_teacher=%s, certificate_level=%s, remark=%s WHERE id=%s
        ''', (
            data.get('username'),
            data.get('password'),
            data.get('name'),
            data.get('phone'),
            data.get('is_head_teacher', 0),
            data.get('manage_class', '0'),
            data.get('subjects', ''),
            data.get('is_class_teacher', '0'),
            data.get('certificate_level', ''),
            data.get('remark', ''),
            teacher_id
        ))
        con.commit()
        cursor.close()
        con.close()
        return True
    except Exception as e:
        logger.error(f"更新教师失败: {str(e)}")
        return False


def delete_teacher(grade: str, teacher_id: int) -> bool:
    """删除教师"""
    from backend.config import ACCOUNT_DB_CONFIG
    from pymysql import Connect

    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    try:
        cursor.execute("DELETE FROM teachers WHERE id=%s", (teacher_id,))
        con.commit()
        return cursor.rowcount > 0
    except Exception as e:
        logger.error(f"Error deleting teacher: {str(e)}")
        return False
    finally:
        cursor.close()
        con.close()