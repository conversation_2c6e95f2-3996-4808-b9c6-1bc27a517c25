<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生个人中心</title>
    <link rel="stylesheet" href="/css/public.css">
    <link rel="stylesheet" href="/css/top.css">
    <link rel="stylesheet" href="/css/student.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
    .sidebar-sticky {
        position: sticky;
        top: 80px; /* 适配你的导航栏高度 */
        z-index: 100;
    }

    @media (max-width: 991.98px) {
        .sidebar-sticky {
            position: static;
            top: auto;
        }
    }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="#">
                <i class="bi bi-person-circle me-2"></i>
                <span>学生个人中心</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#"><i class="bi bi-house-door me-1"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="helpBtn"><i class="bi bi-question-circle me-1"></i> 帮助</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right me-1"></i> 退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        <!-- 欢迎信息 -->
        <div class="welcome-section text-center mb-5 animate__animated animate__fadeIn">
            <h1 class="display-5 fw-bold text-primary">学生个人中心</h1>
            <p class="lead text-muted">管理个人信息、申请请假、查看成绩分析</p>
        </div>

        <div class="row g-4">
            <!-- 左侧控制面板 -->
            <div class="col-lg-3 d-flex flex-column">
                <!-- 个人信息面板（不固定） -->
                <div class="card shadow-sm animate__animated animate__fadeInLeft mb-3">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0 d-flex align-items-center">
                            <i class="bi bi-person-badge me-2"></i> 个人信息
                        </h5>
                    </div>
                    <div class="card-body p-3">
                        <div id="studentInfo" class="text-center">
                            <div class="mb-3">
                                <i class="bi bi-person-circle text-primary" style="font-size: 3rem;"></i>
                            </div>
                            <h6 id="studentName" class="mb-1">加载中...</h6>
                            <p class="text-muted mb-2" id="studentClass">班级：加载中...</p>
                            <p class="text-muted mb-0" id="studentGrade">年级：加载中...</p>
                        </div>
                    </div>
                </div>
                <!-- sticky部分：快速操作+功能面板+退出 -->
                <div class="sidebar-sticky">
                    <!-- 快速操作面板 -->
                    <div class="card shadow-sm mb-3">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-lightning me-2"></i> 快速操作
                            </h5>
                        </div>
                        <div class="card-body p-3">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary btn-sm" id="quickLeaveBtn">
                                    <i class="bi bi-calendar-plus me-1"></i> 快速请假
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" id="refreshDataBtn">
                                    <i class="bi bi-arrow-clockwise me-1"></i> 刷新数据
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- 功能面板 -->
                    <div class="card shadow-sm mb-3">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-gear me-2"></i> 功能面板
                            </h5>
                        </div>
                        <div class="card-body p-3">
                            <!-- 学生留言子面板 -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-chat-dots me-2 text-info"></i>
                                    <span class="fw-semibold">留言部分</span>
                                </div>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-info w-100 active" id="messageTabBtn">
                                        <i class="bi bi-chat-text me-1"></i> 查看留言
                                    </button>
                                </div>
                            </div>
                            <!-- 申请请假子面板 -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-calendar-check me-2 text-success"></i>
                                    <span class="fw-semibold">申请请假</span>
                                </div>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-success w-100" id="leaveTabBtn">
                                        <i class="bi bi-plus-circle me-1"></i> 请假申请
                                    </button>
                                </div>
                            </div>
                            <!-- 成绩分析子面板 -->
                            <div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-graph-up me-2 text-warning"></i>
                                    <span class="fw-semibold">成绩分析</span>
                                </div>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-warning w-100" id="gradeTabBtn">
                                        <i class="bi bi-bar-chart me-1"></i> 成绩查看
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧主内容区 -->
            <div class="col-lg-9">
                <div id="error-container" class="alert alert-danger d-none animate__animated animate__fadeIn" role="alert"></div>
                
                <!-- 学生留言面板 -->
                <div id="messagePanel">
                    <div class="card shadow-sm animate__animated animate__fadeInRight">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-chat-dots me-2"></i> 留言区
                            </h5>
                            <button class="btn btn-sm btn-outline-primary" id="refreshMessageBtn">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- 老师留言区 -->
                            <div class="mb-4 pb-3 border-bottom border-2 border-primary-subtle">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-person-badge text-primary me-2"></i>
                                    <span class="fw-semibold">老师留言</span>
                                </div>
                                <div id="teacherMessageContent" class="ps-4">
                                    <div class="text-muted">暂无老师留言</div>
                                </div>
                            </div>
                            <!-- 学生留言区 -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-chat-left-text text-info me-2"></i>
                                    <span class="fw-semibold">学生留言</span>
                                </div>
                                <div id="messageContent" class="ps-4" style="min-height: 120px;">
                                    <div class="col-12 text-center py-4 h-100 d-flex flex-column justify-content-center align-items-center">
                                        <i class="bi bi-chat-dots text-muted fs-3"></i>
                                        <p class="mt-2 text-muted">暂无留言</p>
                                    </div>
                                </div>
                            </div>
                            <!-- 学生留言输入区 -->
                            <div class="mt-2 p-3 rounded bg-light border border-2 border-primary-subtle shadow-sm">
                                <form id="studentMessageForm" class="d-flex flex-column flex-md-row align-items-md-end gap-2">
                                    <div class="flex-grow-1 w-100 position-relative">
                                        <textarea class="form-control auto-resize" id="studentMessageInput" rows="2" maxlength="300" placeholder="写下你的留言" required style="resize: none; min-height: 38px; max-height: 180px; overflow: hidden; border-radius: 12px; background: #f8fafc; box-shadow: 0 1px 2px rgba(0,0,0,0.03); transition: box-shadow 0.2s, border-color 0.2s; border: 2px solid #e0e7ef;" oninput="autoResizeTextarea(this); updateMsgCount();"></textarea>
                                        <div class="position-absolute end-0 bottom-0 pe-2 pb-1 small text-muted" style="z-index:2; user-select:none; pointer-events:none;" id="msgCount">0/300</div>
                                    </div>
                                    <div class="d-flex flex-column align-items-start gap-2 ms-md-2">
                                        <div class="form-check mb-1">
                                            <input class="form-check-input" type="checkbox" id="anonymousCheck">
                                            <label class="form-check-label small" for="anonymousCheck">匿名留言</label>
                                        </div>
                                        <button type="submit" id="msgSubmitBtn" class="btn btn-primary btn-sm px-4 rounded-pill shadow-sm" style="white-space:nowrap;"><i class="bi bi-send"></i> 提交留言</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 请假申请面板 -->
                <div id="leavePanel" style="display:none;">
                    <div class="card shadow-sm animate__animated animate__fadeInRight">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-calendar-check me-2"></i> 请假申请
                            </h5>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-sm" id="refreshLeaveBtn">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                                <button class="btn btn-success btn-sm" id="applyLeaveBtn">
                                    <i class="bi bi-plus-circle me-1"></i> 申请请假
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-3">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover align-middle text-center">
                                    <thead class="table-light">
                                        <tr>
                                            <th>请假时间</th>
                                            <th>原因</th>
                                            <th>状态</th>
                                            <th>申请时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="leaveTableBody">
                                        <tr><td colspan="5" class="text-muted">暂无请假记录</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 成绩分析面板 -->
                <div id="gradePanel" style="display:none;">
                    <div class="card shadow-sm animate__animated animate__fadeInRight">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-graph-up me-2"></i> 成绩分析
                            </h5>
                            <button class="btn btn-sm btn-outline-primary" id="refreshGradeBtn">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- 整体成绩展示区域 -->
                            <div id="overallGradeSection" class="mb-4">
                                <div class="card overall-grade-card">
                                    <div class="card-header">
                                        <h6 class="mb-0 d-flex align-items-center">
                                            <i class="bi bi-trophy me-2"></i>整体成绩概览
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- 整体成绩统计卡片化布局 -->
                                        <div id="overallGradeStats" class="mb-4">
                                            <div class="overview-cards row g-3">
                                                <div class="col-md-3">
                                                    <div class="overall-stat-item">
                                                        <div class="stat-icon">
                                                            <i class="bi bi-graph-up text-primary"></i>
                                                        </div>
                                                        <div class="stat-content">
                                                            <div class="stat-label">总考试次数</div>
                                                            <div class="stat-value" id="totalExamCount">-</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="overall-stat-item">
                                                        <div class="stat-icon">
                                                            <i class="bi bi-star text-warning"></i>
                                                        </div>
                                                        <div class="stat-content">
                                                            <div class="stat-label">平均总分</div>
                                                            <div class="stat-value" id="overallAvgTotal">-</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="overall-stat-item">
                                                        <div class="stat-icon">
                                                            <i class="bi bi-award text-success"></i>
                                                        </div>
                                                        <div class="stat-content">
                                                            <div class="stat-label">最佳排名</div>
                                                            <div class="stat-value" id="bestRank">-</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="overall-stat-item">
                                                        <div class="stat-icon">
                                                            <i class="bi bi-trending-up text-info"></i>
                                                        </div>
                                                        <div class="stat-content">
                                                            <div class="stat-label">进步幅度</div>
                                                            <div class="stat-value" id="improvementRange">-</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 各科平均分展示 -->
                                        <div id="overallSubjectAverages" class="mb-4">
                                            <h6 class="text-primary mb-3">
                                                <i class="bi bi-bar-chart me-2"></i>各科平均分
                                            </h6>
                                            <div class="row g-2">
                                                <div class="col-md-4 col-lg-2">
                                                    <div class="subject-avg-item">
                                                        <div class="subject-name">语文</div>
                                                        <div class="subject-score" id="overallAvgChinese">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4 col-lg-2">
                                                    <div class="subject-avg-item">
                                                        <div class="subject-name">数学</div>
                                                        <div class="subject-score" id="overallAvgMath">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4 col-lg-2">
                                                    <div class="subject-avg-item">
                                                        <div class="subject-name">英语</div>
                                                        <div class="subject-score" id="overallAvgEnglish">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4 col-lg-2">
                                                    <div class="subject-avg-item">
                                                        <div class="subject-name">物理</div>
                                                        <div class="subject-score" id="overallAvgPhysics">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4 col-lg-2">
                                                    <div class="subject-avg-item">
                                                        <div class="subject-name">化学</div>
                                                        <div class="subject-score" id="overallAvgChemistry">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4 col-lg-2">
                                                    <div class="subject-avg-item">
                                                        <div class="subject-name">生物</div>
                                                        <div class="subject-score" id="overallAvgBiology">-</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 成绩趋势分析图片展示区，可用JS动态插入图片 -->
                                        <div id="gradeTrendContainer" class="mb-3">
                                            <!-- 例如：<img src="/images/高一/第一次月考/20班/吕玉梅/radar_吕玉梅.png" class="overview-chart" alt="成绩趋势图"> -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 考试选择区域 -->
                            <div class="exam-select-section">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="examSelect" class="form-label">
                                            <i class="bi bi-calendar-event me-1 text-primary"></i>选择考试
                                        </label>
                                        <select id="examSelect" class="form-select">
                                            <option value="">请选择考试</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 d-flex align-items-end">
                                        <button class="btn btn-primary" id="loadExamDataBtn" disabled>
                                            <i class="bi bi-search me-1"></i>查看成绩
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 成绩详情展示区域 -->
                            <div id="gradeDetailSection" class="mb-4" style="display:none;">
                                <div class="card grade-detail-card">
                                    <div class="card-header">
                                        <h6 class="mb-0 d-flex align-items-center">
                                            <i class="bi bi-card-list me-2"></i>成绩详情
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- 班级统计信息 -->
                                        <div class="class-stats-section mb-4">
                                            <div class="row g-3">
                                                <div class="col-md-3">
                                                    <div class="stat-card total-students">
                                                        <div class="stat-icon">
                                                            <i class="bi bi-people-fill"></i>
                                                        </div>
                                                        <div class="stat-content">
                                                            <div class="stat-value" id="totalStudents">-</div>
                                                            <div class="stat-label">班级总人数</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="stat-card pass-rate">
                                                        <div class="stat-icon">
                                                            <i class="bi bi-check-circle-fill"></i>
                                                        </div>
                                                        <div class="stat-content">
                                                            <div class="stat-value" id="passRate">-</div>
                                                            <div class="stat-label">合格率</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="stat-card highest-score">
                                                        <div class="stat-icon">
                                                            <i class="bi bi-trophy-fill"></i>
                                                        </div>
                                                        <div class="stat-content">
                                                            <div class="stat-value" id="highestScore">-</div>
                                                            <div class="stat-label">最高分</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="stat-card lowest-score">
                                                        <div class="stat-icon">
                                                            <i class="bi bi-arrow-down-circle-fill"></i>
                                                        </div>
                                                        <div class="stat-content">
                                                            <div class="stat-value" id="lowestScore">-</div>
                                                            <div class="stat-label">最低分</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 成绩对比区域 -->
                                        <div class="grade-comparison-section mb-4">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                    <div class="score-panel my-score-panel">
                                                        <div class="panel-header">
                                                            <h6 class="text-primary mb-3">
                                                                <i class="bi bi-person-check me-2"></i>我的成绩
                                                            </h6>
                                                        </div>
                                                        <div class="panel-body">
                                                <div class="row g-2">
                                                    <div class="col-6">
                                                        <div class="grade-score-item my-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">语文：</span>
                                                                <span class="score-value" id="myChinese">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="grade-score-item my-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">数学：</span>
                                                                <span class="score-value" id="myMath">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="grade-score-item my-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">英语：</span>
                                                                <span class="score-value" id="myEnglish">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="grade-score-item my-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">物理：</span>
                                                                <span class="score-value" id="myPhysics">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="grade-score-item my-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">化学：</span>
                                                                <span class="score-value" id="myChemistry">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="grade-score-item my-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">生物：</span>
                                                                <span class="score-value" id="myBiology">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="grade-total-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-bold fs-5">总分：</span>
                                                                <span class="fw-bold fs-5" id="myTotal">-</span>
                                                            </div>
                                                        </div>
                                                        <div class="grade-rank-info">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">排名：</span>
                                                                <span class="fw-semibold" id="myRank">-</span>
                                                                        </div>
                                                                    </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                    <div class="score-panel avg-score-panel">
                                                        <div class="panel-header">
                                                            <h6 class="text-success mb-3">
                                                                <i class="bi bi-graph-up me-2"></i>班级平均分
                                                            </h6>
                                                        </div>
                                                        <div class="panel-body">
                                                <div class="row g-2">
                                                    <div class="col-6">
                                                        <div class="grade-score-item avg-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">语文：</span>
                                                                <span class="score-value" id="avgChinese">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="grade-score-item avg-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">数学：</span>
                                                                <span class="score-value" id="avgMath">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="grade-score-item avg-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">英语：</span>
                                                                <span class="score-value" id="avgEnglish">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="grade-score-item avg-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">物理：</span>
                                                                <span class="score-value" id="avgPhysics">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="grade-score-item avg-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">化学：</span>
                                                                <span class="score-value" id="avgChemistry">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="grade-score-item avg-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-semibold">生物：</span>
                                                                <span class="score-value" id="avgBiology">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="grade-total-score">
                                                            <div class="d-flex justify-content-between">
                                                                <span class="fw-bold fs-5">平均总分：</span>
                                                                <span class="fw-bold fs-5" id="avgTotal">-</span>
                                                            </div>
                                                        </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 预测信息区域 -->
                                        <div id="predictionInfoSection" class="mt-4" style="display:none;">
                                            <div class="card prediction-info-card">
                                                <div class="card-header">
                                                    <h6 class="mb-0 d-flex align-items-center">
                                                        <i class="bi bi-cpu me-2"></i>智能预测信息
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div id="predictionInfoContent">
                                                        <!-- 预测信息将在这里动态加载 -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 个性化学习建议区域 -->
                                        <div id="examLearningAdviceSection" class="mt-4" style="display:none;">
                                            <div class="card learning-advice-card">
                                                <div class="card-header">
                                                    <h6 class="mb-0 d-flex align-items-center">
                                                        <i class="bi bi-lightbulb me-2"></i>个性化学习建议
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div id="examLearningAdviceContent">
                                                        <!-- 学习建议将在这里动态加载 -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 可视化图表展示区域 -->
                            <div id="chartsSection" style="display:none;">
                                <div class="card charts-section">
                                    <div class="card-header">
                                        <h6 class="mb-0 d-flex align-items-center">
                                            <i class="bi bi-graph-up me-2"></i>可视化分析
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3 text-secondary small text-center">
                                        </div>
                                        <div id="chartsContainer" class="row g-4">
                                            <!-- 图表将在这里动态加载 -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 默认状态 -->
                            <div id="gradeDefaultContent" style="min-height: 300px;">
                                <div class="col-12 text-center py-5 h-100 d-flex flex-column justify-content-center align-items-center">
                                    <i class="bi bi-graph-up text-muted fs-3"></i>
                                    <p class="mt-2 text-muted">请选择考试查看成绩分析</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 申请请假模态框 -->
    <div class="modal fade" id="leaveModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-gradient-primary text-white">
                    <h5 class="modal-title d-flex align-items-center">
                        <i class="bi bi-calendar-plus me-2"></i> 申请请假
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <form id="leaveForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="leaveStartDate" class="form-label fw-semibold">
                                    <i class="bi bi-calendar-event me-1 text-primary"></i>开始日期
                                </label>
                                <input type="date" class="form-control form-control-lg" id="leaveStartDate" required>
                                <div class="form-text">请选择请假开始日期</div>
                            </div>
                            <div class="col-md-6">
                                <label for="leaveEndDate" class="form-label fw-semibold">
                                    <i class="bi bi-calendar-check me-1 text-success"></i>结束日期
                                </label>
                                <input type="date" class="form-control form-control-lg" id="leaveEndDate" required>
                                <div class="form-text">请选择请假结束日期</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label for="leaveReason" class="form-label fw-semibold">
                                <i class="bi bi-chat-text me-1 text-info"></i>请假原因
                            </label>
                            <textarea class="form-control auto-resize" id="leaveReason" rows="3" 
                                placeholder="请详细说明请假原因..." required 
                                maxlength="200" style="resize: none; min-height: 80px; max-height: 300px; overflow: hidden;" 
                                oninput="autoResizeLeaveReason(this); updateReasonCount();"></textarea>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <div class="form-text">请详细说明请假原因，便于老师审批</div>
                                <small class="text-muted" id="reasonCount">0/200</small>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="alert alert-info d-flex align-items-center" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                <div>
                                    <strong>温馨提示：</strong>请假申请提交后需要等待老师审批，请合理安排时间。
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer border-0 pt-0">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>取消
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg px-4">
                                <i class="bi bi-send me-1"></i>提交申请
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-question-circle me-2"></i>使用帮助</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">基本操作</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="bi bi-1-circle me-2"></i>查看教师留言</li>
                                <li class="mb-2"><i class="bi bi-2-circle me-2"></i>申请请假</li>
                                <li class="mb-2"><i class="bi bi-3-circle me-2"></i>查看成绩分析</li>
                                <li class="mb-2"><i class="bi bi-4-circle me-2"></i>管理个人信息</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">功能说明</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>支持请假申请和状态查看</li>
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>查看教师留言和反馈</li>
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>成绩分析和图表展示</li>
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>个人信息管理</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast提示 -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1055;">
        <div id="leaveToast" class="toast align-items-center text-bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-check-circle me-2"></i>请假申请提交成功！
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
        
        <div id="cancelLeaveToast" class="toast align-items-center text-bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-arrow-repeat me-2"></i>销假操作成功！
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
        
        <div id="errorToast" class="toast align-items-center text-bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-exclamation-triangle me-2"></i>操作失败，请重试！
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
        
        <div id="loadingToast" class="toast align-items-center text-bg-primary border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span>正在加载数据...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 销假确认模态框 -->
    <div class="modal fade" id="cancelLeaveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-gradient-info text-white">
                    <h5 class="modal-title d-flex align-items-center">
                        <i class="bi bi-arrow-repeat me-2"></i>确认销假
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="bi bi-question-circle text-info" style="font-size: 3rem;"></i>
                    </div>
                    <p class="text-center mb-0">您确定要对这条请假记录进行销假操作吗？</p>
                    <div class="alert alert-warning mt-3" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>注意：</strong>销假操作不可撤销，请确认请假已结束。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-info" id="confirmCancelLeave">
                        <i class="bi bi-check-circle me-1"></i>确认销假
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script src="/js/public.js"></script>
    <script src="/js/top.js"></script>
    <script src="/js/student.js"></script>
    <script>
    function autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = (textarea.scrollHeight) + 'px';
    }
    function resetTextareaHeight() {
        var ta = document.getElementById('studentMessageInput');
        if (ta) {
            ta.style.height = '';
        }
    }
    function updateMsgCount() {
        var ta = document.getElementById('studentMessageInput');
        var count = ta.value.length;
        var max = ta.getAttribute('maxlength') || 300;
        var counter = document.getElementById('msgCount');
        var btn = document.getElementById('msgSubmitBtn');
        counter.textContent = count + '/' + max;
        if (count > max) {
            counter.classList.add('text-danger');
            btn.disabled = true;
        } else {
            counter.classList.remove('text-danger');
            btn.disabled = (count === 0);
        }
    }
    function autoResizeLeaveReason(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = (textarea.scrollHeight) + 'px';
    }
    function updateReasonCount() {
        var ta = document.getElementById('leaveReason');
        var count = ta.value.length;
        var max = ta.getAttribute('maxlength') || 200;
        var counter = document.getElementById('reasonCount');
        var btn = document.getElementById('msgSubmitBtn');
        counter.textContent = count + '/' + max;
        if (count > max) {
            counter.classList.add('text-danger');
            btn.disabled = true;
        } else {
            counter.classList.remove('text-danger');
            btn.disabled = (count === 0);
        }
    }
    document.addEventListener('DOMContentLoaded', function() {
        var ta = document.getElementById('studentMessageInput');
        if (ta) {
            autoResizeTextarea(ta);
            updateMsgCount();
            ta.addEventListener('focus', function() {
                ta.style.borderColor = '#409eff';
                ta.style.boxShadow = '0 0 0 2px #b3d8ff';
            });
            ta.addEventListener('blur', function() {
                ta.style.borderColor = '#e0e7ef';
                ta.style.boxShadow = '0 1px 2px rgba(0,0,0,0.03)';
                resetTextareaHeight();
            });
        }
        var form = document.getElementById('studentMessageForm');
        if (form) {
            form.addEventListener('submit', function() {
                setTimeout(function() {
                    resetTextareaHeight();
                }, 100);
            });
        }
    });
    </script>
</body>
</html>
