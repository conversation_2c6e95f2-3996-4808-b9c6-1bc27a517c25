/* =====================
   教师分析专用样式（teacher_analyze.css）
   ===================== */

/* ========== 1. 通用卡片样式 ========== */
/* 基础卡片样式 */
.card {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 1rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid rgba(0,0,0,.05);
    padding: 0.75rem 1rem;
}

.card-title {
    font-size: 1.1rem;
    margin-bottom: 0;
    color: var(--dark-color);
    font-weight: 600;
}

/* ========== 2. 图表容器 ========== */
#chartsContainer {
    min-height: 300px;
}

#chartsContainer .card {
    height: 100%;
    margin: 0;
}

#chartsContainer .card:hover {
    transform: translateY(-5px);
}

#chartsContainer img {
    width: 100%;
    height: 280px;
    object-fit: contain;
    border-radius: 0.5rem;
}

#chartsContainer .btn-outline-primary {
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
}

#chartsContainer h4 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0;
}

/* ========== 3. 徽章和标签系统 ========== */
/* 基础徽章样式 */
.badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.badge.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #729fdf 100%) !important;
    color: white;
}

/* 学生分类标签 */
.student-category {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.student-category.top {
    background-color: #d4edda;
    color: #155724;
}

.student-category.middle {
    background-color: #fff3cd;
    color: #856404;
}

.student-category.bottom {
    background-color: #f8d7da;
    color: #721c24;
}

.student-category.improving {
    background-color: #b5ebdb;
    color: #7acedb;
}

/* ========== 4. 图表和分析区域 ========== */
/* 图表标题样式 */
.chart-title {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
}

/* 个人分析图表区域 */
#individual-charts {
    min-height: 200px;
}

#individual-charts .row {
    align-items: stretch;
}

#individual-charts .col-md-4 {
    display: flex;
    flex-direction: column;
}

#individual-charts .card {
    flex-grow: 1;
}

#individual-charts .card-body {
    padding: 1rem;
}

#individual-charts .card-title {
    font-size: 1rem;
    font-weight: 500;
    color: var(--dark-color);
}

#individual-charts img {
    width: 100%;
    height: auto;
    object-fit: contain;
    border-radius: 0.375rem;
}

#individual-charts h4 {
    font-size: 1.25rem;
}

#individual-charts h5 {
    font-weight: 600;
}

#individual-charts .badge {
    font-size: 1rem;
}

#individual-charts .badge.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #a2c3f1 100%) !important;
}

/* ========== 5. 班级统计卡片 ========== */

/* 班级统计卡片 */
.class-stats-cards .stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    margin-bottom: 0.75rem;
    border-left: 4px solid #dee2e6;
    transition: all 0.3s ease;
}

.class-stats-cards .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-card .stat-label {
    font-size: 0.95rem;
    color: #6c757d;
    font-weight: 500;
}

.stat-card .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-top: 0.25rem;
    color: var(--dark-color);
}

.stat-blue {
    border-left: 4px solid #007bff;
}

.stat-blue .stat-value {
    color: #007bff;
}

.stat-green {
    border-left: 4px solid #28a745;
}

.stat-green .stat-value {
    color: #28a745;
}

/* ========== 6. 科目平均分标签 ========== */
/* 科目平均分列表容器 */
.subject-avg-list {
    margin-top: 1rem;
    font-size: 1rem;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.75rem 1.25rem;
}

/* 科目平均分标题 */
.subject-avg-label {
    font-weight: 600;
    color: #495057;
    margin-right: 1rem;
    flex-shrink: 0;
    font-size: 1.1rem;
    letter-spacing: 0.05em;
}

/* 科目平均分标签基础样式 */
.subject-avg-tag {
    display: flex;
    align-items: center;
    min-width: 130px;
    padding: 0.5rem 1.25rem;
    border-radius: 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid transparent;
    background: #f8f9fa;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
    overflow: hidden;
}

/* 科目标签悬停效果 */
.subject-avg-tag:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}
/* 科目名称样式 */
.subject-avg-tag .subject-name {
    font-weight: 600;
    margin-right: 0.5rem;
    letter-spacing: 0.03em;
}

/* 科目分数样式 */
.subject-avg-tag .subject-score {
    font-weight: 700;
    font-size: 1.1rem;
    margin-left: 0.25rem;
}

/* 分数等级标签样式 */
.subject-avg-tag .score-level {
    margin-left: 0.75rem;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.2rem 0.6rem;
    border-radius: 1rem;
    background: rgba(0,0,0,0.1);
    color: #6c757d;
    letter-spacing: 0.02em;
}
/* 高分科目标签样式 */
.subject-avg-tag.high {
    background: linear-gradient(90deg, #eafbe7 60%, #d4f5e9 100%);
    color: #28a745;
    border: 1.5px solid #28a745;
}

.subject-avg-tag.high .score-level {
    background: #d4f5e9;
    color: #28a745;
}

/* 中等分数科目标签样式 */
.subject-avg-tag.mid {
    background: linear-gradient(90deg, #fff8e1 60%, #ffe9b3 100%);
    color: #ff9800;
    border: 1.5px solid #ff9800;
}

.subject-avg-tag.mid .score-level {
    background: #ffe9b3;
    color: #ff9800;
}

/* 低分和不及格科目标签样式 */
.subject-avg-tag.low,
.subject-avg-tag.fail {
    background: linear-gradient(90deg, #fdeaea 60%, #ffd6d6 100%);
    color: #dc3545;
    border: 1.5px solid #dc3545;
}

.subject-avg-tag.low .score-level,
.subject-avg-tag.fail .score-level {
    background: #ffd6d6;
    color: #dc3545;
}
/* 科目标签图标样式 */
.subject-avg-tag .icon {
    margin-right: 0.5rem;
    font-size: 1.1rem;
    opacity: 0.7;
}

/* ========== 7. 班级平均分展示样式 ========== */
/* 班级平均分现在使用与个人成绩详情完全相同的卡片样式布局 */
/* 注意：班级平均分展示已调整为使用 .row .col-md-4 .border.rounded 的卡片布局 */

/* ========== 8. 学生个人成绩分析 ========== */
/* 学生个人成绩分析-各科成绩卡片样式 */
.personal-score-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1em 1.2em;
  margin-bottom: 1em;
}
.personal-score-tag {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 120px;
  padding: 0.7em 1.2em 0.7em 1.2em;
  border-radius: 1.5em;
  font-size: 1.08em;
  font-weight: 500;
  box-shadow: 0 2px 12px rgba(0,0,0,0.09);
  background: #f6f6f6;
  position: relative;
  margin-bottom: 0.2em;
  border: 1.5px solid #eee;
  transition: box-shadow 0.2s, background 0.2s;
}
.personal-score-tag.high { background: linear-gradient(90deg, #eafbe7 60%, #d4f5e9 100%); color: #28a745; border-color: #28a745; }
.personal-score-tag.mid { background: linear-gradient(90deg, #fff8e1 60%, #ffe9b3 100%); color: #ff9800; border-color: #ff9800; }
.personal-score-tag.low, .personal-score-tag.fail { background: linear-gradient(90deg, #fdeaea 60%, #ffd6d6 100%); color: #dc3545; border-color: #dc3545; }
.personal-score-tag .score-label { font-size: 0.85em; font-weight: 600; margin-top: 0.2em; border-radius: 1em; padding: 0.1em 0.7em; }
.personal-score-tag input[type='number'] {
  width: 70px;
  margin-left: 0.5em;
  border-radius: 0.7em;
  border: 1px solid #ccc;
  padding: 0.1em 0.5em;
  font-size: 1em;
  color: inherit;
  background: #fff;
}

/* ========== 7. 学生列表样式 ========== */
#studentList .student-list-item {
    border: 1.5px solid #f0f0f0;
    background: #fff;
    color: #222;
    border-radius: 10px;
    transition: all 0.3s ease;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

#studentList .student-list-item:not(.active):hover {
    background: #f5faff;
    box-shadow: 0 2px 8px rgba(51,153,255,0.08);
    transform: translateY(-1px);
}

#studentList .student-list-item.active {
    background: #e6f0ff !important;
    border: 2px solid #3399ff !important;
    color: #1976d2 !important;
    box-shadow: 0 4px 12px rgba(51,153,255,0.15);
    border-radius: 14px !important;
    font-weight: 600;
    transform: translateY(-2px);
}

#studentList .student-list-item.active .badge {
    background: #3399ff !important;
    color: #fff !important;
}

#studentList .student-list-item.active .text-success {
    color: #1976d2 !important;
}

/* ========== 8. 表单输入框 ========== */
/* 数字输入框样式 */
input[type=number].score-input::-webkit-inner-spin-button,
input[type=number].score-input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
}

input[type=number].score-input {
    -moz-appearance: textfield;
    appearance: textfield;
}

/* ========== 9. 响应式设计 ========== */
/* 平板设备 (991.98px 及以下) */
@media (max-width: 991.98px) {
    /* 科目平均分标签 */
    .subject-avg-list {
        gap: 0.5rem 0.75rem;
    }

    .subject-avg-tag {
        min-width: 90px;
        font-size: 1rem;
        padding: 0.4rem 1rem 0.4rem 0.8rem;
    }
}

/* 手机设备 (767.98px 及以下) */
@media (max-width: 767.98px) {
    /* 个人分析图表 */
    #individual-charts .col-md-4 {
        margin-bottom: 1rem;
    }

    #individual-charts .card {
        margin-bottom: 1rem;
    }

    #individual-charts h4 {
        font-size: 1.1rem;
    }

    #individual-charts .badge {
        font-size: 0.9rem;
    }

    /* 学生列表 */
    #studentList .list-group-item {
        padding: 0.5rem 0.7rem;
        font-size: 0.98rem;
        flex-direction: column;
        align-items: flex-start !important;
    }

    #studentList .badge {
        font-size: 0.85em;
        margin-bottom: 0.2rem;
    }

    #studentList .list-group-item > div {
        flex-direction: column;
        align-items: flex-start;
    }

    #studentList .student-list-item.active {
        border-radius: 10px !important;
        font-size: 1em;
    }

    /* 个人成绩列表 */
    .personal-score-list {
        flex-direction: column;
        gap: 0.5rem 0;
    }

    .personal-score-tag {
        width: 100%;
        min-width: 0;
    }

    /* 班级平均分展示（现在使用卡片布局，无需特殊样式） */
    /* 注意：班级平均分现在使用与个人成绩详情相同的响应式卡片布局 */

    /* 科目平均分标签 */
    .subject-avg-list {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem 0;
    }

    .subject-avg-tag {
        width: 100%;
        min-width: 0;
        font-size: 0.98rem;
        margin-bottom: 0.5rem;
        padding: 0.5rem 0.75rem;
        justify-content: space-between;
    }

    /* 图表容器 */
    #chartsContainer img {
        height: 200px;
    }

    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 0.75rem;
    }
}
