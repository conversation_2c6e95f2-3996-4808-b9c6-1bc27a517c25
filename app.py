# ==================== 导入模块 ====================
import logging
import os
import shutil

import matplotlib.pyplot as plt
import pandas as pd
from flask import Flask, request, jsonify, send_from_directory
from flask_caching import Cache
from flask_cors import CORS
from pymysql import Connect

# 后端模块导入
from backend.config import EXAM_DB_CONFIG, ACCOUNT_DB_CONFIG, DATA_FOLDER, IMAGES_FOLDER, DOWNLOAD_FOLDER
from backend.excel_to_mysql import get_class_list_by_grade, get_exam_list_by_grade

# 从新的集中数据库操作模块导入
from backend.MySQL_to_pythonr import (
    get_grade_list, get_student_scores, get_courses_list,
    verify_teacher_login, verify_student_login, get_teacher_info, get_student_account_info,
    get_exam_db_connection as get_db_connection, get_full_marks, get_class_list_from_exam,
    add_student_message, get_student_messages, delete_student_message,
    add_teacher_message_history, get_teacher_message_history, delete_teacher_message_history,
    get_class_messages, verify_admin_login
)

# 从means.py导入工具函数
from backend.means import (
    add_leave_record, get_student_leaves, get_class_leaves, update_leave_status, delete_leave_record,
    update_student_account_info, add_student, update_student, delete_student
)
from backend.visualization import (
    create_radar_chart, create_bar_chart, create_table_chart,
    create_class_table_chart, get_save_dir
)

# ==================== 日志配置 ====================
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# ==================== 工具函数导入 ====================
from backend.means import (
    resource_path, get_teacher_name_by_id, safe_filename,
    generate_learning_advice, generate_grade_predictions, generate_subject_analysis,
    delete_chart_files, update_class_rankings
)


# ==================== Flask应用初始化 ====================
app = Flask(
    __name__,
    static_folder=resource_path('static'),
    static_url_path='/'
)
CORS(app)  # 启用CORS支持

# 应用配置
app.config.update(
    DATA_FOLDER=DATA_FOLDER,
    IMAGES_FOLDER='data/可视化',
    DOWNLOAD_FOLDER=DOWNLOAD_FOLDER,
    CACHE_TYPE='SimpleCache',
    CACHE_DEFAULT_TIMEOUT=300,
    CACHE_THRESHOLD=1000
)

# 初始化缓存
cache = Cache(app)

# 确保必要目录存在
os.makedirs(app.config['IMAGES_FOLDER'], exist_ok=True)
os.makedirs(app.config['DOWNLOAD_FOLDER'], exist_ok=True)

# matplotlib中文字体配置
plt.rcParams.update({
    'font.sans-serif': ['SimHei'],
    'axes.unicode_minus': False,
    'figure.dpi': 300,
    'savefig.dpi': 300
})




# ==================== 基础路由 ====================
@app.route('/')
def index():
    """首页路由"""
    return send_from_directory(os.path.join(app.static_folder, 'html'), 'log.html')


@app.route('/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory(app.static_folder, filename)


@app.route('/images/<path:filename>')
def serve_image(filename):
    """图片文件服务 - 在data/可视化/目录下查找图片文件，支持多级子目录"""
    try:
        filename = filename.replace('..', '').replace('\\', '/').replace('..', '')
        base_dir = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'data', '可视化')
        return send_from_directory(base_dir, filename)
    except Exception as e:
        logger.error(f"Error serving image {filename}: {str(e)}")
        return jsonify({'error': '图片不存在'}), 404


# ==================== 年级和考试相关API ====================
@cache.memoize(timeout=300)
@app.route('/api/exams', methods=['GET'])
def get_exams():
    """获取年级列表"""
    try:
        logger.debug("开始获取年级列表")
        grades = get_grade_list()
        if not grades:
            logger.warning("未找到任何年级数据")
            return jsonify({'error': '未找到任何年级数据'}), 404
        return jsonify(grades)
    except Exception as e:
        logger.error(f"获取年级列表时发生错误: {str(e)}")
        return jsonify({'error': '获取年级列表失败，请检查数据库连接'}), 500


# ==================== 班级管理API ====================
@app.route('/api/classes', methods=['GET', 'POST'])
def api_classes():
    """班级管理 - 获取班级列表或添加新班级"""
    if request.method == 'GET':
        return _get_classes()
    elif request.method == 'POST':
        return _add_class()


def _get_classes():
    """获取班级列表"""
    grade = request.args.get('grade')
    class_type_filter = request.args.get('class_type')  # 新增班级类型筛选参数

    if not grade:
        return jsonify({'success': False, 'error': '缺少年级参数'}), 400

    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()

        # 构建SQL查询，支持按班级类型筛选
        if class_type_filter:
            cursor.execute('''
                SELECT id, class_name, head_teacher, class_size, class_type, remark
                FROM class
                WHERE class_type = %s
                ORDER BY class_name
            ''', (class_type_filter,))
        else:
            cursor.execute('''
                SELECT id, class_name, head_teacher, class_size, class_type, remark
                FROM class ORDER BY class_name
            ''')

        classes = []
        for row in cursor.fetchall():
            classes.append({
                'id': row[0],
                'class_name': row[1],
                'head_teacher': row[2] or '',
                'class_size': row[3] or 0,
                'class_type': row[4] or '普通班级',
                'remark': row[5] or ''
            })

        cursor.close()
        con.close()
        return jsonify({'success': True, 'classes': classes})

    except Exception as e:
        if 'cursor' in locals():
            cursor.close()
        if 'con' in locals():
            con.close()
        return jsonify({'success': False, 'error': f'获取班级列表失败: {str(e)}'}), 500


def _add_class():
    """添加新班级"""
    data = request.get_json()
    grade = data.get('grade')
    class_name = data.get('class_name')
    head_teacher = data.get('head_teacher', '')
    class_size = data.get('class_size', 0)
    remark = data.get('remark', '')

    if not grade or not class_name:
        return jsonify({'success': False, 'error': '年级和班级名称不能为空'}), 400

    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()

        # 检查班级是否已存在
        cursor.execute('SELECT id FROM class WHERE class_name = %s', (class_name,))
        if cursor.fetchone():
            cursor.close()
            con.close()
            return jsonify({'success': False, 'error': '班级已存在'}), 400

        # 添加新班级
        cursor.execute('''
            INSERT INTO class (class_name, grade, head_teacher, class_size, remark, create_time, class_type)
            VALUES (%s, %s, %s, %s, %s, NOW(), %s)
        ''', (class_name, grade, head_teacher, class_size, remark, '普通班级'))

        con.commit()
        cursor.close()
        con.close()
        return jsonify({'success': True, 'message': '班级添加成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': f'添加班级失败: {str(e)}'}), 500


@app.route('/api/class-list', methods=['GET'])
def get_class_list_api():
    """获取指定年级下所有班级 - 优先查class表，备选students表"""
    grade = request.args.get('grade')
    if not grade or grade == 'undefined':
        return jsonify({'error': '缺少年级参数'}), 400

    try:
        # 优先查class表，返回 class_name 和 head_teacher
        try:
            with get_db_connection() as (_, cursor):
                cursor.execute("USE `{}`".format(grade))
                cursor.execute("SELECT class_name, head_teacher FROM class ORDER BY class_name")
                results = cursor.fetchall()
                class_list = [
                    {'class_name': row[0], 'head_teacher': row[1] or ''} for row in results
                ]
        except Exception:
            # 若class表不存在，则查students表，head_teacher 置空
            with get_db_connection() as (_, cursor):
                cursor.execute("USE `{}`".format(grade))
                cursor.execute("SELECT DISTINCT class_name FROM students ORDER BY class_name")
                results = cursor.fetchall()
                class_list = [
                    {'class_name': row[0], 'head_teacher': ''} for row in results
                ]

        if not class_list:
            return jsonify({'error': '未找到任何班级数据'}), 404
        return jsonify({'classes': class_list})

    except Exception as e:
        logger.error(f"获取班级列表失败: {str(e)}")
        return jsonify({'error': '获取班级列表失败'}), 500


@app.route('/api/account-class-list', methods=['GET'])
def api_account_class_list():
    """获取指定年级的班级列表 - 账户数据库，管理员端用"""
    grade = request.args.get('grade')
    if not grade:
        return jsonify({'success': False, 'error': '缺少年级参数'}), 400

    try:
        # 优先查class表
        try:
            with get_db_connection() as (_, cursor):
                cursor.execute("SELECT class_name FROM class WHERE grade=%s ORDER BY class_name", (grade,))
                results = cursor.fetchall()
                class_list = [row[0] for row in results]
        except Exception:
            # 若class表无grade字段或不存在，则查students表
            with get_db_connection() as (_, cursor):
                cursor.execute("SELECT DISTINCT class_name FROM students WHERE grade=%s ORDER BY class_name", (grade,))
                results = cursor.fetchall()
                class_list = [row[0] for row in results]

        return jsonify({'success': True, 'classes': class_list})

    except Exception as e:
        logger.error(f"获取班级列表失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取班级列表失败'}), 500


@app.route('/api/class-types', methods=['GET'])
def api_class_types():
    """获取数据库中所有的班级类型"""
    grade = request.args.get('grade')
    if not grade:
        return jsonify({'success': False, 'error': '缺少年级参数'}), 400

    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()

        # 先查看表结构
        cursor.execute('DESCRIBE class')
        table_structure = cursor.fetchall()
        logger.info(f"class表结构: {table_structure}")

        # 查询所有班级数据，包括class_type字段
        cursor.execute('SELECT id, class_name, class_type FROM class')
        all_classes = cursor.fetchall()
        logger.info(f"所有班级数据: {all_classes}")

        # 查询该年级下所有不同的班级类型
        cursor.execute('''
            SELECT DISTINCT class_type
            FROM class
            WHERE class_type IS NOT NULL AND class_type != ''
            ORDER BY class_type
        ''')

        results = cursor.fetchall()
        logger.info(f"班级类型查询结果: {results}")
        class_types = [row[0].strip() for row in results if row[0] and row[0].strip()]
        logger.info(f"处理后的班级类型: {class_types}")

        # 如果没有找到任何班级类型，返回默认类型
        if not class_types:
            class_types = ['普通班级']

        cursor.close()
        con.close()

        return jsonify({'success': True, 'class_types': class_types})

    except Exception as e:
        logger.error(f"获取班级类型失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取班级类型失败'}), 500


# noinspection PyTypeChecker
@app.route('/generate_charts', methods=['POST'])
def generate_charts():
    """生成图表（适配新结构）"""
    try:
        data = request.get_json()
        logger.debug(f"接收到的数据: {data}")

        grade = data.get('grade')
        exam_name = data.get('exam') or data.get('exam_name')  # 支持两种参数名
        class_name = data.get('class_name')
        student_id = data.get('student_id')  # 新的参数名
        student_name = data.get('student_name')  # 支持学生姓名参数

        logger.debug(f"解析后的参数: grade={grade}, exam_name={exam_name}, class_name={class_name}")

        if not grade or not exam_name or not class_name:
            return jsonify({'error': '缺少年级、考试或班级参数'}), 400

        # 获取学生成绩数据
        results = get_student_scores(grade, exam_name, class_name)
        if not results:
            return jsonify({'error': '未找到班级数据'}), 404

        # 精确选取字段，避免类型转换错误
        df = pd.DataFrame(
            [
                (
                    row[11],  # class_rank
                    row[2],  # student_id
                    row[3],  # name
                    row[4],  # 语文
                    row[5],  # 数学
                    row[6],  # 英语
                    row[7],  # 物理
                    row[8],  # 化学
                    row[9],  # 生物
                    row[10],  # 总分
                )
                for row in results
            ],
            columns=['排名', '学号', '姓名', '语文', '数学', '英语', '物理', '化学', '生物', '总分']
        )

        # 从数据库获取科目列表和满分数据
        from backend.MySQL_to_pythonr import get_full_marks, get_courses_list
        full_marks = get_full_marks(exam_name, grade)
        subjects = get_courses_list(grade)

        # 确保只使用DataFrame中存在的科目
        available_subjects = [subj for subj in subjects if subj in df.columns]

        total_full_mark = sum(full_marks.values())
        pass_line = total_full_mark * 0.6

        # 计算班级平均分和统计信息
        class_averages = {subj: round(float(df[subj].mean()), 1) for subj in available_subjects}
        class_averages['总分'] = round(float(df['总分'].mean()), 1)

        # 计算班级统计信息
        class_stats = {
            'total_students': len(df),
            'highest_score': float(df['总分'].astype(float).max()),
            'lowest_score': float(df['总分'].astype(float).min()),
            'score_std': round(df['总分'].astype(float).std(), 1),
            'pass_rate': round(len(df[df['总分'] >= pass_line]) / len(df) * 100, 1) if len(df) > 0 else 0
        }

        # 如果指定了学生ID，生成该学生的图表
        if student_id:
            logger.debug(
                f"检查 student_id: {student_id}, 类型: {type(student_id)}, isdigit(): {str(student_id).isdigit()}")
            # 先检查是否在数据库的学号列表中
            student_id_str = str(student_id)
            if student_id_str not in df['学号'].astype(str).tolist():
                logger.debug(f"student_id {student_id} 不在学号列表中，尝试通过账号查找")
                student_info = get_student_account_info(grade, student_id)
                logger.debug(f"get_student_account_info 结果: {student_info}")
                if student_info and 'student_id' in student_info:
                    student_id = str(student_info['student_id'])
                    logger.debug(f"找到对应的学号: {student_id}")
                else:
                    logger.warning(f"未找到账号 {student_id} 对应的学生信息")
            logger.debug(f"最终 student_id: {student_id}, df['学号']: {df['学号'].tolist()}")
            # 优先用学号查找，如果找不到再用姓名查找
            student_row = df[df['学号'].astype(str) == str(student_id)]
            if student_row.empty and student_name:
                # 如果学号找不到，尝试用姓名查找
                student_row = df[df['姓名'] == student_name]
            if student_row.empty:
                return jsonify({'error': '未找到该学生'}), 404

            student_row = student_row.iloc[0]
            student_name = student_row['姓名']
            student_rank = int(student_row['排名']) if pd.notna(student_row['排名']) else None

            # 构建学生数据
            student_data = {
                '语文': float(student_row['语文']),
                '数学': float(student_row['数学']),
                '英语': float(student_row['英语']),
                '物理': float(student_row['物理']),
                '化学': float(student_row['化学']),
                '生物': float(student_row['生物']),
                '总分': float(student_row['总分']),
                '学号': str(student_row['学号'])  # 新增，确保前端能直接用学号查找图表
            }

            # 获取成绩预测
            from backend.moble import predict_student_performance
            try:
                prediction_result = predict_student_performance(grade, student_id, class_name, exam_name)
                predicted_scores = prediction_result.get('predicted_scores', {})
                prediction_confidence = prediction_result.get('confidence', 0.5)
                prediction_type = prediction_result.get('prediction_type', 'unknown')
                prediction_factors = prediction_result.get('factors', {})
            except Exception as e:
                logger.warning(f"预测模型调用失败: {str(e)}")
                # 使用备用预测（原有的得分率计算）
                predicted_scores = {}
                for subject in subjects:
                    predicted_scores[subject] = round(float(student_data[subject]) / float(full_marks[subject]) * 100, 1)
                predicted_scores['总分'] = round(float(student_data['总分']) / float(sum(full_marks.values())) * 100, 1)
                prediction_confidence = 0.5
                prediction_type = 'fallback_percentage'
                prediction_factors = {'error': str(e)}

            # 计算学生成绩分析
            student_analysis = {}
            for subject in subjects:
                student_score = student_data[subject]
                class_avg = class_averages[subject]
                full_mark = full_marks[subject]
                predicted_score = predicted_scores.get(subject, student_score)

                # 计算各科目分析，将percentage替换为predicted_score
                student_analysis[subject] = {
                    'score': student_score,
                    'class_avg': class_avg,
                    'full_mark': full_mark,
                    'percentage': predicted_score,  # 使用预测分数替代得分率
                    'vs_class_avg': round(float(student_score) - float(class_avg), 1),
                    'vs_class_avg_percent': round((float(student_score) - float(class_avg)) / float(class_avg) * 100,
                                                  1) if class_avg > 0 else 0,
                    'rank_in_subject': int(df[df[subject].astype(float) > float(student_score)].shape[
                                               0] + 1) if student_score is not None else None,
                    'performance_level': '优秀' if student_score >= full_mark * 0.9 else '良好' if student_score >= full_mark * 0.8 else '中等' if student_score >= full_mark * 0.7 else '及格' if student_score >= full_mark * 0.6 else '不及格'
                }

            # 总分分析
            total_score = student_data['总分']
            total_class_avg = class_averages['总分']
            predicted_total = predicted_scores.get('总分', total_score)

            student_analysis['总分'] = {
                'score': total_score,
                'class_avg': total_class_avg,
                'full_mark': sum(full_marks.values()),
                'percentage': predicted_total,  # 使用预测总分替代得分率
                'vs_class_avg': round(float(total_score) - float(total_class_avg), 1),
                'vs_class_avg_percent': round(
                    (float(total_score) - float(total_class_avg)) / float(total_class_avg) * 100,
                    1) if total_class_avg > 0 else 0,
                'rank': student_rank,
                'total_students': class_stats['total_students'],
                'performance_level': '优秀' if total_score >= sum(full_marks.values()) * 0.9 else '良好' if total_score >= sum(full_marks.values()) * 0.8 else '中等' if total_score >= sum(full_marks.values()) * 0.7 else '及格' if total_score >= sum(full_marks.values()) * 0.6 else '不及格'
            }

            # 添加预测信息到分析结果中
            student_analysis['prediction_info'] = {
                'confidence': prediction_confidence,
                'type': prediction_type,
                'factors': prediction_factors
            }

            # 生成学习建议
            learning_advice = generate_learning_advice(student_analysis, class_stats)

            # 生成成绩预测
            grade_predictions = generate_grade_predictions(student_analysis, class_stats)

            # 生成科目详细分析
            subject_analysis = generate_subject_analysis(student_analysis, class_stats)

            # 创建保存目录
            base_dir = 'data/可视化'
            save_dir = get_save_dir(base_dir, grade, exam_name, class_name, student_name)
            os.makedirs(save_dir, exist_ok=True)

            # 调用visualization.py中的函数生成图表
            radar_path = create_radar_chart(student_data, full_marks, grade, exam_name, class_name, student_id,
                                            student_name, subjects, 'student')
            bar_path = create_bar_chart(student_data, class_averages, grade, exam_name, class_name, student_id,
                                        student_name, subjects, 'student', full_marks)
            table_path = create_table_chart(student_data, grade, exam_name, class_name, student_id, student_name,
                                            subjects, 'student')

            # 返回相对路径
            charts = {
                f'radar_{student_id}': os.path.relpath(radar_path, IMAGES_FOLDER).replace('\\', '/'),
                f'bar_{student_id}': os.path.relpath(bar_path, IMAGES_FOLDER).replace('\\', '/'),
                f'table_{student_id}': os.path.relpath(table_path, IMAGES_FOLDER).replace('\\', '/')
            }

            return jsonify({
                'success': True,
                'charts': charts,
                'student_name': student_name,
                'student_data': student_data,
                'student_analysis': student_analysis,
                'class_averages': class_averages,
                'class_stats': class_stats,
                'student_rank': student_rank,
                'learning_advice': learning_advice,
                'grade_predictions': grade_predictions,
                'subject_analysis': subject_analysis,
                'full_marks': full_marks
            })

        # 如果没有指定学生ID，生成班级总体分析，并为前3名学生生成个人图表
        else:
            # 计算班级平均分
            class_avg_data = {subj: round(float(df[subj].astype(float).mean()), 1) if not df[subj].empty else 0 for subj
                              in available_subjects}
            class_avg_data['总分'] = round(float(df['总分'].astype(float).mean()), 1) if not df['总分'].empty else 0
            class_avg_data['学号'] = grade
            class_avg_data['姓名'] = class_name
            logger.debug(f"返回 class_avg_data: {class_avg_data}")

            # 从数据库获取满分数据
            full_marks = get_full_marks(exam_name, grade)

            # 创建班级平均分保存目录
            base_dir = 'data/可视化'
            class_avg_dir = get_save_dir(base_dir, grade, exam_name, class_name, "班级平均分")
            os.makedirs(class_avg_dir, exist_ok=True)

            # 调用visualization.py中的函数生成图表（传入班级平均分目录）
            radar_path = create_radar_chart(class_avg_data, full_marks, grade, exam_name, class_name, None, class_name,
                                            subjects, 'class_avg')
            bar_path = create_bar_chart(class_avg_data, class_avg_data, grade, exam_name, class_name, None, class_name,
                                        subjects, 'class_avg', full_marks)
            table_path = create_class_table_chart([class_avg_data], grade, exam_name, class_name, subjects, 'class_avg')

            # 返回相对路径（以班级平均分目录为基准）
            charts = {
                'class_radar': os.path.relpath(radar_path, IMAGES_FOLDER).replace('\\', '/'),
                'class_bar': os.path.relpath(bar_path, IMAGES_FOLDER).replace('\\', '/'),
                'class_table': os.path.relpath(table_path, IMAGES_FOLDER).replace('\\', '/')
            }
            logger.debug(
                f"图片路径: radar={charts['class_radar']}, bar={charts['class_bar']}, table={charts['class_table']}")

            # 构建学生信息字典
            student_info = {}
            for _, row in df.iterrows():
                student_info[row['学号']] = {
                    '学号': row['学号'],
                    '姓名': row['姓名'],
                    '排名': int(row['排名']) if pd.notna(row['排名']) else None,
                    '总分': float(row['总分']) if pd.notna(row['总分']) else 0
                }

            # 生成前3名学生的个人图表
            # 将排名列转换为数值型，以便使用nsmallest
            df['排名'] = pd.to_numeric(df['排名'], errors='coerce')
            top3 = df.nsmallest(3, '排名')
            for _, row in top3.iterrows():
                stu_id = row['学号']
                stu_name = row['姓名']
                student_data = {subj: float(row[subj]) for subj in subjects}
                student_data['总分'] = float(row['总分'])
                stu_save_dir = get_save_dir(base_dir, grade, exam_name, class_name, stu_name)
                os.makedirs(stu_save_dir, exist_ok=True)
                radar_path = create_radar_chart(student_data, full_marks, grade, exam_name, class_name, stu_id,
                                                stu_name, subjects, 'student')
                bar_path = create_bar_chart(student_data, class_avg_data, grade, exam_name, class_name, stu_id,
                                            stu_name, subjects, 'student', full_marks)
                table_path = create_table_chart(student_data, grade, exam_name, class_name, stu_id, stu_name, subjects,
                                                'student')
                charts[f'radar_{stu_id}'] = os.path.relpath(radar_path, IMAGES_FOLDER).replace('\\', '/')
                charts[f'bar_{stu_id}'] = os.path.relpath(bar_path, IMAGES_FOLDER).replace('\\', '/')
                charts[f'table_{stu_id}'] = os.path.relpath(table_path, IMAGES_FOLDER).replace('\\', '/')
                charts[f'radar_{stu_name}'] = charts[f'radar_{stu_id}']
                charts[f'bar_{stu_name}'] = charts[f'bar_{stu_id}']
                charts[f'table_{stu_name}'] = charts[f'table_{stu_id}']

            return jsonify({
                'success': True,
                'charts': charts,
                'class_name': class_name,
                'class_avg_data': class_avg_data,  # 这里包含各科平均分和总分
                'class_stats': class_stats,
                'student_info': student_info,
                'full_marks': full_marks  # 添加满分数据
            })

    except Exception as e:
        logger.error(f"Error generating charts: {str(e)}")
        return jsonify({'error': str(e)}), 500


# ==================== 数据库信息API ====================
@app.route('/api/database-info', methods=['GET'])
def get_database_info():
    """获取数据库信息 - 考试名和班级名"""
    try:
        logger.debug("开始获取数据库信息")
        con = Connect(**EXAM_DB_CONFIG)
        cursor = con.cursor()

        try:
            # 获取所有数据库（考试名）
            cursor.execute("SHOW DATABASES")
            all_dbs = cursor.fetchall()
            logger.debug(f"获取到的所有数据库: {all_dbs}")

            # 过滤掉系统数据库和账户数据库
            exams = [
                db[0] for db in all_dbs
                if db[0] not in ['information_schema', 'mysql', 'performance_schema', 'sys', 'account_db']
            ]

            if not exams:
                logger.warning("未找到任何考试数据")
                return jsonify({'error': '未找到任何考试数据'}), 404

            # 获取每个数据库中的表（班级名）
            exam_classes = {}
            for exam in exams:
                try:
                    cursor.execute(f"USE {exam}")
                    cursor.execute("SHOW TABLES")
                    tables = cursor.fetchall()
                    classes = [table[0] for table in tables]
                    exam_classes[exam] = classes
                    logger.debug(f"数据库 {exam} 中的表: {classes}")
                except Exception as e:
                    logger.error(f"检查数据库 {exam} 时出错: {str(e)}")
                    continue

            return jsonify({
                'exams': exams,
                'exam_classes': exam_classes
            })

        finally:
            cursor.close()
            con.close()

    except Exception as e:
        logger.error(f"获取数据库信息时发生错误: {str(e)}")
        return jsonify({
            'error': str(e),
            'message': '获取数据库信息失败，请检查数据库连接和权限'
        }), 500


# ==================== 用户认证API ====================
@app.route('/api/login', methods=['POST'])
def login():
    """用户登录 - 支持管理员、教师、学生三种角色"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        logger.debug(f"收到登录请求 - 用户名: {username}")

        if not username or not password:
            logger.warning("登录失败: 用户名或密码为空")
            return jsonify({'error': '用户名和密码不能为空'}), 400

        # 管理员登录验证
        admin_result = _verify_admin_login(username, password)
        if admin_result:
            return admin_result

        # 教师登录验证
        teacher_result = _verify_teacher_login(username, password)
        if teacher_result:
            return teacher_result

        # 学生登录验证
        student_result = _verify_student_login(username, password)
        if student_result:
            return student_result

        # 登录失败
        logger.warning(f"用户 {username} 登录失败: 用户名或密码错误")
        return jsonify({
            'success': False,
            'message': '用户名或密码错误'
        }), 401

    except Exception as e:
        logger.error(f"登录处理时发生错误: {str(e)}")
        return jsonify({'error': '登录失败，请稍后重试'}), 500


def _verify_admin_login(username, password):
    """管理员登录验证"""
    if verify_admin_login(username, password):
        logger.info(f"管理员 {username} 登录成功")
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db('root')
        cursor = con.cursor()
        cursor.execute("SELECT grade FROM users WHERE account = %s", (username,))
        result = cursor.fetchone()
        cursor.close()
        con.close()
        grades_str = result[0] if result else ''
        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': username,
            'user_type': 'admin',
            'grade': grades_str
        })
    return None


def _verify_teacher_login(username, password):
    """教师登录验证"""
    teacher_result, teacher_grade, teacher_class, is_head_teacher = verify_teacher_login(username, password)
    if teacher_result:
        logger.info(f"教师 {username} 登录成功，年级: {teacher_grade}, 班级: {teacher_class}, 班主任: {is_head_teacher}")
        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': username,
            'user_type': 'teacher',
            'grade': teacher_grade,
            'class_name': teacher_class,
            'is_head_teacher': is_head_teacher
        })
    return None


def _verify_student_login(username, password):
    """学生登录验证"""
    student_result, student_grade = verify_student_login(username, password)
    if student_result:
        logger.info(f"学生 {username} 登录成功，年级: {student_grade}")
        try:
            student_info = get_student_account_info(student_grade, username)
            if student_info and 'name' in student_info and 'class_name' in student_info:
                return jsonify({
                    'success': True,
                    'message': '登录成功',
                    'user': username,
                    'user_type': 'student',
                    'grade': student_grade,
                    'name': student_info['name'],
                    'class_name': student_info['class_name']
                })
        except Exception as e:
            logger.warning(f"获取学生详细信息失败: {str(e)}")

        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': username,
            'user_type': 'student',
            'grade': student_grade
        })
    return None


# ==================== 年级班级关联API ====================
@app.route('/api/grade-classes', methods=['GET'])
def get_grade_classes():
    """根据年级获取班级列表 - 支持教师权限过滤和考试过滤"""
    grade = request.args.get('grade')
    exam = request.args.get('exam')
    teacher_username = request.args.get('teacher_username')

    if not grade:
        return jsonify({'error': '缺少年级参数'}), 400

    try:
        if teacher_username:
            class_list = _get_teacher_classes(grade, teacher_username, exam)
        else:
            class_list = _get_all_classes(grade, exam)

        return jsonify({'classes': class_list})

    except Exception as e:
        logger.error(f"获取班级列表失败: {str(e)}")
        return jsonify({'error': f'获取班级列表失败: {str(e)}'}), 500


def _get_teacher_classes(grade, teacher_username, exam=None):
    """获取教师任教的班级列表"""
    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()

    # 查询教师的任教信息
    cursor.execute('''
        SELECT manage_class, is_class_teacher
        FROM teachers
        WHERE username = %s
    ''', (teacher_username,))

    result = cursor.fetchone()
    cursor.close()
    con.close()

    if not result:
        raise Exception('未找到教师信息')

    manage_class, is_class_teacher = result
    teacher_classes = set()

    # 班主任管理的班级
    if manage_class and manage_class != '0':
        teacher_classes.add(manage_class)

    # 任课教师的班级
    if is_class_teacher and is_class_teacher != '0':
        class_list = [c.strip() for c in is_class_teacher.split(',') if c.strip() and c.strip() != '0']
        teacher_classes.update(class_list)

    # 考试过滤
    if exam:
        try:
            exam_classes = set(get_class_list_from_exam(grade, exam))
            teacher_classes = teacher_classes.intersection(exam_classes)
        except Exception:
            teacher_classes = set()

    return sorted(list(teacher_classes))


def _get_all_classes(grade, exam=None):
    """获取年级下所有班级"""
    if exam:
        return get_class_list_from_exam(grade, exam)
    else:
        return get_class_list_by_grade(grade)


@app.route('/api/grade-exams', methods=['GET'])
def get_grade_exams():
    """根据年级获取考试列表"""
    grade = request.args.get('grade')
    if not grade or grade == 'undefined':
        return jsonify({'error': '缺少年级参数'}), 400

    exam_list = get_exam_list_by_grade(grade)
    return jsonify({'exams': exam_list})


# ==================== 用户信息API ====================
@app.route('/api/teacher-info', methods=['GET'])
def api_teacher_info():
    """获取教师信息 - 支持按年级和用户名查询"""
    grade = request.args.get('grade')
    username = request.args.get('username')

    if not grade:
        return jsonify({'error': '缺少年级参数'}), 400

    try:
        info = get_teacher_info(grade, username)
        return jsonify(info)
    except Exception as e:
        logger.error(f"获取教师信息失败: {str(e)}")
        return jsonify({'error': '获取教师信息失败'}), 500


@app.route('/api/student-info', methods=['GET'])
def api_student_info():
    """获取学生信息 - 支持按年级、用户名、班级查询"""
    grade = request.args.get('grade')
    username = request.args.get('username')
    class_name = request.args.get('class_name')

    if not grade or grade == 'undefined':
        return jsonify({'error': '缺少年级参数'}), 400

    try:
        info = get_student_account_info(grade, username, class_name)
        return jsonify(info)
    except Exception as e:
        logger.error(f"获取学生信息失败: {str(e)}")
        return jsonify({'error': '获取学生信息失败'}), 500


# ==================== 数据导出API ====================
@app.route('/export_data', methods=['POST'])
def export_data():
    """导出成绩数据 - 支持动态字段和多种格式"""
    try:
        data = request.get_json()
        logger.info(f"收到导出数据请求: {data}")

        grade = data.get('grade')
        exam = data.get('exam')
        class_name = data.get('class_name')
        format_type = data.get('format', 'xlsx')

        logger.info(f"导出参数 - 年级: {grade}, 考试: {exam}, 班级: {class_name}, 格式: {format_type}")

        if not all([grade, exam, class_name]):
            logger.warning("缺少必要参数")
            return jsonify({'success': False, 'error': '请选择年级、考试和班级'}), 400

        # 动态获取字段名和数据
        logger.info(f"连接数据库: {grade}")
        with get_db_connection(grade) as (_, cursor):
            logger.info(f"查询表结构: {exam}")
            cursor.execute(f"SHOW COLUMNS FROM `{exam}`")
            columns = [col[0] for col in cursor.fetchall()]
            logger.info(f"表字段: {columns}")

            # 检查表结构，确定正确的查询方式
            if 'class_name' in columns:
                # 新表结构：使用class_name字段过滤
                logger.info(f"使用新表结构查询: SELECT * FROM `{exam}` WHERE class_name = '{class_name}'")
                cursor.execute(f"SELECT * FROM `{exam}` WHERE class_name = %s", (class_name,))
                results = cursor.fetchall()
            else:
                # 旧表结构：表名就是班级名，需要重新获取列名
                logger.info(f"使用旧表结构查询: SELECT * FROM `{class_name}`")
                cursor.execute(f"SHOW COLUMNS FROM `{class_name}`")
                columns = [col[0] for col in cursor.fetchall()]
                logger.info(f"旧表结构字段: {columns}")
                cursor.execute(f"SELECT * FROM `{class_name}`")
                results = cursor.fetchall()

            logger.info(f"查询到 {len(results)} 条记录")

        if not results:
            logger.warning("未找到成绩数据")
            return jsonify({'success': False, 'error': '未找到成绩数据'}), 404

        # 创建DataFrame并导出
        df = pd.DataFrame(results, columns=columns)

        # 重命名列名为更友好的中文名称
        column_mapping = {
            'id': 'ID',
            'class_name': '班级',
            'student_id': '学号',
            'name': '姓名',
            'class_rank': '班级排名',
            'grade_rank': '年级排名',
            '排名': '班级排名'  # 处理旧表结构
        }

        # 应用列名映射
        df = df.rename(columns=column_mapping)
        logger.info(f"重命名后的列名: {list(df.columns)}")

        # 如果存在ID列且不需要，可以删除
        if 'ID' in df.columns:
            df = df.drop('ID', axis=1)
            logger.info("已删除ID列")

        export_dir = os.path.join(app.config['DOWNLOAD_FOLDER'], grade, class_name, exam)
        os.makedirs(export_dir, exist_ok=True)
        logger.info(f"创建导出目录: {export_dir}")

        filename = f"{grade}-{exam}-{class_name}.{format_type}"
        filepath = os.path.join(export_dir, filename)
        logger.info(f"导出文件路径: {filepath}")

        if format_type == 'xlsx':
            df.to_excel(filepath, index=False)
        else:
            df.to_csv(filepath, index=False, encoding='utf-8-sig')

        logger.info("文件导出成功")
        return jsonify({
            'success': True,
            'message': f'文件已保存至：{filepath}',
            'filepath': filepath
        })

    except Exception as e:
        logger.error(f"导出数据时发生错误: {str(e)}")
        return jsonify({'success': False, 'error': f'导出数据失败：{str(e)}'}), 500


@app.route('/download_image', methods=['POST'])
def download_image():
    """下载图片到指定目录"""
    try:
        data = request.get_json()
        image_path = data.get('image_path')
        grade = data.get('grade')
        exam = data.get('exam')
        class_name = data.get('class_name')
        student_name = data.get('student_name', '')

        if not all([image_path, grade, exam, class_name]):
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400

        # 构建源图片路径
        source_path = os.path.join(app.config['IMAGES_FOLDER'], image_path)
        if not os.path.exists(source_path):
            return jsonify({'success': False, 'error': '图片不存在'}), 404

        # 构建目标路径
        target_dir = os.path.join(
            app.config['DOWNLOAD_FOLDER'],
            grade, class_name, exam,
            student_name if student_name else ''
        )
        os.makedirs(target_dir, exist_ok=True)

        # 复制图片
        filename = os.path.basename(source_path)
        target_path = os.path.join(target_dir, filename)
        shutil.copy2(source_path, target_path)

        return jsonify({
            'success': True,
            'message': f'图片已下载至：{target_path}',
            'filepath': target_path
        })

    except Exception as e:
        logger.error(f"下载图片时发生错误: {str(e)}")
        return jsonify({'success': False, 'error': f'下载图片失败：{str(e)}'}), 500


@app.route('/api/leaves', methods=['GET', 'POST'])
def api_leaves():
    """
    GET: 学生查自己/老师查班级请假
    POST: 学生申请请假
    """
    if request.method == 'GET':
        grade = request.args.get('grade')
        student_id = request.args.get('student_id')
        class_name = request.args.get('class_name')

        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400

        if student_id:
            # 学生查自己
            try:
                records = get_student_leaves(grade, student_id)
                return jsonify({'success': True, 'data': [
                    {
                        'id': r[0],
                        'start_date': str(r[1]),
                        'end_date': str(r[2]),
                        'reason': r[3],
                        'status': r[4],
                        'create_time': str(r[5])
                    } for r in records
                ]})
            except Exception as e:
                logger.error(f"获取学生请假记录失败: {str(e)}")
                return jsonify({'success': False, 'error': f'获取请假记录失败: {str(e)}'}), 500
        elif class_name:
            # 老师查班级
            try:
                records = get_class_leaves(grade, class_name)
                return jsonify({'success': True, 'data': [
                    {
                        'id': r[0],
                        'student_id': r[1],
                        'name': r[2],
                        'start_date': str(r[3]),
                        'end_date': str(r[4]),
                        'reason': r[5],
                        'status': r[6],
                        'create_time': str(r[7])
                    } for r in records
                ]})
            except Exception as e:
                logger.error(f"获取班级请假记录失败: {str(e)}")
                return jsonify({'success': False, 'error': f'获取请假记录失败: {str(e)}'}), 500
        else:
            return jsonify({'success': False, 'error': '缺少学生ID或班级参数'}), 400
    elif request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                logger.warning("请假申请: 未收到JSON数据")
                return jsonify({'success': False, 'error': '请求数据格式错误'}), 400

            logger.debug(f"收到请假申请数据: {data}")

            grade = data.get('grade')
            student_id = data.get('student_id')
            name = data.get('name')
            class_name = data.get('class_name')
            start_date = data.get('start_date')
            end_date = data.get('end_date')
            reason = data.get('reason')

            logger.debug(f"请假申请参数: grade={grade}, student_id={student_id}, name={name}, class_name={class_name}, start_date={start_date}, end_date={end_date}, reason={reason}")

            # 参数验证 - 逐个检查并记录缺失的参数
            missing_params = []
            if not grade:
                missing_params.append('年级')
            if not student_id:
                missing_params.append('学号')
            if not name:
                missing_params.append('姓名')
            if not class_name:
                missing_params.append('班级')
            if not start_date:
                missing_params.append('开始日期')
            if not end_date:
                missing_params.append('结束日期')
            if not reason:
                missing_params.append('请假原因')

            if missing_params:
                error_msg = f"缺少必要参数: {', '.join(missing_params)}"
                logger.warning(f"请假申请参数验证失败: {error_msg}")
                return jsonify({'success': False, 'error': error_msg}), 400

            # 日期验证 - 支持多种日期格式
            try:
                from datetime import datetime

                # 尝试多种日期格式
                date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d']
                start_dt = None
                end_dt = None

                for fmt in date_formats:
                    try:
                        start_dt = datetime.strptime(start_date, fmt).date()
                        end_dt = datetime.strptime(end_date, fmt).date()
                        break
                    except ValueError:
                        continue

                if start_dt is None or end_dt is None:
                    logger.warning(f"日期格式错误: start_date={start_date}, end_date={end_date}")
                    return jsonify({'success': False, 'error': '日期格式不正确，请使用YYYY-MM-DD格式'}), 400

                today = datetime.now().date()

                # 允许申请当天及以后的请假
                if start_dt < today:
                    logger.warning(f"申请过去日期: start_date={start_dt}, today={today}")
                    return jsonify({'success': False, 'error': '不能申请过去的日期'}), 400

                if end_dt < start_dt:
                    logger.warning(f"结束日期早于开始日期: start_date={start_dt}, end_date={end_dt}")
                    return jsonify({'success': False, 'error': '结束日期不能早于开始日期'}), 400

            except Exception as e:
                logger.error(f"日期验证异常: {str(e)}")
                return jsonify({'success': False, 'error': '日期验证失败'}), 400

            # 添加请假记录
            add_leave_record(grade, student_id, name, class_name, start_date, end_date, reason)
            logger.info(f"请假申请成功: student_id={student_id}, name={name}, start_date={start_date}, end_date={end_date}")
            return jsonify({'success': True, 'message': '请假申请提交成功'})

        except Exception as e:
            logger.error(f"添加请假记录失败: {str(e)}")
            return jsonify({'success': False, 'error': f'申请失败: {str(e)}'}), 500


@app.route('/api/leaves/<int:leave_id>', methods=['PUT', 'DELETE'])
def api_leave_update(leave_id):
    """
    PUT: 老师审批/学生销假
    DELETE: 删除请假记录
    """
    if request.method == 'PUT':
        try:
            data = request.get_json()
            if not data:
                logger.warning(f"请假更新: 未收到JSON数据, leave_id={leave_id}")
                return jsonify({'success': False, 'error': '请求数据格式错误'}), 400

            logger.debug(f"收到请假更新请求: leave_id={leave_id}, data={data}")

            grade = data.get('grade')
            status = data.get('status')
            action = data.get('action')  # 前端可能发送action参数
            student_id = data.get('student_id')  # 学生销假时需要验证身份

            # 处理前端发送的action参数
            if action == 'cancel' and not status:
                status = '已销假'

            if not grade:
                logger.warning(f"请假更新: 缺少年级参数, leave_id={leave_id}")
                return jsonify({'success': False, 'error': '缺少年级参数'}), 400

            if not status:
                logger.warning(f"请假更新: 缺少状态参数, leave_id={leave_id}, data={data}")
                return jsonify({'success': False, 'error': '缺少状态参数'}), 400

            # 如果是学生销假，需要验证身份和状态
            if student_id and status == '已销假':
                # 验证请假记录是否属于该学生且状态为已批准且已结束
                con = Connect(**ACCOUNT_DB_CONFIG)
                con.select_db(grade)
                cursor = con.cursor()
                try:
                    cursor.execute('''
                        SELECT status, end_date FROM leave_records
                        WHERE id = %s AND student_id = %s
                    ''', (leave_id, student_id))
                    result = cursor.fetchone()

                    if not result:
                        logger.warning(f"请假销假: 记录不存在或无权限, leave_id={leave_id}, student_id={student_id}")
                        return jsonify({'success': False, 'error': '请假记录不存在或无权限操作'}), 403

                    current_status, end_date = result
                    from datetime import datetime
                    today = datetime.now().date()

                    if current_status != '已批准':
                        logger.warning(f"请假销假: 状态不允许销假, leave_id={leave_id}, current_status={current_status}")
                        return jsonify({'success': False, 'error': '只能对已批准的请假进行销假'}), 400

                    if end_date > today:
                        logger.warning(f"请假销假: 请假尚未结束, leave_id={leave_id}, end_date={end_date}, today={today}")
                        return jsonify({'success': False, 'error': '请假尚未结束，无法销假'}), 400
                finally:
                    cursor.close()
                    con.close()

            # 如果是教师审批，验证教师权限（可选，因为前端已经过滤了班级）
            # 这里可以添加额外的权限验证逻辑

            update_leave_status(grade, leave_id, status)
            logger.info(f"请假状态更新成功: leave_id={leave_id}, status={status}")
            return jsonify({'success': True})

        except Exception as e:
            logger.error(f"更新请假状态失败: leave_id={leave_id}, error={str(e)}")
            return jsonify({'success': False, 'error': f'操作失败: {str(e)}'}), 500
    elif request.method == 'DELETE':
        grade = request.args.get('grade')
        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400

        try:
            delete_leave_record(grade, leave_id)
            return jsonify({'success': True})
        except Exception as e:
            logger.error(f"删除请假记录失败: {str(e)}")
            return jsonify({'success': False, 'error': f'删除失败: {str(e)}'}), 500


@app.route('/api/update-student', methods=['POST'])
def update_student():
    """更新学生账号、密码和教师留言"""
    data = request.get_json()
    grade = data.get('grade')
    student_db_id = data.get('student_db_id')
    new_username = data.get('new_username')
    new_password = data.get('new_password')
    teacher_message = data.get('teacher_message', '')
    if not all([grade, student_db_id, new_username, new_password]):
        return jsonify({'success': False, 'error': '参数不完整'}), 400
    try:
        student_db_id = int(student_db_id)
    except (ValueError, TypeError):
        return jsonify({'success': False, 'error': '无效的学生ID'}), 400
    result = update_student_account_info(grade, student_db_id, new_username, new_password, teacher_message)
    if result:
        return jsonify({'success': True, 'message': '保存成功'})
    else:
        return jsonify({'success': False, 'error': '保存失败'}), 500


@app.route('/api/messages', methods=['GET'])
def api_messages():
    """获取教师留言"""
    grade = request.args.get('grade')
    student_id = request.args.get('student_id')

    if not grade or not student_id:
        return jsonify({'success': False, 'error': '缺少必要参数'}), 400

    try:
        # 从学生信息中获取教师留言
        student_info = get_student_account_info(grade, student_id)
        if student_info and 'teacher_message' in student_info and student_info['teacher_message']:
            return jsonify({
                'success': True,
                'data': [{
                    'teacher_name': '班主任',
                    'message': student_info['teacher_message'],
                    'create_time': '最近更新'
                }]
            })
        else:
            return jsonify({'success': True, 'data': []})
    except Exception as e:
        logger.error(f"获取教师留言失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取留言失败'}), 500


@app.route('/api/grades', methods=['GET'])
def api_grades():
    """获取学生成绩"""
    grade = request.args.get('grade')
    student_id = request.args.get('student_id')

    if not grade or not student_id:
        return jsonify({'success': False, 'error': '缺少必要参数'}), 400

    try:
        # 获取该年级的所有考试
        exam_list = get_exam_list_by_grade(grade)
        if not exam_list:
            return jsonify({'success': True, 'data': []})

        # 获取学生信息
        student_info = get_student_account_info(grade, student_id)
        if not student_info or 'name' not in student_info or 'class_name' not in student_info or 'student_id' not in student_info:
            return jsonify({'success': True, 'data': [], 'trend_analysis': {}})

        student_name = str(student_info['name']).strip()
        class_name = student_info['class_name']
        student_id_val = str(student_info['student_id']).strip()

        # 获取每个考试的成绩
        grade_data = []
        for exam in exam_list:
            try:
                # 获取该考试该班级的成绩数据
                results = get_student_scores(grade, exam, class_name)
                if results:
                    # 查找该学生的成绩 - 优先用学号匹配，姓名作为兜底
                    for result in results:
                        logger.debug(f"查找: student_id_val={student_id_val}, 成绩表学号={result[1]}, 姓名={result[2]}")
                        if len(result) >= 10 and (
                                str(result[1]).strip() == str(student_id_val).strip() or result[2] == student_name
                        ):
                            total_score = result[9] if result[9] is not None else 0  # 总分
                            rank = result[0] if result[0] is not None else None  # 排名
                            grade_data.append({
                                'exam_name': exam,
                                'total_score': float(total_score) if total_score else 0,
                                'rank': int(rank) if rank else None
                            })
                            break
            except Exception as e:
                logger.warning(f"获取考试 {exam} 成绩失败: {str(e)}")
                continue

        return jsonify({'success': True, 'data': grade_data})
    except Exception as e:
        logger.error(f"获取学生成绩失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取成绩失败'}), 500


@app.route('/api/student-grade-trend', methods=['GET'])
def api_student_grade_trend():
    """获取学生成绩趋势分析"""
    grade = request.args.get('grade')
    student_id = request.args.get('student_id')

    if not grade or not student_id:
        return jsonify({'success': False, 'error': '缺少必要参数'}), 400

    try:
        # 获取该年级的所有考试
        exam_list = get_exam_list_by_grade(grade)
        if not exam_list:
            return jsonify({'success': True, 'data': [], 'trend_analysis': {}})

        # 获取学生信息
        student_info = get_student_account_info(grade, student_id)
        if not student_info or 'name' not in student_info or 'class_name' not in student_info or 'student_id' not in student_info:
            return jsonify({'success': True, 'data': [], 'trend_analysis': {}})

        student_name = str(student_info['name']).strip()
        class_name = student_info['class_name']
        student_id_val = str(student_info['student_id']).strip()

        # 获取每个考试的详细成绩
        trend_data = []
        subjects = get_courses_list(grade)

        for exam in exam_list:
            try:
                # 获取该考试该班级的成绩数据
                results = get_student_scores(grade, exam, class_name)
                if results:
                    # 查找该学生的成绩
                    for result in results:
                        # result[2]:学号, result[3]:姓名
                        result_id = str(result[2]).strip() if len(result) > 2 else ''
                        result_name = str(result[3]).strip() if len(result) > 3 else ''
                        if (result_id == student_id_val) or (result_name == student_name):
                            exam_data = {
                                'exam_name': exam,
                                'scores': {subj: result[idx] if len(result) > idx else None for idx, subj in
                                           zip(range(4, 10), subjects)},
                                'total_score': result[10] if len(result) > 10 else None,
                                'class_rank': result[11] if len(result) > 11 else None,
                                'grade_rank': result[12] if len(result) > 12 else None
                            }
                            trend_data.append(exam_data)
                            break
            except Exception as e:
                logger.warning(f"Error processing exam {exam}: {e}")
        # ... existing code ...

        # 计算趋势分析
        trend_analysis = {}
        if trend_data:
            # 计算最佳班级排名
            class_ranks = [int(d.get('class_rank')) for d in trend_data if d.get('class_rank') is not None]
            best_class_rank = min(class_ranks) if class_ranks else None
            # 计算进步幅度（总分）
            total_scores = [float(d.get('total_score')) for d in trend_data if d.get('total_score') is not None]
            improvement = None
            if len(total_scores) >= 2:
                improvement = round(total_scores[-1] - total_scores[0], 2)
            # 计算各科平均分
            subjects = get_courses_list(grade)
            subject_averages = {}
            for subj in subjects:
                scores = [float(d['scores'].get(subj)) for d in trend_data if d['scores'].get(subj) is not None]
                subject_averages[subj] = round(sum(scores) / len(scores), 2) if scores else None
            trend_analysis = {
                'best_class_rank': best_class_rank,
                'improvement': improvement,
                'subject_averages': subject_averages
            }
        # 返回 trend_data 供前端趋势图表使用
        return jsonify({
            'success': True,
            'data': trend_data,
            'exam_count': len(trend_data),
            'trend_analysis': trend_analysis
        })
    except Exception as e:
        logger.error(f"获取学生成绩趋势失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取成绩趋势失败'}), 500


@app.route('/api/student-messages', methods=['POST', 'GET'])
def api_student_messages():
    if request.method == 'POST':
        data = request.get_json()
        grade = data.get('grade')
        student_id = data.get('student_id')
        name = data.get('name')
        class_name = data.get('class_name')
        is_anonymous = data.get('is_anonymous', False)
        message = data.get('message')
        if not all([grade, student_id, name, class_name, message]):
            return jsonify({'success': False, 'error': '参数不完整'}), 400
        try:
            add_student_message(grade, student_id, name, class_name, is_anonymous, message)
            return jsonify({'success': True})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    elif request.method == 'GET':
        grade = request.args.get('grade')
        student_id = request.args.get('student_id')
        class_name = request.args.get('class_name')
        if not grade:
            return jsonify({'success': False, 'error': '参数不完整'}), 400
        try:
            if class_name and not student_id:
                # 查询全班留言
                messages = get_class_messages(grade, class_name)
                return jsonify({'success': True, 'data': messages})
            elif student_id:
                messages = get_student_messages(grade, student_id)
            return jsonify({'success': True, 'data': messages})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/student-messages/<int:msg_id>', methods=['DELETE'])
def delete_student_message_api(msg_id):
    grade = request.args.get('grade')
    student_id = request.args.get('student_id')
    is_admin = request.args.get('admin') == '1'
    if not grade:
        return jsonify({'success': False, 'error': '参数不完整'}), 400
    try:
        result = delete_student_message(grade, msg_id, student_id, is_admin)
        if result:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': '删除失败或无权限'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/teacher-message-history', methods=['GET', 'POST'])
def api_teacher_message_history():
    if request.method == 'GET':
        grade = request.args.get('grade')
        student_id = request.args.get('student_id')
        if not all([grade, student_id]):
            return jsonify({'success': False, 'error': '参数不完整'}), 400
        try:
            messages = get_teacher_message_history(grade, student_id)
            return jsonify({'success': True, 'data': messages})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500
    elif request.method == 'POST':
        data = request.get_json()
        grade = data.get('grade')
        student_id = data.get('student_id')
        teacher_name = data.get('teacher_name')
        message = data.get('message')
        if not all([grade, student_id, teacher_name, message]):
            return jsonify({'success': False, 'error': '参数不完整'}), 400
        try:
            add_teacher_message_history(grade, student_id, teacher_name, message)
            return jsonify({'success': True})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/teacher-message-history/<int:msg_id>', methods=['DELETE'])
def api_delete_teacher_message_history(msg_id):
    grade = request.args.get('grade')
    if not grade:
        return jsonify({'success': False, 'error': '参数不完整'}), 400
    try:
        result = delete_teacher_message_history(grade, msg_id)
        if result:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': '删除失败'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/teachers', methods=['GET'])
def api_teachers():
    grade = request.args.get('grade')
    class_name = request.args.get('class_name')
    try:
        teachers = []
        if not grade:
            grade_list = get_grade_list()
            for g in grade_list:
                try:
                    info = get_teacher_info(g, class_name=None)
                    for t in info.get('teachers', []):
                        t['grade'] = g
                        teachers.append(t)
                except Exception as e:
                    continue
        else:
            if class_name:
                info = get_teacher_info(grade, class_name=class_name)
            else:
                info = get_teacher_info(grade, class_name=None)
            for t in info.get('teachers', []):
                t['grade'] = grade
                teachers.append(t)
        # 字段同步，类型安全
        for t in teachers:
            t['id'] = int(t.get('id', 0))
            t['name'] = str(t.get('name', ''))
            t['username'] = str(t.get('username', ''))
            t['phone'] = str(t.get('phone', ''))
            t['subjects'] = str(t.get('subjects', ''))
            t['certificate_level'] = str(t.get('certificate_level', ''))
            t['remark'] = str(t.get('remark', ''))
            t['is_head_teacher'] = int(t.get('is_head_teacher', 0))
            t['manage_class'] = str(t.get('manage_class', '0'))
            t['is_class_teacher'] = str(t.get('is_class_teacher', '0'))
            t.pop('class_name', None)
            t.pop('teach_classes', None)
            t.pop('password', None)
        return jsonify({'success': True, 'teachers': teachers})
    except Exception as e:
        logger.error(f"获取教师列表失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取教师列表失败，请联系管理员。'}), 500


@app.route('/api/teachers', methods=['POST'])
def api_add_teacher():
    data = request.get_json()
    grade = data.get('grade')
    name = data.get('name')
    username = data.get('username')
    password = data.get('password')
    phone = data.get('phone', '')
    certificate_level = data.get('certificate_level', '')
    subjects = data.get('subjects', '')
    remark = data.get('remark', '')
    # 字段校验
    if not grade or not name or not username or not password:
        return jsonify({'success': False, 'error': '年级、姓名、账号和密码不能为空'}), 400
    if len(username) > 50 or len(name) > 255 or len(password) > 50:
        return jsonify({'success': False, 'error': '账号、姓名或密码长度超限'}), 400
    if len(phone) > 20:
        return jsonify({'success': False, 'error': '电话长度超限'}), 400
    if len(certificate_level) > 50:
        return jsonify({'success': False, 'error': '证级长度超限'}), 400
    if len(subjects) > 500:
        return jsonify({'success': False, 'error': '科目长度超限'}), 400
    if len(remark) > 255:
        return jsonify({'success': False, 'error': '备注长度超限'}), 400
    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()
        cursor.execute('SELECT id FROM teachers WHERE username = %s', (username,))
        if cursor.fetchone():
            cursor.close()
            con.close()
            return jsonify({'success': False, 'error': '账号已存在'}), 400
        cursor.execute('''
            INSERT INTO teachers 
            (username, password, name, phone, grade, is_head_teacher, manage_class, subjects, is_class_teacher, certificate_level, remark)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ''', (username, password, name, phone, grade, 0, '0', subjects, '0', certificate_level, remark))
        con.commit()
        cursor.close()
        con.close()
        return jsonify({'success': True, 'message': '教师添加成功'})
    except Exception as e:
        logger.error(f"添加教师失败: {str(e)}")
        return jsonify({'success': False, 'error': '添加教师失败，请联系管理员。'}), 500


@app.route('/api/teachers/<int:teacher_id>', methods=['GET', 'PUT', 'DELETE'])
def api_teacher_manage(teacher_id):
    if request.method == 'GET':
        grade = request.args.get('grade')
        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400
        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()
            cursor.execute('''
                SELECT id, username, password, name, phone, grade, is_head_teacher, manage_class, subjects, is_class_teacher, certificate_level, remark
                FROM teachers 
                WHERE id = %s
            ''', (teacher_id,))
            result = cursor.fetchone()
            cursor.close()
            con.close()
            if not result:
                return jsonify({'success': False, 'error': '教师不存在'}), 404
            teacher_info = {
                'id': int(result[0]),
                'username': str(result[1]),
                'password': str(result[2]),  # 保留密码字段
                'name': str(result[3]),
                'phone': str(result[4] or ''),
                'grade': str(result[5] or ''),
                'is_head_teacher': int(result[6] or 0),
                'manage_class': str(result[7] or '0'),
                'subjects': str(result[8] or ''),
                'is_class_teacher': str(result[9] or '0'),
                'certificate_level': str(result[10] or ''),
                'remark': str(result[11] or '')
            }
            return jsonify({'success': True, 'teacher': teacher_info})
        except Exception as e:
            logger.error(f"获取教师信息失败: {str(e)}")
            return jsonify({'success': False, 'error': '获取教师信息失败，请联系管理员。'}), 500
    elif request.method == 'PUT':
        data = request.get_json()
        grade = data.get('grade')
        name = data.get('name')
        username = data.get('username')
        password = data.get('password')
        phone = data.get('phone', '')
        certificate_level = data.get('certificate_level', '')
        subjects = data.get('subjects', '')
        remark = data.get('remark', '')
        if not grade or not name or not username:
            return jsonify({'success': False, 'error': '年级、姓名和账号不能为空'}), 400
        if len(username) > 50 or len(name) > 255:
            return jsonify({'success': False, 'error': '账号或姓名长度超限'}), 400
        if password and len(password) > 50:
            return jsonify({'success': False, 'error': '密码长度超限'}), 400
        if len(phone) > 20:
            return jsonify({'success': False, 'error': '电话长度超限'}), 400
        if len(certificate_level) > 50:
            return jsonify({'success': False, 'error': '证级长度超限'}), 400
        if len(subjects) > 500:
            return jsonify({'success': False, 'error': '科目长度超限'}), 400
        if len(remark) > 255:
            return jsonify({'success': False, 'error': '备注长度超限'}), 400
        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()
            cursor.execute('SELECT id FROM teachers WHERE id = %s', (teacher_id,))
            if not cursor.fetchone():
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '教师不存在'}), 404
            cursor.execute('SELECT id FROM teachers WHERE username = %s AND id != %s', (username, teacher_id))
            if cursor.fetchone():
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '账号已存在'}), 400
            if password:
                cursor.execute('''
                    UPDATE teachers 
                    SET name = %s, username = %s, password = %s, phone = %s, certificate_level = %s, subjects = %s, remark = %s
                    WHERE id = %s
                ''', (name, username, password, phone, certificate_level, subjects, remark, teacher_id))
            else:
                cursor.execute('''
                    UPDATE teachers 
                    SET name = %s, username = %s, phone = %s, certificate_level = %s, subjects = %s, remark = %s
                    WHERE id = %s
                ''', (name, username, phone, certificate_level, subjects, remark, teacher_id))
            con.commit()
            cursor.close()
            con.close()
            return jsonify({'success': True, 'message': '教师信息更新成功'})
        except Exception as e:
            logger.error(f"更新教师信息失败: {str(e)}")
            return jsonify({'success': False, 'error': '更新教师信息失败，请联系管理员。'}), 500
    elif request.method == 'DELETE':
        grade = request.args.get('grade')
        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400
        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()
            cursor.execute('SELECT name FROM teachers WHERE id = %s', (teacher_id,))
            result = cursor.fetchone()
            if not result:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '教师不存在'}), 404
            teacher_name = result[0]
            cursor.execute('SELECT COUNT(*) FROM class WHERE head_teacher = %s', (teacher_name,))
            class_count = cursor.fetchone()[0]
            if class_count > 0:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': f'该教师是{class_count}个班级的班主任，无法删除'}), 400
            cursor.execute('DELETE FROM teachers WHERE id = %s', (teacher_id,))
            if cursor.rowcount == 0:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '删除失败'}), 500
            con.commit()
            cursor.close()
            con.close()
            return jsonify({'success': True, 'message': '教师删除成功'})
        except Exception as e:
            logger.error(f"删除教师失败: {str(e)}")
            return jsonify({'success': False, 'error': '删除教师失败，请联系管理员。'}), 500


@app.route('/api/students', methods=['GET'])
def api_students():
    """获取学生列表API"""
    grade = request.args.get('grade')
    class_name = request.args.get('class_name')
    try:
        students = []
        if not grade:
            grade_list = get_grade_list()
            for g in grade_list:
                try:
                    info = get_student_account_info(g, class_name=class_name)
                    for s in info.get('students', []):
                        s['grade'] = g
                        students.append(s)
                except Exception as e:
                    continue
        else:
            if class_name:
                info = get_student_account_info(grade, class_name=class_name)
            else:
                info = get_student_account_info(grade, class_name=None)
            for s in info.get('students', []):
                s['grade'] = grade
                students.append(s)

        # 字段同步，类型安全
        for s in students:
            s['id'] = int(s.get('id', 0))
            s['username'] = str(s.get('username', ''))
            s['password'] = str(s.get('password', ''))
            s['student_id'] = str(s.get('student_id', ''))
            s['name'] = str(s.get('name', ''))
            s['class_name'] = str(s.get('class_name', ''))
            s['grade'] = str(s.get('grade', ''))
            s['teacher_message'] = str(s.get('teacher_message', ''))
            s['phone'] = str(s.get('phone', ''))
            s['feedback'] = str(s.get('feedback', ''))

        return jsonify({'success': True, 'students': students})
    except Exception as e:
        logger.error(f"获取学生列表失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取学生列表失败，请联系管理员。'}), 500


@app.route('/api/students/<int:student_id>', methods=['GET', 'PUT', 'DELETE'])
def api_student_manage(student_id):
    """学生管理API - 获取、更新、删除单个学生"""
    if request.method == 'GET':
        grade = request.args.get('grade')
        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400
        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()
            cursor.execute('''
                SELECT id, username, password, student_id, name, class_name, grade, teacher_message, phone, feedback
                FROM students 
                WHERE id = %s
            ''', (student_id,))
            result = cursor.fetchone()
            cursor.close()
            con.close()
            if not result:
                return jsonify({'success': False, 'error': '学生不存在'}), 404
            student_info = {
                'id': int(result[0]),
                'username': str(result[1]),
                'password': str(result[2]),
                'student_id': str(result[3]),
                'name': str(result[4]),
                'class_name': str(result[5]),
                'grade': str(result[6]),
                'teacher_message': str(result[7] or ''),
                'phone': str(result[8] or ''),
                'feedback': str(result[9] or '')
            }
            return jsonify({'success': True, 'student': student_info})
        except Exception as e:
            logger.error(f"获取学生信息失败: {str(e)}")
            return jsonify({'success': False, 'error': '获取学生信息失败，请联系管理员。'}), 500

    elif request.method == 'PUT':
        data = request.get_json()
        grade = data.get('grade')
        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400
        try:
            result = update_student(grade, student_id, data)
            if result:
                return jsonify({'success': True, 'message': '学生信息更新成功'})
            else:
                return jsonify({'success': False, 'error': '学生信息更新失败'}), 500
        except Exception as e:
            logger.error(f"更新学生信息失败: {str(e)}")
            return jsonify({'success': False, 'error': f'更新学生信息失败: {str(e)}'}), 500

    elif request.method == 'DELETE':
        grade = request.args.get('grade')
        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400
        try:
            result = delete_student(grade, student_id)
            if result:
                return jsonify({'success': True, 'message': '学生删除成功'})
            else:
                return jsonify({'success': False, 'error': '学生删除失败'}), 500
        except Exception as e:
            logger.error(f"删除学生失败: {str(e)}")
            return jsonify({'success': False, 'error': f'删除学生失败: {str(e)}'}), 500


@app.route('/api/students', methods=['POST'])
def api_add_student():
    data = request.get_json()
    grade = data.get('grade')
    if not grade:
        return jsonify({'success': False, 'error': '缺少年级参数'}), 400
    try:
        result = add_student(grade, data)
        if result:
            return jsonify({'success': True, 'message': '学生添加成功'})
        else:
            return jsonify({'success': False, 'error': '学生添加失败'}), 500
    except Exception as e:
        logger.error(f"添加学生失败: {str(e)}")
        return jsonify({'success': False, 'error': f'添加学生失败: {str(e)}'}), 500


@app.route('/api/classes/<int:class_id>', methods=['GET', 'PUT', 'DELETE'])
def api_class_manage(class_id):
    """获取、更新或删除班级"""
    if request.method == 'GET':
        grade = request.args.get('grade')
        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400

        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()

            cursor.execute('''
                SELECT id, class_name, grade, head_teacher, class_size, remark, create_time, class_type 
                FROM class 
                WHERE id = %s
            ''', (class_id,))

            result = cursor.fetchone()
            cursor.close()
            con.close()

            if not result:
                return jsonify({'success': False, 'error': '班级不存在'}), 404

            class_info = {
                'id': result[0],
                'class_name': result[1],
                'grade': result[2],
                'head_teacher': result[3] or '',
                'class_size': result[4] or 0,
                'remark': result[5] or '',
                'create_time': str(result[6]) if result[6] else '',
                'class_type': result[7] or ''
            }

            return jsonify({'success': True, 'class': class_info})

        except Exception as e:
            logger.error(f"获取班级信息失败: {str(e)}")
            return jsonify({'success': False, 'error': f'获取班级信息失败: {str(e)}'}), 500

    elif request.method == 'PUT':
        data = request.get_json()
        grade = data.get('grade')
        class_name = data.get('class_name')
        head_teacher = data.get('head_teacher', '')
        class_size = data.get('class_size', 0)
        remark = data.get('remark', '')
        class_type = data.get('class_type', '普通班级')
        subject_teachers = data.get('subject_teachers', {})  # { "化学": "张三", ... }
        if not grade or not class_name:
            return jsonify({'success': False, 'error': '年级和班级名称不能为空'}), 400
        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()
            # 检查班级名称是否与其他班级重复
            cursor.execute('SELECT id FROM class WHERE class_name = %s AND id != %s', (class_name, class_id))
            if cursor.fetchone():
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '班级名称已存在'}), 400
            # 更新班级信息
            cursor.execute('''
                UPDATE class 
                SET class_name = %s, head_teacher = %s, class_size = %s, remark = %s, class_type = %s
                WHERE id = %s
            ''', (class_name, head_teacher, class_size, remark, class_type, class_id))
            # 不要用 rowcount==0 判断不存在
            cursor.execute('SELECT id FROM class WHERE id = %s', (class_id,))
            if not cursor.fetchone():
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '班级不存在'}), 404
            # 1. 班主任唯一性
            if head_teacher:
                cursor.execute('UPDATE teachers SET manage_class = "0", is_head_teacher=0 WHERE manage_class = %s',
                               (class_name,))
                cursor.execute('UPDATE teachers SET manage_class = %s, is_head_teacher=1 WHERE name = %s',
                               (class_name, head_teacher))
            # 2. 科目-老师关系（只更新teachers表，不动grade_courses表）
            for subj, teacher in subject_teachers.items():
                if teacher:
                    # 先查出该科目下所有老师
                    cursor.execute('SELECT id, is_class_teacher FROM teachers WHERE subjects = %s', (subj,))
                    for t_id, t_classes in cursor.fetchall():
                        class_list = [c for c in (t_classes or '').split(',') if c and c != class_name]
                        # 如果不是当前选中老师，移除本班
                        if teacher != get_teacher_name_by_id(cursor, t_id):  # type: ignore
                            new_val = ','.join(class_list) if class_list else '0'
                            cursor.execute('UPDATE teachers SET is_class_teacher = %s WHERE id = %s', (new_val, t_id))
                    # 给当前选中老师追加本班
                    cursor.execute('SELECT is_class_teacher FROM teachers WHERE name = %s', (teacher,))
                    result = cursor.fetchone()
                    if result:
                        cur_val = result[0] or ''
                        cur_classes = set([c for c in cur_val.split(',') if c and c != '0'])
                        cur_classes.add(class_name)
                        new_val = ','.join(sorted(cur_classes))
                        cursor.execute('UPDATE teachers SET is_class_teacher = %s WHERE name = %s', (new_val, teacher))
            con.commit()
            cursor.close()
            con.close()
            return jsonify({'success': True, 'message': '班级更新成功'})
        except Exception as e:
            logger.error(f"更新班级失败: {str(e)}")
            return jsonify({'success': False, 'error': f'更新班级失败: {str(e)}'}), 500

    elif request.method == 'DELETE':
        grade = request.args.get('grade')
        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400

        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()

            # 检查班级是否存在
            cursor.execute('SELECT class_name FROM class WHERE id = %s', (class_id,))
            result = cursor.fetchone()
            if not result:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '班级不存在'}), 404

            class_name = result[0]

            # 检查是否有学生在该班级
            cursor.execute('SELECT COUNT(*) FROM students WHERE class_name = %s', (class_name,))
            student_count = cursor.fetchone()[0]
            if student_count > 0:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': f'该班级还有{student_count}名学生，无法删除'}), 400

            # 删除班级
            cursor.execute('DELETE FROM class WHERE id = %s', (class_id,))

            if cursor.rowcount == 0:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '删除失败'}), 500

            con.commit()
            cursor.close()
            con.close()

            return jsonify({'success': True, 'message': '班级删除成功'})

        except Exception as e:
            logger.error(f"删除班级失败: {str(e)}")
            return jsonify({'success': False, 'error': f'删除班级失败: {str(e)}'}), 500


# 管理员请假管理API
@app.route('/api/admin/leaves', methods=['GET'])
def api_admin_leaves():
    """管理员查看请假记录"""
    grade = request.args.get('grade')
    class_name = request.args.get('class_name')

    if not grade:
        return jsonify({'success': False, 'error': '缺少年级参数'}), 400

    try:
        if class_name:
            # 查看指定班级的请假记录
            records = get_class_leaves(grade, class_name)
        else:
            # 查看该年级下所有请假记录
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()

            cursor.execute('''
                SELECT id, student_id, name, class_name, start_date, end_date, reason, status, create_time 
                FROM leave_records 
                ORDER BY create_time DESC
            ''')

            records = cursor.fetchall()
            cursor.close()
            con.close()

        return jsonify({'success': True, 'data': [
            {
                'id': r[0],
                'student_id': r[1],
                'name': r[2],
                'class_name': r[3],
                'start_date': str(r[4]),
                'end_date': str(r[5]),
                'reason': r[6],
                'status': r[7],
                'create_time': str(r[8])
            } for r in records
        ]})

    except Exception as e:
        logger.error(f"获取请假记录失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取请假记录失败: {str(e)}'}), 500


# 课程管理API
@app.route('/api/courses', methods=['GET', 'POST'])
def api_courses():
    """课程管理API"""
    if request.method == 'GET':
        try:
            # 获取所有课程信息
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db('root')
            cursor = con.cursor()

            cursor.execute('''
                SELECT id, name, code, credit, type, description, create_time 
                FROM courses 
                ORDER BY name
            ''')

            courses = []
            for row in cursor.fetchall():
                courses.append({
                    'id': row[0],
                    'name': row[1],
                    'code': row[2] or '',
                    'credit': row[3] or 0,
                    'type': row[4] or '必修',
                    'description': row[5] or '',
                    'create_time': str(row[6]) if row[6] else ''
                })

            cursor.close()
            con.close()

            return jsonify({'success': True, 'courses': courses})

        except Exception as e:
            logger.error(f"获取课程列表失败: {str(e)}")
            return jsonify({'success': False, 'error': f'获取课程列表失败: {str(e)}'}), 500

    elif request.method == 'POST':
        data = request.get_json()
        name = data.get('name')
        code = data.get('code', '')
        credit = data.get('credit', 0)
        type_name = data.get('type', '必修')
        description = data.get('description', '')

        if not name:
            return jsonify({'success': False, 'error': '课程名称不能为空'}), 400

        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db('root')
            cursor = con.cursor()

            # 检查课程是否已存在
            cursor.execute('SELECT id FROM courses WHERE name = %s', (name,))
            if cursor.fetchone():
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '课程已存在'}), 400

            # 添加新课程
            cursor.execute('''
                INSERT INTO courses (name, code, credit, type, description, create_time)
                VALUES (%s, %s, %s, %s, %s, NOW())
            ''', (name, code, credit, type_name, description))

            con.commit()
            cursor.close()
            con.close()

            return jsonify({'success': True, 'message': '课程添加成功'})

        except Exception as e:
            logger.error(f"添加课程失败: {str(e)}")
            return jsonify({'success': False, 'error': f'添加课程失败: {str(e)}'}), 500


@app.route('/api/courses/<int:course_id>', methods=['PUT', 'DELETE'])
def api_course_manage(course_id):
    """更新或删除课程"""
    if request.method == 'PUT':
        data = request.get_json()
        name = data.get('name')
        code = data.get('code', '')
        credit = data.get('credit', 0)
        type_name = data.get('type', '必修')
        description = data.get('description', '')

        if not name:
            return jsonify({'success': False, 'error': '课程名称不能为空'}), 400

        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db('root')
            cursor = con.cursor()

            # 检查课程名称是否与其他课程重复
            cursor.execute('SELECT id FROM courses WHERE name = %s AND id != %s', (name, course_id))
            if cursor.fetchone():
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '课程名称已存在'}), 400

            # 更新课程信息
            cursor.execute('''
                UPDATE courses 
                SET name = %s, code = %s, credit = %s, type = %s, description = %s
                WHERE id = %s
            ''', (name, code, credit, type_name, description, course_id))

            if cursor.rowcount == 0:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '课程不存在'}), 404

            con.commit()
            cursor.close()
            con.close()

            return jsonify({'success': True, 'message': '课程更新成功'})

        except Exception as e:
            logger.error(f"更新课程失败: {str(e)}")
            return jsonify({'success': False, 'error': f'更新课程失败: {str(e)}'}), 500

    elif request.method == 'DELETE':
        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db('root')
            cursor = con.cursor()

            # 检查课程是否存在
            cursor.execute('SELECT name FROM courses WHERE id = %s', (course_id,))
            result = cursor.fetchone()
            if not result:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '课程不存在'}), 404

            # 删除课程
            cursor.execute('DELETE FROM courses WHERE id = %s', (course_id,))

            if cursor.rowcount == 0:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '删除失败'}), 500

            con.commit()
            cursor.close()
            con.close()

            return jsonify({'success': True, 'message': '课程删除成功'})

        except Exception as e:
            logger.error(f"删除课程失败: {str(e)}")
            return jsonify({'success': False, 'error': f'删除课程失败: {str(e)}'}), 500


# 年级课程管理API
@app.route('/api/grade-courses', methods=['GET'])
def api_grade_courses():
    """获取指定年级的课程信息"""
    grade = request.args.get('grade')
    if not grade:
        return jsonify({'success': False, 'error': '缺少年级参数'}), 400

    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()

        cursor.execute('''
            SELECT id, course_name, course_code, credit, type, full_mark, teacher_names, create_time
            FROM grade_courses
            ORDER BY course_name
        ''')

        courses = []
        for row in cursor.fetchall():
            courses.append({
                'id': row[0],
                'course_name': row[1],
                'course_code': row[2],
                'credit': float(row[3]) if row[3] else 15.0,
                'type': row[4] or '必修',
                'full_mark': float(row[5]) if row[5] else 100.0,
                'teacher_names': row[6] or '',
                'create_time': str(row[7]) if row[7] else ''
            })

        cursor.close()
        con.close()

        return jsonify({'success': True, 'courses': courses})

    except Exception as e:
        logger.error(f"获取年级课程信息失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取课程信息失败: {str(e)}'}), 500


@app.route('/api/subjects', methods=['GET', 'POST'])
def api_subjects():
    """科目管理API - 获取和新增科目"""
    if request.method == 'GET':
        grade = request.args.get('grade')
        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400

        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()

            # 从grade_courses表获取科目列表，包含完整信息
            cursor.execute('''
                SELECT course_name, course_code, credit, type, full_mark, teacher_names
                FROM grade_courses
                ORDER BY course_name
            ''')

            subjects = []
            for row in cursor.fetchall():
                subjects.append({
                    'name': row[0],
                    'code': row[1] or '',
                    'credit': float(row[2]) if row[2] else 15.0,
                    'type': row[3] or '必修',
                    'full_mark': float(row[4]) if row[4] else 100.0,
                    'teacher_names': row[5] or ''
                })

            cursor.close()
            con.close()

            return jsonify({'success': True, 'subjects': subjects})

        except Exception as e:
            logger.error(f"获取科目列表失败: {str(e)}")
            return jsonify({'success': False, 'error': f'获取科目列表失败: {str(e)}'}), 500

    elif request.method == 'POST':
        # 新增科目
        data = request.get_json()
        grade = data.get('grade')
        course_name = data.get('course_name')
        credit = data.get('credit')
        full_mark = data.get('full_mark')
        course_type = data.get('type', '必修')

        # 验证必填字段
        if not grade:
            return jsonify({'success': False, 'error': '年级不能为空'}), 400
        if not course_name:
            return jsonify({'success': False, 'error': '课程名称不能为空'}), 400
        if credit is None or credit == '':
            return jsonify({'success': False, 'error': '课程学分不能为空'}), 400
        if full_mark is None or full_mark == '':
            return jsonify({'success': False, 'error': '课程满分不能为空'}), 400

        # 验证数据类型和范围
        try:
            credit = float(credit)
            if credit <= 0 or credit > 999:
                return jsonify({'success': False, 'error': '课程学分必须在0-999之间'}), 400
        except (ValueError, TypeError):
            return jsonify({'success': False, 'error': '课程学分必须是有效数字'}), 400

        try:
            full_mark = float(full_mark)
            if full_mark <= 0 or full_mark > 9999:
                return jsonify({'success': False, 'error': '课程满分必须在0-9999之间'}), 400
        except (ValueError, TypeError):
            return jsonify({'success': False, 'error': '课程满分必须是有效数字'}), 400

        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()

            # 检查课程是否已存在
            cursor.execute('SELECT id FROM grade_courses WHERE course_name = %s', (course_name,))
            if cursor.fetchone():
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '课程已存在'}), 400

            # 使用已有的随机生成课程代码函数
            from backend.means import generate_random_course_code
            course_code = generate_random_course_code(course_name)

            # 添加新课程，包含所有必填字段
            cursor.execute('''
                INSERT INTO grade_courses (course_name, course_code, credit, type, full_mark, teacher_names, create_time)
                VALUES (%s, %s, %s, %s, %s, %s, NOW())
            ''', (course_name, course_code, credit, course_type, full_mark, ''))

            con.commit()
            cursor.close()
            con.close()

            return jsonify({'success': True, 'message': '课程添加成功'})

        except Exception as e:
            logger.error(f"添加课程失败: {str(e)}")
            return jsonify({'success': False, 'error': f'添加课程失败: {str(e)}'}), 500


@app.route('/api/grade-courses', methods=['POST'])
def api_add_grade_course():
    """为指定年级添加课程"""
    data = request.get_json()
    grade = data.get('grade')
    course_name = data.get('course_name')
    credit = data.get('credit')
    full_mark = data.get('full_mark')
    type_name = data.get('type', '必修')
    teacher_names = data.get('teacher_names', '')

    # 验证必填字段
    if not grade:
        return jsonify({'success': False, 'error': '年级不能为空'}), 400
    if not course_name:
        return jsonify({'success': False, 'error': '课程名称不能为空'}), 400
    if credit is None or credit == '':
        return jsonify({'success': False, 'error': '课程学分不能为空'}), 400
    if full_mark is None or full_mark == '':
        return jsonify({'success': False, 'error': '课程满分不能为空'}), 400

    # 验证数据类型和范围
    try:
        credit = float(credit)
        if credit <= 0 or credit > 999:
            return jsonify({'success': False, 'error': '课程学分必须在0-999之间'}), 400
    except (ValueError, TypeError):
        return jsonify({'success': False, 'error': '课程学分必须是有效数字'}), 400

    try:
        full_mark = float(full_mark)
        if full_mark <= 0 or full_mark > 9999:
            return jsonify({'success': False, 'error': '课程满分必须在0-9999之间'}), 400
    except (ValueError, TypeError):
        return jsonify({'success': False, 'error': '课程满分必须是有效数字'}), 400

    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()

        # 检查课程是否已存在
        cursor.execute('SELECT id FROM grade_courses WHERE course_name = %s', (course_name,))
        if cursor.fetchone():
            cursor.close()
            con.close()
            return jsonify({'success': False, 'error': '课程名称已存在'}), 400

        # 自动生成课程代码
        from backend.means import generate_random_course_code
        course_code = generate_random_course_code(course_name)

        # 添加新课程，包含所有必填字段
        cursor.execute('''
            INSERT INTO grade_courses (course_name, course_code, credit, type, full_mark, teacher_names, create_time)
            VALUES (%s, %s, %s, %s, %s, %s, NOW())
        ''', (course_name, course_code, credit, type_name, full_mark, teacher_names))

        con.commit()
        cursor.close()
        con.close()

        return jsonify({'success': True, 'message': '课程添加成功'})

    except Exception as e:
        logger.error(f"添加年级课程失败: {str(e)}")
        return jsonify({'success': False, 'error': f'添加课程失败: {str(e)}'}), 500


@app.route('/api/grade-courses/<int:course_id>', methods=['PUT', 'DELETE'])
def api_grade_course_manage(course_id):
    """更新或删除年级课程"""
    if request.method == 'PUT':
        data = request.get_json()
        grade = data.get('grade')
        course_name = data.get('course_name')
        credit = data.get('credit')
        full_mark = data.get('full_mark')
        type_name = data.get('type', '必修')
        teacher_names = data.get('teacher_names', '')

        # 验证必填字段
        if not grade:
            return jsonify({'success': False, 'error': '年级不能为空'}), 400
        if not course_name:
            return jsonify({'success': False, 'error': '课程名称不能为空'}), 400
        if credit is None or credit == '':
            return jsonify({'success': False, 'error': '课程学分不能为空'}), 400
        if full_mark is None or full_mark == '':
            return jsonify({'success': False, 'error': '课程满分不能为空'}), 400

        # 验证数据类型和范围
        try:
            credit = float(credit)
            if credit <= 0 or credit > 999:
                return jsonify({'success': False, 'error': '课程学分必须在0-999之间'}), 400
        except (ValueError, TypeError):
            return jsonify({'success': False, 'error': '课程学分必须是有效数字'}), 400

        try:
            full_mark = float(full_mark)
            if full_mark <= 0 or full_mark > 9999:
                return jsonify({'success': False, 'error': '课程满分必须在0-9999之间'}), 400
        except (ValueError, TypeError):
            return jsonify({'success': False, 'error': '课程满分必须是有效数字'}), 400

        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()

            # 检查课程名称是否与其他课程重复
            cursor.execute('SELECT id FROM grade_courses WHERE course_name = %s AND id != %s',
                           (course_name, course_id))
            if cursor.fetchone():
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '课程名称已存在'}), 400

            # 获取当前课程的编码，如果不存在则生成新的
            cursor.execute('SELECT course_code FROM grade_courses WHERE id = %s', (course_id,))
            result = cursor.fetchone()
            if result and result[0]:
                course_code = result[0]  # 保持原有编码
            else:
                # 如果没有编码，生成新的
                from backend.means import generate_random_course_code
                course_code = generate_random_course_code(course_name)

            # 更新课程信息，包含所有字段
            cursor.execute('''
                UPDATE grade_courses
                SET course_name = %s, course_code = %s, credit = %s, type = %s, full_mark = %s, teacher_names = %s
                WHERE id = %s
            ''', (course_name, course_code, credit, type_name, full_mark, teacher_names, course_id))

            if cursor.rowcount == 0:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '课程不存在'}), 404

            con.commit()
            cursor.close()
            con.close()

            return jsonify({'success': True, 'message': '课程更新成功'})

        except Exception as e:
            logger.error(f"更新年级课程失败: {str(e)}")
            return jsonify({'success': False, 'error': f'更新课程失败: {str(e)}'}), 500

    elif request.method == 'DELETE':
        grade = request.args.get('grade')
        if not grade:
            return jsonify({'success': False, 'error': '缺少年级参数'}), 400

        try:
            con = Connect(**ACCOUNT_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()

            # 检查课程是否存在
            cursor.execute('SELECT course_name FROM grade_courses WHERE id = %s', (course_id,))
            result = cursor.fetchone()
            if not result:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '课程不存在'}), 404

            # 删除课程
            cursor.execute('DELETE FROM grade_courses WHERE id = %s', (course_id,))

            if cursor.rowcount == 0:
                cursor.close()
                con.close()
                return jsonify({'success': False, 'error': '删除失败'}), 500

            con.commit()
            cursor.close()
            con.close()

            return jsonify({'success': True, 'message': '课程删除成功'})

        except Exception as e:
            logger.error(f"删除年级课程失败: {str(e)}")
            return jsonify({'success': False, 'error': f'删除课程失败: {str(e)}'}), 500


@app.route('/api/grade-courses/<int:course_id>/teachers', methods=['PUT'])
def api_update_course_teachers(course_id):
    """只更新课程的任课老师信息"""
    data = request.get_json()
    grade = data.get('grade')
    teacher_names = data.get('teacher_names', '')

    if not grade:
        return jsonify({'success': False, 'error': '缺少年级参数'}), 400

    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()

        # 检查课程是否存在
        cursor.execute('SELECT course_name FROM grade_courses WHERE id = %s', (course_id,))
        result = cursor.fetchone()
        if not result:
            cursor.close()
            con.close()
            return jsonify({'success': False, 'error': '课程不存在'}), 404

        # 只更新任课老师信息
        cursor.execute('''
            UPDATE grade_courses 
            SET teacher_names = %s
            WHERE id = %s
        ''', (teacher_names, course_id))

        if cursor.rowcount == 0:
            cursor.close()
            con.close()
            return jsonify({'success': False, 'error': '更新失败'}), 500

        con.commit()
        cursor.close()
        con.close()

        return jsonify({'success': True, 'message': '任课老师更新成功'})

    except Exception as e:
        logger.error(f"更新课程任课老师失败: {str(e)}")
        return jsonify({'success': False, 'error': f'更新任课老师失败: {str(e)}'}), 500


@app.route('/api/class-teachers', methods=['GET'])
def api_class_teachers():
    """获取指定班级的班主任和任课老师信息（每科只返回一个老师，直接查 teachers 表）"""
    grade = request.args.get('grade')
    class_name = request.args.get('class_name')
    if not grade or not class_name:
        return jsonify({'success': False, 'error': '缺少年级或班级参数'}), 400
    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()
        # 获取班主任信息
        cursor.execute('SELECT head_teacher FROM class WHERE class_name = %s', (class_name,))
        head_teacher_result = cursor.fetchone()
        head_teacher = head_teacher_result[0] if head_teacher_result else ''
        head_teacher_info = None
        if head_teacher:
            cursor.execute('''
                SELECT id, name, username, phone, subjects, is_head_teacher, manage_class, is_class_teacher
                FROM teachers WHERE name = %s
            ''', (head_teacher,))
            row = cursor.fetchone()
            if row:
                head_teacher_info = {
                    'id': row[0],
                    'name': row[1],
                    'username': row[2],
                    'phone': row[3] or '',
                    'subjects': row[4] or '',
                    'is_head_teacher': row[5],
                    'manage_class': row[6],
                    'is_class_teacher': row[7]
                }
        # 直接查 teachers 表，is_class_teacher=当前班级
        cursor.execute('''
            SELECT id, name, username, phone, subjects, is_head_teacher, manage_class, is_class_teacher
            FROM teachers WHERE FIND_IN_SET(%s, is_class_teacher)
        ''', (class_name,))
        subject_teachers = []
        for row in cursor.fetchall():
            subject_teachers.append({
                'id': row[0],
                'name': row[1],
                'username': row[2],
                'phone': row[3] or '',
                'subjects': row[4] or '',
                'is_head_teacher': row[5],
                'manage_class': row[6],
                'is_class_teacher': row[7],
                'course_name': row[4] or ''
            })
        cursor.close()
        con.close()
        # 返回时subject_teachers为[{course_name, name}]
        subject_teachers_list = [
            {'course_name': t['course_name'], 'name': t['name']} for t in subject_teachers
        ]
        return jsonify({
            'success': True,
            'class_name': class_name,
            'head_teacher': head_teacher_info,
            'subject_teachers': subject_teachers_list
        })
    except Exception as e:
        logger.error(f"获取班级教师信息失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取班级教师信息失败: {str(e)}'}), 500


@app.route('/api/grade-exam-averages', methods=['GET'])
def get_grade_exam_averages():
    """
    获取指定年级下所有考试的年级平均分，并生成可视化图表
    """
    grade = request.args.get('grade')
    if not grade:
        return jsonify({'success': False, 'error': '缺少年级参数'}), 400

    try:
        exam_list = get_exam_list_by_grade(grade)
        if not exam_list:
            return jsonify({'success': False, 'error': '未找到考试数据'}), 404

        from backend.visualization import create_radar_chart, create_bar_chart, create_grade_average_table_chart
        import os
        import pandas as pd

        averages = []
        charts = {}
        for exam in exam_list:
            # 获取所有班级的总分
            with get_db_connection() as (con, cursor):
                con.select_db(grade)
                cursor.execute("SHOW TABLES")
                tables = [row[0] for row in cursor.fetchall()]
                class_tables = [t for t in tables if
                                t not in ('class', 'exam', 'students', 'teachers', 'grade_courses')]
                total_scores = []
                subjects = get_courses_list(grade)
                class_avg_data = {subj: 0 for subj in subjects}
                class_count = 0
                for class_name in class_tables:
                    try:
                        cursor.execute(f"SELECT * FROM `{exam}` WHERE class_name=%s", (class_name,))
                        results = cursor.fetchall()
                        if not results:
                            continue
                        columns = [desc[0] for desc in cursor.description]
                        df = pd.DataFrame(results, columns=columns)
                        total_scores.extend([float(score) for score in df['总分'].dropna().tolist()])
                        for subj in subjects:
                            class_avg_data[subj] += float(df[subj].astype(float).mean())
                        class_count += 1
                    except Exception:
                        continue
                avg_score = round(sum(total_scores) / len(total_scores), 1) if total_scores else 0
                averages.append({'exam': exam, 'avg_score': avg_score})
                # 计算年级平均分（各科平均）
                if class_count > 0:
                    for subj in subjects:
                        class_avg_data[subj] = round(class_avg_data[subj] / class_count, 1)
                # 添加总分字段
                class_avg_data['总分'] = round(sum(total_scores) / len(total_scores), 1) if total_scores else 0
                # 添加学号和姓名字段
                class_avg_data['学号'] = f"{grade}年级"
                class_avg_data['姓名'] = exam
                # 生成可视化图表 - 使用新的目录结构
                # 从数据库获取满分数据
                full_marks = get_full_marks(exam, grade)
                # 年级平均分保存在 年级/考试/年级平均分/ 目录下
                radar_path = create_radar_chart(class_avg_data, full_marks, grade, exam, None, None, f"{grade}年级",
                                                subjects, 'grade_avg')
                bar_path = create_bar_chart(class_avg_data, class_avg_data, grade, exam, None, None, f"{grade}年级",
                                            subjects, 'grade_avg', full_marks)
                table_path = create_grade_average_table_chart(class_avg_data, grade, exam, subjects)
                charts[exam] = {
                    'radar': os.path.relpath(radar_path, IMAGES_FOLDER).replace('\\', '/'),
                    'bar': os.path.relpath(bar_path, IMAGES_FOLDER).replace('\\', '/'),
                    'table': os.path.relpath(table_path, IMAGES_FOLDER).replace('\\', '/')
                }
        return jsonify({'success': True, 'averages': averages, 'charts': charts})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


# ==================== 年级平均分API ====================
@app.route('/api/grade-exam-average', methods=['GET'])
def api_grade_exam_average():
    """获取指定年级、考试的年级平均分和图表"""
    grade = request.args.get('grade')
    exam = request.args.get('exam')

    if not grade or not exam:
        return jsonify({'success': False, 'error': '缺少年级或考试参数'}), 400

    # 路径安全处理
    safe_grade = safe_filename(grade)
    safe_exam = safe_filename(exam)

    # 检查安全文件名是否合法
    if not safe_grade or not safe_exam:
        return jsonify({
            'success': False,
            'error': f'年级或考试名包含非法字符: grade={grade}, exam={exam}'
        }), 400

    try:
        avg_data = _calculate_grade_average(grade, exam)
        charts = _generate_grade_charts(avg_data, safe_grade, safe_exam)

        return jsonify({
            'success': True,
            'avg_data': avg_data,
            'charts': charts
        })

    except Exception as e:
        logger.error(f"获取年级平均分失败: {repr(e)}")
        return jsonify({
            'success': False,
            'error': f'{repr(e)}',
            'grade': grade,
            'exam': exam
        }), 500


def _calculate_grade_average(grade, exam):
    """计算年级平均分"""
    with get_db_connection() as (_, cursor):
        cursor.execute("USE `{}`".format(grade))
        cursor.execute(f"SELECT * FROM `{exam}`")
        results = cursor.fetchall()

        subjects = get_courses_list(grade)

        if not results:
            avg_data = {subj: 0 for subj in subjects}
            avg_data.update({
                '总分': 0,
                '统计班级数': 0,
                '统计人数': 0,
                '学号': f"{grade}年级",
                '姓名': exam
            })
            return avg_data

        # 使用pandas计算平均分
        columns = [desc[0] for desc in cursor.description]
        df = pd.DataFrame(results, columns=columns)
        classes = df['class_name'].unique()

        avg_data = {}
        for subj in subjects:
            # 按班级分组计算平均分，再计算年级平均分
            group_means = df.groupby('class_name')[subj].apply(lambda x: x.astype(float).mean())
            avg_data[subj] = round(float(group_means.mean()), 1)

        avg_data.update({
            '总分': round(float(df['总分'].astype(float).mean()), 1),
            '统计班级数': len(classes),
            '统计人数': len(df),
            '学号': f"{grade}年级",
            '姓名': exam
        })

        return avg_data


def _generate_grade_charts(avg_data, safe_grade, safe_exam):
    """生成年级平均分图表"""
    from backend.visualization import create_radar_chart, create_bar_chart, create_grade_average_table_chart
    from backend.MySQL_to_pythonr import get_courses_list, get_full_marks

    subjects = get_courses_list(safe_grade)
    full_marks = get_full_marks(safe_exam, safe_grade)

    # 生成图表
    radar_path = create_radar_chart(
        avg_data, full_marks, safe_grade, safe_exam, None, None,
        f"{safe_grade}年级", subjects, 'grade_avg'
    )
    bar_path = create_bar_chart(
        avg_data, avg_data, safe_grade, safe_exam, None, None,
        f"{safe_grade}年级", subjects, 'grade_avg', full_marks
    )
    table_path = create_grade_average_table_chart(avg_data, safe_grade, safe_exam, subjects)

    return {
        'radar': os.path.relpath(radar_path, IMAGES_FOLDER).replace('\\', '/'),
        'bar': os.path.relpath(bar_path, IMAGES_FOLDER).replace('\\', '/'),
        'table': os.path.relpath(table_path, IMAGES_FOLDER).replace('\\', '/')
    }


@app.route('/api/subject-teachers', methods=['GET'])
def api_subject_teachers():
    """
    获取指定年级、科目的所有教师（一个教师只教一个科目）
    """
    grade = request.args.get('grade')
    subject = request.args.get('subject')
    if not grade or not subject:
        return jsonify({'success': False, 'error': '缺少年级或科目参数'}), 400
    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()
        # 只查subjects字段等于该科目的教师
        cursor.execute('''
            SELECT id, name, username, phone, subjects, is_head_teacher, manage_class, is_class_teacher
            FROM teachers
            WHERE subjects = %s
        ''', (subject,))
        teachers = []
        for row in cursor.fetchall():
            teachers.append({
                'id': row[0],
                'name': row[1],
                'username': row[2],
                'phone': row[3] or '',
                'subjects': row[4] or '',
                'is_head_teacher': row[5],
                'manage_class': row[6],
                'is_class_teacher': row[7]
            })
        cursor.close()
        con.close()
        return jsonify({'success': True, 'teachers': teachers})
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取任课老师失败: {str(e)}'}), 500


logger.info("应用启动完成，数据库连接正常")





@app.route('/api/update_student_score', methods=['POST'])
def api_update_student_score():
    """更新学生成绩API"""
    try:
        data = request.get_json()
        grade = data.get('grade')
        exam = data.get('exam')
        class_name = data.get('class_name')
        student_id = data.get('student_id')
        scores = data.get('scores')

        if not all([grade, exam, class_name, student_id, scores]):
            return jsonify({'success': False, 'error': '参数不完整'}), 400

        # 验证成绩数据
        subjects = get_courses_list(grade)
        for subject in subjects:
            if subject not in scores:
                return jsonify({'success': False, 'error': f'缺少{subject}成绩'}), 400
            score = scores[subject]
            if not isinstance(score, (int, float)) or score < 0:
                return jsonify({'success': False, 'error': f'{subject}成绩格式错误'}), 400

        # 更新数据库
        with get_db_connection() as (con, cursor):
            con.select_db(grade)

            # 检查学生是否存在
            cursor.execute(f"SELECT id FROM `{exam}` WHERE student_id = %s AND class_name = %s",
                           (student_id, class_name))
            student_row = cursor.fetchone()
            if not student_row:
                return jsonify({'success': False, 'error': '未找到该学生'}), 404

            # 构建更新SQL
            update_fields = []
            update_values = []

            for subject in subjects:
                update_fields.append(f"`{subject}` = %s")
                update_values.append(scores[subject])

            # 添加总分
            update_fields.append("`总分` = %s")
            update_values.append(scores['总分'])

            # 添加WHERE条件
            update_values.append(student_id)
            update_values.append(class_name)

            sql = f"UPDATE `{exam}` SET {', '.join(update_fields)} WHERE student_id = %s AND class_name = %s"
            cursor.execute(sql, update_values)

            # 重新计算排名
            update_class_rankings(grade, exam, class_name, cursor)

            con.commit()

            # 删除相关的图表文件
            delete_chart_files(grade, exam, class_name, student_id)

            logger.info(f"成功更新学生 {student_id} 在 {grade} {exam} {class_name} 的成绩")
            return jsonify({'success': True, 'message': '成绩更新成功'})

    except Exception as e:
        logger.error(f"更新学生成绩失败: {str(e)}")
        return jsonify({'success': False, 'error': f'更新失败: {str(e)}'}), 500





# ==================== 应用启动 ====================
logger.info("应用启动完成，数据库连接正常")

if __name__ == '__main__':
    # 直接运行 Flask 服务，用户可手动在浏览器访问 http://127.0.0.1:5000
    app.run(debug=False, port=5000)
