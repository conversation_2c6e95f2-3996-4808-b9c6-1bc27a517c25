/* =====================
   登录页专用样式（log.css）
   ===================== */

/* ========== 1. 页面基础设置 ========== */
body.login-page {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-x: hidden;
}

/* ========== 2. 登录容器 ========== */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 20px;
}

/* 背景装饰元素 */
.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.3;
    pointer-events: none;
}

/* ========== 3. 登录卡片 ========== */
.login-card {
    width: 100%;
    max-width: 420px;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    animation: slideInUp 0.6s ease-out;
}

.login-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.2), 0 0 0 1px rgba(255,255,255,0.15);
}

/* ========== 4. 登录页头部 ========== */
.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.login-header .logo-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color, #527aaf) 0%, #0a58ca 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 20px rgba(82, 122, 175, 0.3);
    transition: transform 0.3s ease;
}

.login-header .logo-icon:hover {
    transform: scale(1.05);
}

.login-header .logo-icon i {
    font-size: 2.5rem;
    color: white;
}

.login-header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    letter-spacing: -0.5px;
}

.login-header p {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
}

/* ========== 5. 登录表单 ========== */
.login-form {
    margin-bottom: 1.5rem;
}

.form-floating {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-floating .form-control {
    height: 58px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    padding: 1rem 0.75rem 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.form-floating .form-control:focus {
    border-color: var(--primary-color, #527aaf);
    box-shadow: 0 0 0 0.2rem rgba(82, 122, 175, 0.25);
    background: white;
    animation: inputFocus 0.3s ease-out;
}

.form-floating label {
    color: #6c757d;
    font-weight: 500;
}

/* 输入框图标 */
.form-floating::before {
    content: '';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.5;
    z-index: 10;
}

.form-floating.username::before {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 16"><path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10z"/></svg>');
}

.form-floating.password::before {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 16"><path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/></svg>');
}

/* 角色选择组件 */
.role-selection {
    margin-bottom: 1.5rem;
}

.role-selection > label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
    display: block;
}

.role-options {
    display: flex;
    gap: 0.75rem;
}

.role-option {
    flex: 1;
    position: relative;
}

.role-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.role-option label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    font-weight: 500;
    margin: 0;
}

.role-option label:hover {
    border-color: var(--primary-color, #527aaf);
    background: rgba(82, 122, 175, 0.05);
}

.role-option input[type="radio"]:checked + label {
    border-color: var(--primary-color, #527aaf);
    background: rgba(82, 122, 175, 0.1);
    color: var(--primary-color, #527aaf);
}

/* ========== 6. 记住密码选项 ========== */
.remember-me {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.remember-me input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color, #527aaf);
}

.remember-me label {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
    cursor: pointer;
}

/* ========== 7. 登录按钮 ========== */
.login-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color, #527aaf) 0%, #0a58ca 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

/* 按钮交互状态 */
.login-btn:hover:not(:disabled):not(.loading) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(82, 122, 175, 0.4);
}

.login-btn:active:not(:disabled):not(.loading) {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 按钮加载状态 */
.login-btn.loading {
    pointer-events: none;
}

.login-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}



/* ========== 8. 忘记密码链接 ========== */
.forgot-password {
    text-align: center;
    margin-bottom: 1.5rem;
}

.forgot-password a {
    color: var(--primary-color, #527aaf);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password a:hover {
    color: #0a58ca;
    text-decoration: underline;
}

/* ========== 9. 登录页底部 ========== */
.login-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.login-footer p {
    color: #6c757d;
    font-size: 0.85rem;
    margin: 0.5rem 0;
}

.login-footer .version {
    font-size: 0.8rem;
    color: #adb5bd;
}

/* ========== 10. 登录页面特定消息提示 ========== */
/* 继承public.css中的基础提示样式，这里只定义登录页面特有的提示样式 */
.alert {
    border-radius: 10px;
    margin-bottom: 1.5rem;
    padding: 1rem;
    font-size: 0.9rem;
    animation: slideInUp 0.4s ease-out;
}

/* ========== 11. 登录页面特定动画效果 ========== */
/* 继承public.css中的基础动画，这里只定义登录页面特有的动画 */

/* 输入框聚焦动画 */
@keyframes inputFocus {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
    }
}

/* 按钮加载动画 */
@keyframes spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* ========== 12. 响应式设计 ========== */
/* 平板设备 (768px 及以下) */
@media (max-width: 768px) {
    .login-container {
        padding: 15px;
    }

    .login-card {
        max-width: 100%;
        padding: 2rem;
        margin: 0 auto;
    }

    .login-header .logo-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 1rem;
    }

    .login-header .logo-icon i {
        font-size: 2rem;
    }

    .login-header h1 {
        font-size: 1.6rem;
    }

    .role-options {
        flex-direction: column;
        gap: 0.5rem;
    }

    .role-option label {
        padding: 0.6rem;
    }
}

/* 手机设备 (480px 及以下) */
@media (max-width: 480px) {
    .login-container {
        padding: 10px;
    }

    .login-card {
        padding: 1.5rem;
        border-radius: 15px;
    }

    .login-header .logo-icon {
        width: 60px;
        height: 60px;
    }

    .login-header .logo-icon i {
        font-size: 1.8rem;
    }

    .login-header h1 {
        font-size: 1.4rem;
        margin-bottom: 0.25rem;
    }

    .login-header p {
        font-size: 0.9rem;
    }

    .form-floating .form-control {
        height: 52px;
        font-size: 0.95rem;
    }

    .login-btn {
        padding: 0.9rem;
        font-size: 1rem;
    }

    .alert {
        padding: 0.8rem;
        font-size: 0.85rem;
    }
}
