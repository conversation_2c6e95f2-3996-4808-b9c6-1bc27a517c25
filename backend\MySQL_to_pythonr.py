# ==================== 数据库操作集中模块 ====================
"""
数据库操作集中模块 - 将mysql_python.py和mysql_python_account.py中的数据库读取操作集中管理
包含考试数据库和账户数据库的所有读取操作
"""

import logging
from contextlib import contextmanager
from typing import Optional, Tuple, Any, List, Dict

from pymysql import Connect, Connection, OperationalError
from pymysql.cursors import Cursor

from backend.config import EXAM_DB_CONFIG, ACCOUNT_DB_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# ==================== 考试数据库连接管理 ====================
@contextmanager
def get_exam_db_connection(db_name: Optional[str] = None) -> Tuple[Connection, Cursor]:  # type: ignore
    """考试数据库连接上下文管理器"""
    con = None
    try:
        logger.debug(f"Attempting to connect to exam database: {db_name if db_name else 'default'}")
        con = Connect(**EXAM_DB_CONFIG)
        if db_name:
            con.select_db(db_name)
        cursor = con.cursor()
        logger.debug("Exam database connection successful")
        yield con, cursor
    except OperationalError as e:
        error_msg = f"Exam database connection error: {str(e)}"
        logger.error(error_msg)
        if "Can't connect to MySQL server" in str(e):
            raise Exception("无法连接到数据库服务器，请检查MySQL服务是否启动")
        elif "Unknown database" in str(e):
            raise Exception(f"数据库 {db_name} 不存在")
        else:
            raise Exception(f"数据库连接错误: {str(e)}")
    except Exception as e:
        error_msg = f"Unexpected exam database error: {str(e)}"
        logger.error(error_msg)
        raise Exception(f"数据库操作错误: {str(e)}")
    finally:
        if con:
            con.close()
            logger.debug("Exam database connection closed")


@contextmanager
def get_account_db_connection(db_name: Optional[str] = None) -> Tuple[Connection, Cursor]:  # type: ignore
    """账户数据库连接上下文管理器"""
    con = None
    try:
        logger.debug(f"Attempting to connect to account database: {db_name if db_name else 'account_db'}")
        con = Connect(**ACCOUNT_DB_CONFIG)
        if db_name:
            con.select_db(db_name)
        else:
            con.select_db('account_db')
        cursor = con.cursor()
        logger.debug("Account database connection successful")
        yield con, cursor
    except OperationalError as e:
        error_msg = f"Account database connection error: {str(e)}"
        logger.error(error_msg)
        if "Can't connect to MySQL server" in str(e):
            raise Exception("无法连接到账户数据库服务器，请检查MySQL服务是否启动")
        elif "Unknown database" in str(e):
            raise Exception(f"账户数据库 {db_name} 不存在")
        else:
            raise Exception(f"账户数据库连接错误: {str(e)}")
    except Exception as e:
        error_msg = f"Unexpected account database error: {str(e)}"
        logger.error(error_msg)
        raise Exception(f"账户数据库操作错误: {str(e)}")
    finally:
        if con:
            con.close()
            logger.debug("Account database connection closed")


def connect_exam_database(sheet_name: str) -> Tuple[Connection, Cursor]:
    """连接到指定考试数据库（非上下文管理器版本）"""
    try:
        logger.debug(f"Connecting to exam database: {sheet_name}")
        con = Connect(**EXAM_DB_CONFIG)
        con.select_db(sheet_name)
        cursor = con.cursor()
        logger.debug(f"Successfully connected to database: {sheet_name}")
        return con, cursor
    except OperationalError as e:
        error_msg = f"Error connecting to database {sheet_name}: {str(e)}"
        logger.error(error_msg)
        if "Can't connect to MySQL server" in str(e):
            raise Exception("无法连接到数据库服务器，请检查MySQL服务是否启动")
        elif "Unknown database" in str(e):
            raise Exception(f"考试数据库 {sheet_name} 不存在")
        else:
            raise Exception(f"数据库连接错误: {str(e)}")
    except Exception as e:
        error_msg = f"Unexpected error connecting to database {sheet_name}: {str(e)}"
        logger.error(error_msg)
        raise Exception(f"数据库操作错误: {str(e)}")


def connect_account_database_with_grade(grade: str) -> Tuple[Connection, Cursor]:
    """连接到指定年级的账户数据库（非上下文管理器版本）"""
    try:
        logger.debug(f"Connecting to account database for grade: {grade}")
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()
        logger.debug(f"Successfully connected to account database: {grade}")
        return con, cursor
    except Exception as e:
        logger.error(f"Database connection error for grade {grade}: {str(e)}")
        raise


# ==================== 年级、考试、班级查询 ====================
def get_grade_list() -> List[str]:
    """获取所有年级（数据库）列表"""
    try:
        logger.debug("Fetching grade list (databases)")
        with get_exam_db_connection() as (con, cursor):
            cursor.execute("SHOW DATABASES")
            all_dbs = cursor.fetchall()
            # 过滤掉系统数据库和账户数据库
            grades = [
                db[0] for db in all_dbs
                if db[0] not in ['information_schema', 'mysql', 'performance_schema', 'sys', 'account_db', 'root']
            ]
            logger.debug(f"Found grades: {grades}")
            return grades
    except Exception as e:
        logger.error(f"Error getting grade list: {str(e)}")
        raise Exception("获取年级列表失败，请检查数据库连接")


def get_exam_list(grade: str) -> List[str]:
    """获取指定年级下所有考试（表名）"""
    try:
        logger.debug(f"Fetching exam list (tables) for grade: {grade}")
        with get_exam_db_connection(grade) as (con, cursor):
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            exams = [table[0] for table in tables if table[0] not in ['class_list', 'exam_list']]
            logger.debug(f"Found exams: {exams}")
            return exams
    except Exception as e:
        logger.error(f"Error getting exam list for grade {grade}: {str(e)}")
        raise Exception(f"获取考试列表失败: {str(e)}")


def get_class_list_from_exam(grade: str, exam: str) -> List[str]:
    """获取指定年级和考试下所有班级（表内distinct class_name字段）"""
    try:
        logger.debug(f"Fetching class list for grade: {grade}, exam: {exam}")
        with get_exam_db_connection(grade) as (con, cursor):
            cursor.execute(f"SELECT DISTINCT class_name FROM `{exam}`")
            results = cursor.fetchall()
            classes = [row[0] for row in results]
            logger.debug(f"Found classes: {classes}")
            return classes
    except Exception as e:
        logger.error(f"Error getting class list for grade {grade}, exam {exam}: {str(e)}")
        raise Exception(f"获取班级列表失败: {str(e)}")


def get_class_list_from_account(grade: str) -> List[str]:
    """从账户数据库获取指定年级所有班级列表"""
    try:
        logger.debug(f"Fetching class list from account database for grade: {grade}")
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()
        cursor.execute("SELECT DISTINCT class_name FROM students ORDER BY class_name")
        results = cursor.fetchall()
        cursor.close()
        con.close()
        classes = [row[0] for row in results]
        logger.debug(f"Found classes from account database: {classes}")
        return classes
    except Exception as e:
        logger.error(f"Error getting class list from account database for grade {grade}: {str(e)}")
        raise Exception(f"获取班级列表失败: {str(e)}")


# ==================== 学生成绩查询 ====================
def get_student_scores_by_class(class_name: str, cursor: Cursor) -> tuple[tuple[Any, ...], ...]:
    """获取指定班级的学生成绩（旧版本兼容）"""
    try:
        logger.debug(f"Fetching scores for class: {class_name}")
        cursor.execute(f"SELECT * FROM {class_name} ORDER BY 排名")
        results = cursor.fetchall()
        if not results:
            logger.warning(f"No scores found for class: {class_name}")
        else:
            logger.debug(f"Successfully fetched {len(results)} records for class: {class_name}")
        return results
    except Exception as e:
        error_msg = f"Error getting scores for class {class_name}: {str(e)}"
        logger.error(error_msg)
        if "Table doesn't exist" in str(e):
            raise Exception(f"班级表 {class_name} 不存在")
        else:
            raise Exception(f"获取成绩数据失败: {str(e)}")


def get_student_scores(grade: str, exam: str, class_name: str) -> List[tuple]:
    """获取指定年级、考试、班级的学生成绩"""
    try:
        logger.debug(f"Fetching scores for grade: {grade}, exam: {exam}, class: {class_name}")
        with get_exam_db_connection(grade) as (con, cursor):
            cursor.execute(f"SELECT * FROM `{exam}` WHERE class_name = %s", (class_name,))
            results = cursor.fetchall()
            logger.debug(f"Fetched {len(results)} records")
            return results
    except Exception as e:
        logger.error(f"Error getting scores for grade {grade}, exam {exam}, class {class_name}: {str(e)}")
        raise Exception(f"获取成绩数据失败: {str(e)}")


def get_student_info(exam_name: str, class_name: str, student_id: Optional[str] = None) -> Dict[str, Any]:
    """获取学生成绩信息，如果提供student_id则获取特定学生信息"""
    try:
        logger.debug(f"Fetching student info for exam: {exam_name}, class: {class_name}, student_id: {student_id}")
        with get_exam_db_connection(exam_name) as (_, cursor):
            if student_id:
                cursor.execute(
                    f"SELECT * FROM {class_name} WHERE 学号 = %s",
                    (student_id,)
                )
                result = cursor.fetchone()
                if result:
                    logger.debug(f"Found student info for student_id: {student_id}")
                    return {
                        '学号': result[1],
                        '姓名': result[2],
                        '语文': float(result[3]),
                        '数学': float(result[4]),
                        '英语': float(result[5]),
                        '物理': float(result[6]),
                        '化学': float(result[7]),
                        '生物': float(result[8]),
                        '总分': float(result[9]),
                        '排名': int(result[0])
                    }
                logger.warning(f"No student found with student_id: {student_id}")
                return {}
            else:
                cursor.execute(f"SELECT * FROM {class_name} ORDER BY 排名")
                results = cursor.fetchall()
                logger.debug(f"Found {len(results)} students in class {class_name}")
                return {
                    'students': [
                        {
                            '学号': row[1],
                            '姓名': row[2],
                            '排名': int(row[0])
                        }
                        for row in results
                    ]
                }
    except Exception as e:
        error_msg = f"Error getting student info: {str(e)}"
        logger.error(error_msg)
        if "Unknown database" in str(e):
            raise Exception(f"考试数据库 {exam_name} 不存在")
        elif "Table doesn't exist" in str(e):
            raise Exception(f"班级表 {class_name} 不存在")
        else:
            raise Exception(f"获取学生信息失败: {str(e)}")


def get_students_by_class(class_name: str) -> List[Dict[str, Any]]:
    """获取指定班级的所有学生信息"""
    try:
        logger.debug(f"Fetching students for class: {class_name}")
        with get_exam_db_connection() as (con, cursor):
            # 获取所有考试数据库
            cursor.execute("SHOW DATABASES")
            all_dbs = cursor.fetchall()
            exams = [
                db[0] for db in all_dbs
                if db[0] not in ['information_schema', 'mysql', 'performance_schema', 'sys', 'account_db']
            ]

            # 在第一个找到该班级的考试数据库中获取学生信息
            for exam in exams:
                try:
                    cursor.execute(f"USE {exam}")
                    cursor.execute(f"SELECT 学号, 姓名 FROM {class_name} ORDER BY 排名")
                    results = cursor.fetchall()
                    if results:
                        students = [
                            {
                                '学号': row[0],
                                '姓名': row[1]
                            }
                            for row in results
                        ]
                        logger.debug(f"Found {len(students)} students in class {class_name}")
                        return students
                except Exception as e:
                    logger.debug(f"Error checking exam {exam}: {str(e)}")
                    continue

            logger.warning(f"No students found for class {class_name}")
            return []

    except Exception as e:
        error_msg = f"Error getting students for class {class_name}: {str(e)}"
        logger.error(error_msg)
        raise Exception(f"获取学生信息失败: {str(e)}")


def get_exam_data(exam_name: str, class_name: str) -> List[Dict[str, Any]]:
    """获取指定考试和班级的成绩数据"""
    try:
        logger.debug(f"Fetching exam data for exam: {exam_name}, class: {class_name}")
        with get_exam_db_connection(exam_name) as (_, cursor):
            cursor.execute(f"SELECT * FROM {class_name} ORDER BY 排名")
            results = cursor.fetchall()

            if not results:
                logger.warning(f"No data found for class {class_name} in exam {exam_name}")
                return []

            # 转换为字典列表
            data = []
            for row in results:
                student_data = {
                    '排名': int(row[0]),
                    '学号': row[1],
                    '姓名': row[2],
                    '语文': float(row[3]),
                    '数学': float(row[4]),
                    '英语': float(row[5]),
                    '物理': float(row[6]),
                    '化学': float(row[7]),
                    '生物': float(row[8]),
                    '总分': float(row[9])
                }
                data.append(student_data)

            logger.debug(f"Successfully fetched {len(data)} records")
            return data

    except Exception as e:
        error_msg = f"Error getting exam data: {str(e)}"
        logger.error(error_msg)
        raise Exception(f"获取考试数据失败: {str(e)}")


# ==================== 统计数据查询 ====================
def get_class_statistics(exam_name: str, class_name: str) -> Dict[str, Any]:
    """获取班级统计数据"""
    try:
        logger.debug(f"Calculating statistics for exam: {exam_name}, class: {class_name}")
        with get_exam_db_connection(exam_name) as (_, cursor):
            cursor.execute(f"SELECT * FROM {class_name}")
            results = cursor.fetchall()

            if not results:
                logger.warning(f"No data found for class {class_name} in exam {exam_name}")
                return {}

            # 计算各科平均分
            subjects = ['语文', '数学', '英语', '物理', '化学', '生物']
            averages = {}
            for i, subject in enumerate(subjects, 3):  # 从索引3开始是各科成绩
                scores = [float(row[i]) for row in results]
                averages[subject] = round(sum(scores) / len(scores), 1)

            # 计算总分平均分
            total_scores = [float(row[9]) for row in results]  # 索引9是总分
            averages['总分'] = round(sum(total_scores) / len(total_scores), 1)

            stats = {
                '班级': class_name,
                '考试': exam_name,
                '学生人数': len(results),
                '平均分': averages
            }
            logger.debug(f"Calculated statistics: {stats}")
            return stats
    except Exception as e:
        error_msg = f"Error getting class statistics: {str(e)}"
        logger.error(error_msg)
        if "Unknown database" in str(e):
            raise Exception(f"考试数据库 {exam_name} 不存在")
        elif "Table doesn't exist" in str(e):
            raise Exception(f"班级表 {class_name} 不存在")
        else:
            raise Exception(f"获取班级统计数据失败: {str(e)}")


def get_class_averages(exam_name: str, class_name: str) -> Dict[str, float]:
    """获取指定考试和班级的平均分数据"""
    try:
        logger.debug(f"Getting class averages for exam: {exam_name}, class: {class_name}")
        with get_exam_db_connection(exam_name) as (_, cursor):
            # 计算各科目平均分
            subjects = ['语文', '数学', '英语', '物理', '化学', '生物']
            averages = {}

            for subject in subjects:
                cursor.execute(f"SELECT AVG(`{subject}`) FROM `{class_name}` WHERE `{subject}` IS NOT NULL")
                result = cursor.fetchone()
                avg_score = result[0] if result[0] is not None else 0.0
                averages[subject] = round(float(avg_score), 1)

            # 计算总分平均分
            cursor.execute(f"SELECT AVG(`总分`) FROM `{class_name}` WHERE `总分` IS NOT NULL")
            result = cursor.fetchone()
            total_avg = result[0] if result[0] is not None else 0.0
            averages['总分'] = round(float(total_avg), 1)

            logger.debug(f"Class averages: {averages}")
            return averages

    except Exception as e:
        error_msg = f"Error getting class averages: {str(e)}"
        logger.error(error_msg)
        raise Exception(f"获取班级平均分失败: {str(e)}")


# ==================== 课程和满分数据查询 ====================
def get_full_marks(exam_name: str, grade: str = None) -> Dict[str, float]:
    """获取指定考试的满分数据，从数据库中读取"""
    try:
        logger.debug(f"Getting full marks for exam: {exam_name}, grade: {grade}")

        # 如果没有指定年级，尝试从考试名称推断年级
        if not grade:
            # 尝试从全局变量或其他方式获取当前年级
            # 这里需要根据实际情况调整
            grade = '高一'  # 默认年级

        full_marks = {}

        try:
            # 连接到年级数据库获取课程满分信息
            con = Connect(**EXAM_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()

            # 从grade_courses表获取课程满分信息
            cursor.execute("""
                SELECT course_name, full_mark
                FROM grade_courses
                WHERE full_mark IS NOT NULL AND full_mark > 0
                ORDER BY course_name
            """)

            results = cursor.fetchall()
            for course_name, full_mark in results:
                full_marks[course_name] = float(full_mark)

            cursor.close()
            con.close()

            logger.debug(f"Retrieved full marks from database: {full_marks}")

        except Exception as db_error:
            logger.warning(f"Failed to get full marks from database: {str(db_error)}")
            # 如果数据库查询失败，使用默认满分设置
            full_marks = {
                '语文': 150.0,
                '数学': 150.0,
                '英语': 150.0,
                '物理': 100.0,
                '化学': 100.0,
                '生物': 100.0
            }
            logger.debug(f"Using default full marks: {full_marks}")

        # 如果数据库中没有满分信息，使用默认值
        if not full_marks:
            full_marks = {
                '语文': 150.0,
                '数学': 150.0,
                '英语': 150.0,
                '物理': 100.0,
                '化学': 100.0,
                '生物': 100.0
            }
            logger.debug(f"No full marks found in database, using defaults: {full_marks}")

        return full_marks

    except Exception as e:
        error_msg = f"Error getting full marks: {str(e)}"
        logger.error(error_msg)
        raise Exception(f"获取满分数据失败: {str(e)}")


def get_courses_list(grade: str = None) -> list:
    """从数据库获取课程列表"""
    try:
        logger.debug(f"Getting courses list for grade: {grade}")

        # 如果没有指定年级，使用默认年级
        if not grade:
            grade = '高一'

        courses = []

        try:
            # 连接到年级数据库获取课程信息
            con = Connect(**EXAM_DB_CONFIG)
            con.select_db(grade)
            cursor = con.cursor()

            # 从grade_courses表获取课程列表
            cursor.execute("""
                SELECT course_name
                FROM grade_courses
                ORDER BY course_name
            """)

            results = cursor.fetchall()
            courses = [row[0] for row in results]

            cursor.close()
            con.close()

            logger.debug(f"Retrieved courses from database: {courses}")

        except Exception as db_error:
            logger.warning(f"Failed to get courses from database: {str(db_error)}")
            # 如果数据库查询失败，使用默认课程列表
            courses = ['语文', '数学', '英语', '物理', '化学', '生物']
            logger.debug(f"Using default courses: {courses}")

        # 如果数据库中没有课程信息，使用默认值
        if not courses:
            courses = ['语文', '数学', '英语', '物理', '化学', '生物']
            logger.debug(f"No courses found in database, using defaults: {courses}")

        return courses

    except Exception as e:
        error_msg = f"Error getting courses list: {str(e)}"
        logger.error(error_msg)
        # 返回默认课程列表
        return ['语文', '数学', '英语', '物理', '化学', '生物']


# ==================== 用户验证 ====================
def verify_admin_login(username: str, password: str) -> bool:
    """只查 root 数据库 users 表，验证管理员登录"""
    try:
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db('root')
        cursor = con.cursor()
        cursor.execute(
            "SELECT * FROM users WHERE account = %s AND password = %s",
            (username, password)
        )
        result = cursor.fetchone()
        cursor.close()
        con.close()
        return result is not None
    except Exception as e:
        logger.error(f"Error verifying admin login: {str(e)}")
        return False


def verify_teacher_login(username: str, password: str) -> tuple[bool, str | None, str | None, bool]:
    """遍历所有年级数据库，验证教师登录，返回(是否成功, 年级, 班级, 是否为班主任)"""
    try:
        grades = get_grade_list()
        for grade in grades:
            try:
                con = Connect(
                    host=ACCOUNT_DB_CONFIG['host'],
                    user=ACCOUNT_DB_CONFIG['user'],
                    passwd=ACCOUNT_DB_CONFIG['passwd'],
                    port=ACCOUNT_DB_CONFIG['port'],
                    autocommit=True
                )
                con.select_db(grade)
                cursor = con.cursor()
                cursor.execute(
                    "SELECT manage_class, is_head_teacher FROM teachers WHERE username = %s AND password = %s",
                    (username, password)
                )
                result = cursor.fetchone()
                cursor.close()
                con.close()
                if result:
                    manage_class = result[0]
                    is_head_teacher = bool(result[1])
                    return True, grade, manage_class, is_head_teacher
            except Exception as e:
                logger.debug(f"Grade {grade} not found or error: {str(e)}")
                continue
        return False, None, None, False
    except Exception as e:
        logger.error(f"Error verifying teacher login: {str(e)}")
        return False, None, None, False


def verify_student_login(username: str, password: str) -> tuple[bool, str|None]:
    """遍历所有年级数据库，验证学生登录，返回(是否成功, 年级)"""
    try:
        grades = get_grade_list()
        for grade in grades:
            try:
                con = Connect(
                    host=ACCOUNT_DB_CONFIG['host'],
                    user=ACCOUNT_DB_CONFIG['user'],
                    passwd=ACCOUNT_DB_CONFIG['passwd'],
                    port=ACCOUNT_DB_CONFIG['port'],
                    autocommit=True
                )
                con.select_db(grade)
                cursor = con.cursor()
                cursor.execute(
                    "SELECT * FROM students WHERE username = %s AND password = %s",
                    (username, password)
                )
                result = cursor.fetchone()
                cursor.close()
                con.close()
                if result:
                    return True, grade
            except Exception as e:
                logger.debug(f"Grade {grade} not found or error: {str(e)}")
                continue
        return False, None
    except Exception as e:
        logger.error(f"Error verifying student login: {str(e)}")
        return False, None


# ==================== 班级和考试信息查询 ====================
def get_class_info_from_exam(grade: str, class_name: str = None) -> dict:
    """从考试数据库获取班级信息，支持按班级名筛选"""
    try:
        logger.debug(f"Getting class info from exam database for grade: {grade}, class: {class_name}")
        with get_exam_db_connection(grade) as (con, cursor):
            if class_name:
                cursor.execute("SELECT * FROM class WHERE class_name = %s", (class_name,))
                result = cursor.fetchone()
                if result:
                    return {
                        'id': result[0],
                        'class_type': result[1],
                        'class_name': result[2],
                        'class_size': result[3],
                        'head_teacher': result[4]
                    }
                return {}
            else:
                cursor.execute("SELECT * FROM class ORDER BY id")
                results = cursor.fetchall()
                return {
                    'classes': [
                        {
                            'id': row[0],
                            'class_type': row[1],
                            'class_name': row[2],
                            'class_size': row[3],
                            'head_teacher': row[4]
                        }
                        for row in results
                    ]
                }
    except Exception as e:
        logger.error(f"Error getting class info from exam database for grade {grade}: {str(e)}")
        raise


def get_class_info_from_account(grade: str, class_name: str = None) -> dict:
    """从账户数据库获取班级信息，支持按班级名筛选"""
    try:
        logger.debug(f"Getting class info from account database for grade: {grade}, class: {class_name}")
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()
        if class_name:
            cursor.execute("SELECT * FROM class WHERE class_name = %s", (class_name,))
            result = cursor.fetchone()
            cursor.close()
            con.close()
            if result:
                return {
                    'id': result[0],
                    'class_type': result[1],
                    'class_name': result[2],
                    'class_size': result[3],
                    'head_teacher': result[4]
                }
            return {}
        else:
            cursor.execute("SELECT * FROM class ORDER BY id")
            results = cursor.fetchall()
            cursor.close()
            con.close()
            return {
                'classes': [
                    {
                        'id': row[0],
                        'class_type': row[1],
                        'class_name': row[2],
                        'class_size': row[3],
                        'head_teacher': row[4]
                    }
                    for row in results
                ]
            }
    except Exception as e:
        logger.error(f"Error getting class info from account database for grade {grade}: {str(e)}")
        raise


def get_exam_info_from_exam(grade: str, exam_name: str = None) -> dict:
    """从考试数据库获取考试信息，支持按考试名筛选"""
    try:
        logger.debug(f"Getting exam info from exam database for grade: {grade}, exam: {exam_name}")
        with get_exam_db_connection(grade) as (con, cursor):
            if exam_name:
                cursor.execute("SELECT * FROM exam WHERE exam_name = %s", (exam_name,))
                result = cursor.fetchone()
                if result:
                    return {
                        'id': result[0],
                        'exam_name': result[1],
                        'participant_count': result[2],
                        'absent_count': result[3]
                    }
                return {}
            else:
                cursor.execute("SELECT * FROM exam ORDER BY id")
                results = cursor.fetchall()
                return {
                    'exams': [
                        {
                            'id': row[0],
                            'exam_name': row[1],
                            'participant_count': row[2],
                            'absent_count': row[3]
                        }
                        for row in results
                    ]
                }
    except Exception as e:
        logger.error(f"Error getting exam info from exam database for grade {grade}: {str(e)}")
        raise


def get_exam_info_from_account(grade: str, exam_name: str = None) -> dict:
    """从账户数据库获取考试信息，支持按考试名筛选"""
    try:
        logger.debug(f"Getting exam info from account database for grade: {grade}, exam: {exam_name}")
        con = Connect(**ACCOUNT_DB_CONFIG)
        con.select_db(grade)
        cursor = con.cursor()
        if exam_name:
            cursor.execute("SELECT * FROM exam WHERE exam_name = %s", (exam_name,))
            result = cursor.fetchone()
            cursor.close()
            con.close()
            if result:
                return {
                    'id': result[0],
                    'exam_name': result[1],
                    'participant_count': result[2],
                    'absent_count': result[3]
                }
            return {}
        else:
            cursor.execute("SELECT * FROM exam ORDER BY id")
            results = cursor.fetchall()
            cursor.close()
            con.close()
            return {
                'exams': [
                    {
                        'id': row[0],
                        'exam_name': row[1],
                        'participant_count': row[2],
                        'absent_count': row[3]
                    }
                    for row in results
                ]
            }
    except Exception as e:
        logger.error(f"Error getting exam info from account database for grade {grade}: {str(e)}")
        raise


# ==================== 用户信息查询 ====================
def get_teacher_info(grade: str, username: Optional[str] = None, class_name: Optional[str] = None) -> dict:
    """获取指定年级教师账户信息，支持按用户名或班级筛选（班级时返回班主任和任课老师）"""
    try:
        con = Connect(
            host=ACCOUNT_DB_CONFIG['host'],
            user=ACCOUNT_DB_CONFIG['user'],
            passwd=ACCOUNT_DB_CONFIG['passwd'],
            port=ACCOUNT_DB_CONFIG['port'],
            autocommit=True
        )
        con.select_db(grade)
        cursor = con.cursor()

        def teacher_row_to_dict(row):
            return {
                'id': row[0],
                'username': row[1],
                'password': row[2],
                'name': row[3],
                'phone': row[4],
                'grade': row[5],
                'is_head_teacher': row[6],
                'manage_class': row[7],
                'subjects': row[8],
                'is_class_teacher': row[9],
                'certificate_level': row[10],
                'remark': row[11]
            }

        if username:
            cursor.execute(
                "SELECT * FROM teachers WHERE username = %s",
                (username,)
            )
            result = cursor.fetchone()
            cursor.close()
            con.close()
            if result:
                return teacher_row_to_dict(result)
            return {}
        elif class_name:
            cursor.execute(
                "SELECT * FROM teachers WHERE manage_class = %s OR FIND_IN_SET(%s, is_class_teacher)",
                (class_name, class_name)
            )
            results = cursor.fetchall()
            cursor.close()
            con.close()
            return {
                'teachers': [teacher_row_to_dict(row) for row in results]
            }
        else:
            cursor.execute("SELECT * FROM teachers ORDER BY id")
            results = cursor.fetchall()
            cursor.close()
            con.close()
            return {
                'teachers': [teacher_row_to_dict(row) for row in results]
            }
    except Exception as e:
        logger.error(f"Error getting teacher info for grade {grade}: {str(e)}")
        raise


def get_student_account_info(grade: str, username: Optional[str] = None, class_name: Optional[str] = None) -> dict:
    """获取指定年级学生账户信息"""
    try:
        con = Connect(
            host=ACCOUNT_DB_CONFIG['host'],
            user=ACCOUNT_DB_CONFIG['user'],
            passwd=ACCOUNT_DB_CONFIG['passwd'],
            port=ACCOUNT_DB_CONFIG['port'],
            autocommit=True
        )
        con.select_db(grade)
        cursor = con.cursor()

        if username:
            cursor.execute(
                "SELECT * FROM students WHERE username = %s",
                (username,)
            )
            result = cursor.fetchone()
            cursor.close()
            con.close()
            if result:
                return {
                    'id': result[0],
                    'username': result[1],
                    'student_id': result[3],
                    'name': result[4],
                    'class_name': result[5],
                    'grade': result[6],
                    'teacher_message': result[7] if len(result) > 7 else '',
                    'phone': result[8] if len(result) > 8 else '',
                    'feedback': result[9] if len(result) > 9 else ''
                }
            return {}
        elif class_name:
            cursor.execute(
                "SELECT * FROM students WHERE class_name = %s ORDER BY CAST(student_id AS UNSIGNED)",
                (class_name,)
            )
            results = cursor.fetchall()
            cursor.close()
            con.close()
            return {
                'students': [
                    {
                        'id': row[0],
                        'username': row[1],
                        'password': row[2],
                        'student_id': row[3],
                        'name': row[4],
                        'class_name': row[5],
                        'grade': row[6],
                        'teacher_message': row[7] if len(row) > 7 else '',
                        'phone': row[8] if len(row) > 8 else '',
                        'feedback': row[9] if len(row) > 9 else ''
                    }
                    for row in results
                ]
            }
        else:
            cursor.execute(
                "SELECT * FROM students ORDER BY class_name, CAST(student_id AS UNSIGNED)"
            )
            results = cursor.fetchall()
            cursor.close()
            con.close()
            return {
                'students': [
                    {
                        'id': row[0],
                        'username': row[1],
                        'password': row[2],
                        'student_id': row[3],
                        'name': row[4],
                        'class_name': row[5],
                        'grade': row[6],
                        'teacher_message': row[7] if len(row) > 7 else '',
                        'phone': row[8] if len(row) > 8 else '',
                        'feedback': row[9] if len(row) > 9 else ''
                    }
                    for row in results
                ]
            }
    except Exception as e:
        logger.error(f"Error getting student account info for grade {grade}: {str(e)}")
        raise


# ==================== 消息管理 ====================
def add_student_message(grade, student_id, name, class_name, is_anonymous, message):
    """添加学生消息"""
    from backend.means import create_student_message_table_if_not_exists

    create_student_message_table_if_not_exists(grade)
    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    cursor.execute('''
        INSERT INTO student_messages (student_id, name, class_name, is_anonymous, message)
        VALUES (%s, %s, %s, %s, %s)
    ''', (student_id, name, class_name, int(is_anonymous), message))
    con.commit()
    cursor.close()
    con.close()


def get_student_messages(grade, student_id):
    """获取学生消息"""
    from backend.means import create_student_message_table_if_not_exists

    create_student_message_table_if_not_exists(grade)
    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    cursor.execute('''
        SELECT id, name, is_anonymous, message, create_time, student_id
        FROM student_messages
        WHERE student_id = %s OR student_id = %s
        ORDER BY create_time DESC
    ''', (student_id, student_id))
    results = cursor.fetchall()
    cursor.close()
    con.close()
    return [
        {
            'id': r[0],
            'name': '匿名' if r[2] else r[1],
            'is_anonymous': bool(r[2]),
            'message': r[3],
            'create_time': str(r[4]),
            'is_self': (r[5] == student_id)
        } for r in results
    ]


def delete_student_message(grade, msg_id, student_id=None, is_admin=False):
    """删除学生消息"""
    from backend.means import create_student_message_table_if_not_exists

    create_student_message_table_if_not_exists(grade)
    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    if is_admin:
        cursor.execute('DELETE FROM student_messages WHERE id = %s', (msg_id,))
    else:
        cursor.execute('DELETE FROM student_messages WHERE id = %s AND student_id = %s', (msg_id, student_id))
    affected = cursor.rowcount
    con.commit()
    cursor.close()
    con.close()
    return affected > 0


def add_teacher_message_history(grade, student_id, teacher_name, message):
    """添加教师消息历史"""
    from backend.means import create_teacher_message_history_table_if_not_exists

    create_teacher_message_history_table_if_not_exists(grade)
    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    cursor.execute('''
        INSERT INTO teacher_message_history (student_id, teacher_name, message)
        VALUES (%s, %s, %s)
    ''', (student_id, teacher_name, message))
    con.commit()
    cursor.close()
    con.close()


def get_teacher_message_history(grade, student_id):
    """获取教师消息历史"""
    from backend.means import create_teacher_message_history_table_if_not_exists

    create_teacher_message_history_table_if_not_exists(grade)
    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    cursor.execute('''
        SELECT id, teacher_name, message, create_time
        FROM teacher_message_history
        WHERE student_id = %s
        ORDER BY create_time DESC
    ''', (student_id,))
    results = cursor.fetchall()
    cursor.close()
    con.close()
    return [
        {
            'id': r[0],
            'teacher_name': r[1],
            'message': r[2],
            'create_time': str(r[3])
        } for r in results
    ]


def delete_teacher_message_history(grade, msg_id):
    """删除教师消息历史"""
    from backend.means import create_teacher_message_history_table_if_not_exists

    create_teacher_message_history_table_if_not_exists(grade)
    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    cursor.execute('DELETE FROM teacher_message_history WHERE id = %s', (msg_id,))
    affected = cursor.rowcount
    con.commit()
    cursor.close()
    con.close()
    return affected > 0


def get_class_messages(grade, class_name):
    """获取指定年级和班级的所有学生留言，按时间倒序排列"""
    from backend.means import create_student_message_table_if_not_exists

    create_student_message_table_if_not_exists(grade)
    con = Connect(**ACCOUNT_DB_CONFIG)
    con.select_db(grade)
    cursor = con.cursor()
    cursor.execute('''
        SELECT id, name, is_anonymous, message, create_time, student_id
        FROM student_messages
        WHERE class_name = %s
        ORDER BY create_time DESC
    ''', (class_name,))
    results = cursor.fetchall()
    cursor.close()
    con.close()
    return [
        {
            'id': r[0],
            'name': '匿名' if r[2] else r[1],
            'is_anonymous': bool(r[2]),
            'message': r[3],
            'create_time': str(r[4]),
            'student_id': r[5]
        } for r in results
    ]


# ==================== 向后兼容的函数别名 ====================
def get_class_info(grade: str, class_name: str = None) -> dict:
    """获取班级信息（向后兼容，默认从考试数据库获取）"""
    return get_class_info_from_exam(grade, class_name)


def get_exam_info(grade: str, exam_name: str = None) -> dict:
    """获取考试信息（向后兼容，默认从考试数据库获取）"""
    return get_exam_info_from_exam(grade, exam_name)


def get_class_list(grade: str, exam: str = None) -> List[str]:
    """获取班级列表（向后兼容）"""
    if exam:
        return get_class_list_from_exam(grade, exam)
    else:
        return get_class_list_from_account(grade)
