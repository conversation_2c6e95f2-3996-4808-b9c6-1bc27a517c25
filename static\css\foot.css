/* =====================
   底部样式（foot.css）
   ===================== */

/* ========== 1. 底部容器 ========== */
.footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    padding: 2rem 0 1rem 0;
    margin-top: auto;
    color: #6c757d;
}

/* ========== 2. 底部内容 ========== */
.footer-content {
    text-align: center;
    font-size: 0.9rem;
}

.footer-content p {
    margin-bottom: 0.5rem;
}

.footer-content .text-muted {
    color: #6c757d !important;
}

/* ========== 3. 底部链接 ========== */
.footer-links {
    margin: 1rem 0;
}

.footer-links a {
    color: #6c757d;
    text-decoration: none;
    margin: 0 1rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* ========== 4. 版权信息 ========== */
.copyright {
    font-size: 0.8rem;
    color: #adb5bd;
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
    margin-top: 1rem;
}

/* ========== 5. 响应式设计 ========== */
@media (max-width: 768px) {
    .footer {
        padding: 1.5rem 0 1rem 0;
    }

    .footer-links a {
        display: block;
        margin: 0.5rem 0;
    }
}