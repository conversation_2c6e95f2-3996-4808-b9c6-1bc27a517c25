/**
 * ========================================
 * 顶部导航栏和认证功能模块 (top.js)
 * ========================================
 * 专门处理页面顶部导航栏相关功能：
 * 1. 用户登录状态检查和管理
 * 2. 导航栏状态管理
 * 3. 退出登录处理
 * 4. 帮助功能
 * 5. 页面初始化协调
 * ========================================
 */

// ========================================
// 1. 用户认证和会话管理
// ========================================

/**
 * 检查用户登录状态
 * @returns {boolean} 是否已登录
 */
function checkLogin() {
    const isLoggedIn = sessionStorage.getItem('isLoggedIn');
    if (isLoggedIn !== 'true') {
        window.location.href = '/html/log.html';
        return false;
    }
    return true;
}

/**
 * 获取当前用户信息
 * @returns {Object} 用户信息对象
 */
function getCurrentUser() {
    return {
        username: sessionStorage.getItem('username'),
        name: sessionStorage.getItem('name'),
        userType: sessionStorage.getItem('user_type'),
        grade: sessionStorage.getItem('grade'),
        className: sessionStorage.getItem('class_name'),
        isHeadTeacher: sessionStorage.getItem('is_head_teacher') === 'true'
    };
}

/**
 * 清除用户会话信息
 */
function clearUserSession() {
    sessionStorage.clear();
}

// ========================================
// 2. 退出登录处理
// ========================================

/**
 * 处理退出登录功能
 */
function handleLogout() {
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            confirmLogout();
        });
    }
}

/**
 * 确认退出登录
 */
function confirmLogout() {
    Swal.fire({
        title: '确认退出',
        text: '您确定要退出登录吗？',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            performLogout();
        }
    });
}

/**
 * 执行退出登录操作
 */
function performLogout() {
    try {
        clearUserSession();
        showSuccessToast('已成功退出登录');
        setTimeout(() => {
            window.location.href = '/html/log.html';
        }, 1000);
    } catch (error) {
        console.error('退出登录时发生错误:', error);
        // 即使出错也要跳转到登录页
        window.location.href = '/html/log.html';
    }
}

// ========================================
// 3. 帮助功能处理
// ========================================

/**
 * 处理帮助按钮功能
 */
function handleHelp() {
    const helpBtn = document.getElementById('helpBtn');
    if (helpBtn) {
        helpBtn.addEventListener('click', function() {
            showHelpModal();
        });
    }
}

/**
 * 显示帮助模态框或信息
 */
function showHelpModal() {
    const helpModal = document.getElementById('helpModal');
    if (helpModal) {
        const modal = new bootstrap.Modal(helpModal);
        modal.show();
    } else {
        showDefaultHelpInfo();
    }
}

/**
 * 显示默认帮助信息
 */
function showDefaultHelpInfo() {
    const userType = getCurrentUser().userType;
    const helpContent = getHelpContentByUserType(userType);

    Swal.fire({
        title: '使用帮助',
        html: helpContent,
        icon: 'info',
        confirmButtonText: '知道了',
        width: 600
    });
}

/**
 * 根据用户类型获取帮助内容
 * @param {string} userType - 用户类型
 * @returns {string} 帮助内容 HTML
 */
function getHelpContentByUserType(userType) {
    const commonHelp = `
        <div class="text-start">
            <h6>基本操作：</h6>
            <ul>
                <li>点击左侧功能按钮切换不同功能</li>
                <li>使用搜索框快速查找信息</li>
                <li>点击表格行查看详细信息</li>
            </ul>
    `;

    const specificHelp = {
        'admin': `
            <h6>管理员功能：</h6>
            <ul>
                <li>管理年级、班级、教师和学生信息</li>
                <li>查看和分析各类统计数据</li>
                <li>导出和下载相关报表</li>
            </ul>
        `,
        'teacher': `
            <h6>教师功能：</h6>
            <ul>
                <li>查看和分析班级成绩数据</li>
                <li>管理学生请假申请</li>
                <li>发送和接收消息</li>
            </ul>
        `,
        'student': `
            <h6>学生功能：</h6>
            <ul>
                <li>查看个人成绩分析</li>
                <li>提交请假申请</li>
                <li>查看学习建议</li>
            </ul>
        `
    };

    const endHelp = `
            <h6>注意事项：</h6>
            <ul>
                <li>请及时保存重要数据</li>
                <li>如遇问题请联系管理员</li>
                <li>定期检查系统通知</li>
            </ul>
        </div>
    `;

    return commonHelp + (specificHelp[userType] || '') + endHelp;
}

// ========================================
// 4. 导航栏状态管理
// ========================================

/**
 * 初始化导航栏功能
 */
function initNavbar() {
    setActiveNavLink();
    updateUserInfo();
}

/**
 * 设置当前页面的导航链接为激活状态
 */
function setActiveNavLink() {
    const currentPage = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.classList.remove('active');

        // 根据当前页面设置激活状态
        const linkText = link.textContent.trim();
        if (currentPage.includes('admin') && linkText.includes('管理员')) {
            link.classList.add('active');
        } else if (currentPage.includes('student') && linkText.includes('学生')) {
            link.classList.add('active');
        } else if (currentPage.includes('teacher') && linkText.includes('教师')) {
            link.classList.add('active');
        }
    });
}

/**
 * 更新导航栏中的用户信息显示
 */
function updateUserInfo() {
    const user = getCurrentUser();

    // 更新用户名显示
    const userNameElement = document.getElementById('currentUserName');
    if (userNameElement && user.name) {
        userNameElement.textContent = user.name;
    }

    // 更新用户类型显示
    const userTypeElement = document.getElementById('currentUserType');
    if (userTypeElement && user.userType) {
        const typeNames = {
            'admin': '管理员',
            'teacher': '教师',
            'student': '学生'
        };
        userTypeElement.textContent = typeNames[user.userType] || user.userType;
    }

    // 更新年级班级信息
    const userInfoElement = document.getElementById('currentUserInfo');
    if (userInfoElement) {
        let infoText = '';
        if (user.grade) {
            infoText += user.grade;
        }
        if (user.className) {
            infoText += ` ${user.className}`;
        }
        userInfoElement.textContent = infoText;
    }
}

// ========================================
// 5. 页面初始化协调
// ========================================

/**
 * 统一的页面初始化函数
 * 协调各个模块的初始化顺序
 */
function initializePage() {
    if (checkLogin()) {
        // 初始化公共组件（来自 public.js）
        if (typeof initPageComponents === 'function') {
            initPageComponents();
        }

        // 初始化导航栏
        initNavbar();

        // 绑定事件处理器
        handleLogout();
        handleHelp();

        // 触发自定义初始化完成事件
        document.dispatchEvent(new CustomEvent('pageInitialized', {
            detail: { user: getCurrentUser() }
        }));
    }
}

// ========================================
// 6. 事件监听器注册
// ========================================

// DOM加载完成后执行初始化
document.addEventListener('DOMContentLoaded', initializePage);