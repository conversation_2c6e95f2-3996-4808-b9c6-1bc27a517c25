# ==================== Excel文件数据转化成MySQL模块 ====================
"""
Excel文件数据转化成MySQL模块
整合了excel_mysql.py和excel_mysql_account.py的功能
包含Excel文件数据转化成MySQL的代码和MySQL库和表的创建代码
"""

import logging
import os
import re
from contextlib import contextmanager
from typing import Tuple

import pandas as pd
from pymysql import Connect, Connection
from pymysql.cursors import Cursor
import numpy as np

# 导入工具函数
from backend.means import (
    standardize_scores_df, extract_courses_from_scores, 
    generate_random_course_code, chinese_to_num, get_exam_order
)

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'passwd': '**********',
    'autocommit': True
}

# 数据目录配置
SCORE_DATA_DIR = "D:/学生成绩app/data/各班学生成绩"
ACCOUNT_DATA_DIR = r"D:\学生成绩app\data\账号信息"


# ==================== 数据库连接管理 ====================
@contextmanager
def get_db_connection() -> Tuple[Connection, Cursor]:  # type: ignore
    """数据库连接上下文管理器"""
    con = None
    try:
        con = Connect(**DB_CONFIG)
        cursor = con.cursor()
        yield con, cursor
    except Exception as e:
        logger.error(f"Database connection error: {str(e)}")
        raise
    finally:
        if con:
            con.close()


# ==================== 数据库和表创建函数 ====================
def create_database(cursor: Cursor, db_name: str) -> None:
    """创建数据库"""
    try:
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{db_name}`")
        logger.info(f"数据库 '{db_name}' 创建成功!")
    except Exception as e:
        logger.error(f"Error creating database {db_name}: {str(e)}")
        raise


def create_class_table(cursor: Cursor) -> None:
    """创建班级表"""
    try:
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS class (
                id INT AUTO_INCREMENT PRIMARY KEY,
                grade VARCHAR(20),
                class_type VARCHAR(50),
                class_name VARCHAR(50) UNIQUE,
                class_size INT,
                head_teacher VARCHAR(255),
                create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                remark VARCHAR(255) DEFAULT ''
            )
        ''')
        logger.info("class表创建成功!")
    except Exception as e:
        logger.error(f"Error creating class table: {str(e)}")
        raise


def create_exam_table(cursor: Cursor) -> None:
    """创建考试表"""
    try:
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS exam (
                id INT AUTO_INCREMENT PRIMARY KEY,
                exam_name VARCHAR(255) UNIQUE,
                participant_count INT,
                absent_count INT
            )
        ''')
        logger.info("exam表创建成功!")
    except Exception as e:
        logger.error(f"Error creating exam table: {str(e)}")
        raise


def create_score_table(cursor: Cursor, table_name: str, grade: str = None) -> None:
    """创建成绩表，动态从数据库获取课程字段"""
    try:
        # 获取课程列表
        try:
            # 尝试从grade_courses表获取课程列表
            cursor.execute("SELECT course_name FROM grade_courses ORDER BY course_name")
            results = cursor.fetchall()
            courses = [row[0] for row in results] if results else []
        except Exception:
            # 如果获取失败，使用默认课程列表
            courses = ['语文', '数学', '英语', '物理', '化学', '生物']

        # 如果没有课程，使用默认课程
        if not courses:
            courses = ['语文', '数学', '英语', '物理', '化学', '生物']

        # 构建课程字段SQL
        course_fields = []
        for course in courses:
            course_fields.append(f"`{course}` DECIMAL(4, 1)")

        course_fields_sql = ",\n            ".join(course_fields)

        create_table_query = f"""
        CREATE TABLE IF NOT EXISTS `{table_name}`(
            id INT AUTO_INCREMENT PRIMARY KEY,
            class_name VARCHAR(50) NOT NULL,
            student_id VARCHAR(50) NOT NULL,
            name VARCHAR(255),
            {course_fields_sql},
            总分 DECIMAL(4, 1),
            class_rank INT,
            grade_rank INT
        )
        """
        cursor.execute(create_table_query)
        logger.info(f"{table_name}成绩表创建成功，包含课程: {', '.join(courses)}")
    except Exception as e:
        logger.error(f"Error creating table {table_name}: {str(e)}")
        raise


def create_students_table(cursor: Cursor) -> None:
    """创建学生表"""
    try:
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS students (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(50) NOT NULL,
                student_id VARCHAR(50) NOT NULL,
                name VARCHAR(255) NOT NULL,
                class_name VARCHAR(50) NOT NULL,
                grade VARCHAR(20),
                teacher_message VARCHAR(255) DEFAULT '',
                feedback VARCHAR(255) DEFAULT '',
                phone VARCHAR(20) DEFAULT ''
            )
        ''')
        logger.info("学生表创建成功!")
    except Exception as e:
        logger.error(f"Error creating students table: {str(e)}")
        raise


def create_teachers_table(cursor: Cursor) -> None:
    """创建教师表"""
    try:
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS teachers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(50) NOT NULL,
                name VARCHAR(255) NOT NULL,
                phone VARCHAR(20),
                grade VARCHAR(20),
                is_head_teacher TINYINT(1) DEFAULT 0,
                manage_class VARCHAR(255) DEFAULT '0',
                subjects VARCHAR(500) DEFAULT '',
                is_class_teacher VARCHAR(255) DEFAULT '0',
                certificate_level VARCHAR(50) DEFAULT '',
                remark VARCHAR(255) DEFAULT ''
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ''')
        logger.info("教师表创建成功!")
    except Exception as e:
        logger.error(f"Error creating teachers table: {str(e)}")
        raise


def create_grade_courses_table(cursor: Cursor, grade: str) -> None:
    """创建年级课程表（初始不分配教师，后续通过update_grade_courses_teacher_names分配）"""
    try:
        cursor.execute('DROP TABLE IF EXISTS grade_courses')

        create_table_query = """
        CREATE TABLE grade_courses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_name VARCHAR(100) NOT NULL,
            course_code VARCHAR(50) NOT NULL,
            credit DECIMAL(3,1),
            type VARCHAR(20),
            full_mark DECIMAL(4,1),
            teacher_names TEXT,
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """
        cursor.execute(create_table_query)

        # 导入6门课程信息（暂不分配教师，等教师数据导入后再分配）
        courses = [
            ('语文', 'CHI001', 15.0, '必修', 150),
            ('数学', 'MATH001', 15.0, '必修', 150),
            ('英语', 'ENG001', 15.0, '必修', 150),
            ('物理', 'PHY001', 15.0, '必修', 100),
            ('化学', 'CHEM001', 15.0, '必修', 100),
            ('生物', 'BIO001', 15.0, '必修', 100)
        ]

        for course_name, base_code, credit, course_type, full_mark in courses:
            course_code = generate_random_course_code(course_name)

            # 初始创建时不分配教师，设为空字符串
            teacher_name = ''

            cursor.execute('''
                INSERT INTO grade_courses (course_name, course_code, credit, type, full_mark, teacher_names)
                VALUES (%s, %s, %s, %s, %s, %s)
            ''', (course_name, course_code, credit, course_type, full_mark, teacher_name))

        logger.info(f"年级 {grade} 课程表创建成功，导入 {len(courses)} 门课程!")
    except Exception as e:
        logger.error(f"Error creating grade courses table in grade {grade}: {str(e)}")
        raise





# ==================== 数据插入函数 ====================
def insert_student_scores(cursor: Cursor, table_name: str, class_name: str, df: pd.DataFrame) -> None:
    """插入学生成绩数据，增加班级字段和班级排名，年级排名先设为0。单条数据异常时不中断整体流程。"""
    try:
        # 先标准化特殊值
        df = standardize_scores_df(df)

        # 获取数据库表中的课程字段
        cursor.execute(f"SHOW COLUMNS FROM `{table_name}`")
        columns = [row[0] for row in cursor.fetchall()]

        # 过滤出课程字段（排除系统字段）
        system_fields = ['id', 'class_name', 'student_id', 'name', '总分', 'class_rank', 'grade_rank']
        course_fields = [col for col in columns if col not in system_fields]

        # 确保DataFrame中有这些课程字段
        available_courses = [course for course in course_fields if course in df.columns]

        # 自动生成班级排名
        df = df.sort_values(by=['总分', '数学', '语文'], ascending=[False, False, False]).reset_index(drop=True)
        df['class_rank'] = df.index + 1
        success_count = 0

        for idx, row in df.iterrows():
            try:
                # 构建动态插入语句
                insert_fields = ['class_name', 'student_id', 'name'] + available_courses + ['总分', 'class_rank', 'grade_rank']
                placeholders = ', '.join(['%s'] * len(insert_fields))
                fields_sql = ', '.join([f'`{field}`' for field in insert_fields])

                # 构建值列表
                values = [
                    class_name,
                    row['学号'],
                    row['姓名']
                ]

                # 添加课程成绩
                for course in available_courses:
                    values.append(row.get(course, None))

                # 添加总分、班级排名、年级排名
                values.extend([
                    row['总分'],
                    int(row['class_rank']),
                    0  # grade_rank 先设为0，后续统一更新
                ])

                cursor.execute(f"""
                    INSERT INTO `{table_name}` ({fields_sql})
                    VALUES ({placeholders})
                """, values)
                success_count += 1
            except Exception as e:
                logger.error(f"插入第{idx+1}行数据失败: {row.to_dict()}，原因: {str(e)}")
                continue
        logger.info(f"成功从Excel文件导入{success_count}条记录，失败{len(df)-success_count}条!")
    except Exception as e:
        logger.error(f"Error inserting scores for table {table_name}: {str(e)}")
        raise


def insert_teacher_data(cursor: Cursor, df: pd.DataFrame, grade: str) -> None:
    """插入教师数据"""
    try:
        # 从数据库获取科目列表
        try:
            cursor.execute("SELECT course_name FROM grade_courses ORDER BY course_name")
            results = cursor.fetchall()
            subjects_list = [row[0] for row in results] if results else ['语文', '数学', '英语', '物理', '化学', '生物']
        except Exception:
            subjects_list = ['语文', '数学', '英语', '物理', '化学', '生物']

        for index, row in df.iterrows():
            # 随机分配1个科目
            import random
            num_subjects = random.randint(1, 1)
            assigned_subjects = random.sample(subjects_list, num_subjects)
            subjects_json = ','.join(assigned_subjects)

            # 所有教师均不为班主任
            is_head_teacher = 0
            manage_class = '0'
            is_class_teacher = '0'
            certificate_level = row['任教证级'] if '任教证级' in row and pd.notna(row['任教证级']) else ''
            remark = row['备注'] if '备注' in row and pd.notna(row['备注']) else ''

            cursor.execute("""
                INSERT INTO teachers
                (username, password, name, phone, grade, is_head_teacher, manage_class, subjects, is_class_teacher, certificate_level, remark)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                row['账号'],
                row['密码'],
                row['名字'],
                row['电话号码'],
                grade,
                is_head_teacher,
                manage_class,
                subjects_json,
                is_class_teacher,
                certificate_level,
                remark
            ))
        logger.info(f"成功导入{len(df)}条教师记录到年级: {grade}")
    except Exception as e:
        logger.error(f"Error inserting teacher data in grade: {str(e)}")
        raise


def insert_student_data(cursor: Cursor, class_name: str, df: pd.DataFrame, grade: str) -> None:
    """插入学生数据"""
    try:
        for _, row in df.iterrows():
            values = {
                'username': row['账号'] if pd.notna(row['账号']) else None,
                'password': row['密码'] if pd.notna(row['密码']) else None,
                'student_id': row['学号'] if pd.notna(row['学号']) else None,
                'name': row['姓名'] if pd.notna(row['姓名']) else None,
                'class_name': class_name,
                'grade': grade
            }
            cursor.execute("""
                INSERT INTO students
                (username, password, student_id, name, class_name, grade)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                values['username'],
                values['password'],
                values['student_id'],
                values['name'],
                values['class_name'],
                values['grade']
            ))
        logger.info(f"成功导入{len(df)}条学生记录到年级: {grade}")
    except Exception as e:
        logger.error(f"Error inserting student data in grade {grade}: {str(e)}")
        raise


# ==================== 数据更新和统计函数 ====================
def update_grade_rank(grade_name: str, exam_name: str, cursor: Cursor) -> None:
    """自动计算所有学生的年级排名，并写入成绩表"""
    try:
        # 获取所有班级的成绩
        cursor.execute(f"SELECT id, student_id, name, 总分, 数学, 语文 FROM `{exam_name}`")
        all_rows = cursor.fetchall()
        # 生成DataFrame
        df = pd.DataFrame(all_rows, columns=['id', 'student_id', 'name', '总分', '数学', '语文'])
        # 排序并生成年级排名
        df = df.sort_values(by=['总分', '数学', '语文'], ascending=[False, False, False]).reset_index(drop=True)
        df['grade_rank'] = df.index + 1
        # 更新每个学生的年级排名
        for _, row in df.iterrows():
            cursor.execute(f"UPDATE `{exam_name}` SET grade_rank = %s WHERE id = %s", (int(row['grade_rank']), int(row['id'])))
        logger.info(f"已更新{exam_name}所有学生的年级排名!")
    except Exception as e:
        logger.error(f"Error updating grade_rank for {exam_name}: {str(e)}")
        raise


def update_exam_participant_absent(cursor: Cursor, exam_name: str, df: pd.DataFrame, class_name: str, grade_name: str) -> None:
    """统计参考人数和缺考人数，并写入exam表。缺考判定为成绩为0或空，且学生信息表有该学生。"""
    try:
        # 获取应考学生学号
        cursor.execute(f"SELECT student_id FROM students WHERE class_name=%s", (class_name,))
        should_students = set(row[0] for row in cursor.fetchall())
        # 成绩为0或空的学生
        absent_mask = (df['总分'].isna()) | (df['总分'] == 0) | (df['总分'] == '0')
        absent_students = set(df.loc[absent_mask, '学号'])
        # 缺考人数：应考且成绩为0或空
        absent_count = len(absent_students & should_students)
        # 参考人数：成绩不为0且不为空的学生数
        present_mask = (~df['总分'].isna()) & (df['总分'] != 0) & (df['总分'] != '0')
        participant_count = df.loc[present_mask, '学号'].isin(should_students).sum()
        cursor.execute("SELECT id FROM exam WHERE exam_name=%s", (exam_name,))
        if cursor.fetchone() is None:
            cursor.execute("INSERT INTO exam (exam_name, participant_count, absent_count) VALUES (%s, %s, %s)", (exam_name, participant_count, absent_count))
        else:
            cursor.execute("UPDATE exam SET participant_count=%s, absent_count=%s WHERE exam_name=%s", (participant_count, absent_count, exam_name))
        logger.info(f"考试{exam_name} 参考人数: {participant_count} 缺考人数: {absent_count}")
    except Exception as e:
        logger.error(f"Error updating exam participant/absent for {exam_name}: {str(e)}")
        raise


def final_update_exam_participant_absent(cursor: Cursor, exam_name: str) -> None:
    """所有数据导入后，统计数据库内每个考试表的参考人数和缺考人数，并写入exam表。"""
    try:
        # 检查表结构，若无'总分'字段则跳过
        cursor.execute(f"SHOW COLUMNS FROM `{exam_name}` LIKE '总分'")
        if cursor.fetchone() is None:
            return
        # 参考人数：总分不为空/NaN的记录数
        cursor.execute(f"SELECT COUNT(*) FROM `{exam_name}` WHERE 总分 IS NOT NULL")
        participant_count = cursor.fetchone()[0]
        # 缺考人数：总分为空或NaN的记录数
        cursor.execute(f"SELECT COUNT(*) FROM `{exam_name}` WHERE 总分 IS NULL")
        absent_count = cursor.fetchone()[0]
        cursor.execute("SELECT id FROM exam WHERE exam_name=%s", (exam_name,))
        if cursor.fetchone() is None:
            cursor.execute("INSERT INTO exam (exam_name, participant_count, absent_count) VALUES (%s, %s, %s)", (exam_name, participant_count, absent_count))
        else:
            cursor.execute("UPDATE exam SET participant_count=%s, absent_count=%s WHERE exam_name=%s", (participant_count, absent_count, exam_name))
        logger.info(f"[最终统计] 考试{exam_name} 参考人数: {participant_count} 缺考人数: {absent_count}")
    except Exception as e:
        logger.error(f"Error in final update exam participant/absent for {exam_name}: {str(e)}")
        raise


# ==================== 自动填充和补全函数 ====================
def auto_fill_class_table(grade: str):
    """自动填充班级表"""
    with get_db_connection() as (con, cursor):
        con.select_db(grade)
        # 获取所有班级名
        cursor.execute("SELECT DISTINCT class_name FROM students")
        class_names = [row[0] for row in cursor.fetchall()]
        for class_name in class_names:
            # 班级人数
            cursor.execute("SELECT COUNT(*) FROM students WHERE class_name=%s", (class_name,))
            class_size = cursor.fetchone()[0]
            # 班主任（用teachers表的manage_class=class_name且is_head_teacher=1）
            cursor.execute("SHOW TABLES LIKE 'teachers'")
            if cursor.fetchone():
                cursor.execute("SELECT name FROM teachers WHERE manage_class=%s AND is_head_teacher=1", (class_name,))
                head_teacher_row = cursor.fetchone()
                head_teacher = head_teacher_row[0] if head_teacher_row else '待分配'
            else:
                head_teacher = '待分配'
            # 插入或更新class表
            cursor.execute("SELECT id FROM class WHERE class_name=%s", (class_name,))
            if cursor.fetchone() is None:
                cursor.execute(
                    "INSERT INTO class (grade, class_name, class_size, head_teacher) VALUES (%s, %s, %s, %s)",
                    (grade, class_name, class_size, head_teacher)
                )
            else:
                cursor.execute(
                    "UPDATE class SET grade=%s, class_size=%s, head_teacher=%s WHERE class_name=%s",
                    (grade, class_size, head_teacher, class_name)
                )
        logger.info(f"{grade} 所有班级信息已补全")


def auto_fill_grade_courses_table(grade_name: str):
    """根据所有考试表字段自动补全年级课程表（grade_courses），自动分配教师和课程类型"""
    with get_db_connection() as (con, cursor):
        con.select_db(grade_name)
        # 获取所有考试表名
        cursor.execute("SHOW TABLES")
        all_tables = [row[0] for row in cursor.fetchall()]
        exam_tables = [t for t in all_tables if t not in ('class', 'exam', 'students', 'teachers', 'grade_courses')]
        all_courses = set()
        for table in exam_tables:
            cursor.execute(f"SHOW COLUMNS FROM `{table}`")
            columns = [row[0] for row in cursor.fetchall()]
            for col in columns:
                if col not in ['id', 'class_name', 'student_id', 'name', '总分', 'class_rank', 'grade_rank']:
                    all_courses.add(col)

        # 创建/补全grade_courses表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS grade_courses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_name VARCHAR(100) NOT NULL,
                course_code VARCHAR(50) NOT NULL,
                credit DECIMAL(3,1) DEFAULT 15.0,
                type VARCHAR(20) DEFAULT '必修',
                teacher_names TEXT,
                create_time DATETIME DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ''')

        # 从数据库获取必修课程列表，如果没有则使用默认
        try:
            cursor.execute("SELECT course_name FROM grade_courses WHERE type='必修'")
            results = cursor.fetchall()
            must_courses = [row[0] for row in results] if results else ['语文', '数学', '英语', '物理', '化学', '生物']
        except Exception:
            must_courses = ['语文', '数学', '英语', '物理', '化学', '生物']

        for course_name in all_courses:
            cursor.execute("SELECT COUNT(*) FROM grade_courses WHERE course_name=%s", (course_name,))
            if cursor.fetchone()[0] == 0:
                # 课程类型
                course_type = '必修' if course_name in must_courses else '选修'
                # 课程编号
                course_code = generate_random_course_code(course_name)
                # 自动分配教师
                cursor.execute("SHOW TABLES LIKE 'teachers'")
                if cursor.fetchone():
                    cursor.execute("SELECT name FROM teachers WHERE subjects LIKE %s", (f"%{course_name}%",))
                    teacher_rows = cursor.fetchall()
                    if teacher_rows:
                        teacher_names = ','.join([row[0] for row in teacher_rows])
                    else:
                        # 若无匹配，随机分配一名教师
                        cursor.execute("SELECT name FROM teachers ORDER BY RAND() LIMIT 1")
                        row = cursor.fetchone()
                        teacher_names = row[0] if row else '待分配'
                else:
                    teacher_names = '待分配'
                cursor.execute('''
                    INSERT INTO grade_courses (course_name, course_code, credit, type, teacher_names)
                    VALUES (%s, %s, %s, %s, %s)
                ''', (course_name, course_code, 15.0, course_type, teacher_names))
        logger.info(f"{grade_name} 课程表已自动补全（来源于考试表字段，自动分配教师/类型）")


def complete_teacher_names_in_grade_courses(grade_name: str):
    """补全grade_courses表中teacher_names为空的课程，自动分配教师"""
    with get_db_connection() as (con, cursor):
        con.select_db(grade_name)
        cursor.execute("SELECT id, course_name FROM grade_courses WHERE teacher_names IS NULL OR teacher_names = ''")
        rows = cursor.fetchall()
        for course_id, course_name in rows:
            cursor.execute("SHOW TABLES LIKE 'teachers'")
            if cursor.fetchone():
                # 优先分配subjects包含该课程的所有教师
                cursor.execute("SELECT name FROM teachers WHERE subjects LIKE %s", (f"%{course_name}%",))
                teacher_rows = cursor.fetchall()
                if teacher_rows:
                    teacher_names = ','.join([row[0] for row in teacher_rows])
                else:
                    # 若无匹配，随机分配一名教师
                    cursor.execute("SELECT name FROM teachers ORDER BY RAND() LIMIT 1")
                    row = cursor.fetchone()
                    teacher_names = row[0] if row else '待分配'
            else:
                teacher_names = '待分配'
            cursor.execute("UPDATE grade_courses SET teacher_names=%s WHERE id=%s", (teacher_names, course_id))
        logger.info(f"{grade_name} 课程表中teacher_names已自动补全")


def update_grade_courses_teacher_names(grade: str):
    """更新grade_courses表中teacher_names为空的课程，智能分配教师"""
    with get_db_connection() as (con, cursor):
        con.select_db(grade)
        cursor.execute("SELECT id, course_name FROM grade_courses WHERE teacher_names IS NULL OR teacher_names = ''")
        rows = cursor.fetchall()

        logger.info(f"开始为年级 {grade} 分配课程教师，共 {len(rows)} 门课程需要分配")

        for course_id, course_name in rows:
            cursor.execute("SHOW TABLES LIKE 'teachers'")
            if cursor.fetchone():
                # 先查看教师表中有哪些教师和科目
                cursor.execute("SELECT name, subjects FROM teachers")
                all_teachers = cursor.fetchall()
                logger.debug(f"年级 {grade} 共有 {len(all_teachers)} 名教师")

                # 查找教授该科目的教师
                matching_teachers = []
                for teacher_name, subjects in all_teachers:
                    if subjects and course_name in subjects:
                        matching_teachers.append(teacher_name)

                if matching_teachers:
                    teacher_names = ','.join(matching_teachers)
                    logger.info(f"课程 {course_name} 分配教师: {teacher_names}")
                else:
                    # 如果没有匹配的教师，随机分配一个教师
                    if all_teachers:
                        import random
                        random_teacher = random.choice(all_teachers)
                        teacher_names = random_teacher[0]
                        logger.info(f"课程 {course_name} 随机分配教师: {teacher_names}")
                    else:
                        teacher_names = '待分配'
                        logger.warning(f"课程 {course_name} 无可分配教师")
            else:
                teacher_names = '待分配'
                logger.warning(f"年级 {grade} 无教师表，课程 {course_name} 设为待分配")

            cursor.execute("UPDATE grade_courses SET teacher_names=%s WHERE id=%s", (teacher_names, course_id))

        logger.info(f"{grade} 课程表中teacher_names已更新完成")


def fix_existing_grade_courses_teacher_names():
    """修复现有数据库中grade_courses表的teacher_names字段"""
    try:
        with get_db_connection() as (con, cursor):
            # 获取所有数据库（年级）
            cursor.execute("SHOW DATABASES")
            all_dbs = cursor.fetchall()

            # 过滤出年级数据库
            grade_dbs = [
                db[0] for db in all_dbs
                if db[0] not in ['information_schema', 'mysql', 'performance_schema', 'sys', 'account_db', 'root']
            ]

            logger.info(f"发现年级数据库: {grade_dbs}")

            for grade in grade_dbs:
                logger.info(f"正在修复年级 {grade} 的课程教师分配...")
                con.select_db(grade)

                # 检查是否有grade_courses表
                cursor.execute("SHOW TABLES LIKE 'grade_courses'")
                if cursor.fetchone():
                    update_grade_courses_teacher_names(grade)
                else:
                    logger.warning(f"年级 {grade} 没有grade_courses表")

            logger.info("所有年级的课程教师分配修复完成")

    except Exception as e:
        logger.error(f"修复课程教师分配时发生错误: {str(e)}")
        raise


# ==================== Excel文件处理函数 ====================
def process_excel_file(file_path: str, grade_name: str, class_name: str) -> None:
    """处理成绩Excel文件"""
    try:
        logger.info(f"\n处理年级: {grade_name} 班级: {class_name}")
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        logger.info(f"发现{len(sheet_names)}次考试数据: {sheet_names}")

        with get_db_connection() as (con, cursor):
            cursor.execute("SHOW DATABASES")
            databases = [db[0] for db in cursor.fetchall()]
            if grade_name not in databases:
                create_database(cursor, grade_name)
            con.select_db(grade_name)
            logger.info(f"已切换到数据库 '{grade_name}'")

            for sheet_name in sheet_names:
                logger.info(f"\n正在处理考试: {sheet_name}")
                table_name = sheet_name
                create_score_table(cursor, table_name, grade_name)
                df = excel_file.parse(sheet_name)
                df = df.sort_values(by=['总分', '数学', '语文'], ascending=[False, False, False])
                df = df.reset_index(drop=True)
                insert_student_scores(cursor, table_name, class_name, df)
                update_exam_participant_absent(cursor, sheet_name, df, class_name, grade_name)

    except Exception as e:
        logger.error(f"Error processing Excel file {file_path}: {str(e)}")
        raise


def process_teacher_excel(file_path: str) -> None:
    """处理老师信息Excel文件，每个sheet为一个年级，导入到各自年级数据库"""
    try:
        logger.info(f"\n处理老师信息文件: {file_path}")
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        logger.info(f"发现{len(sheet_names)}个年级: {sheet_names}")

        with get_db_connection() as (con, cursor):
            for grade in sheet_names:
                logger.info(f"\n正在处理年级: {grade}")
                df = excel_file.parse(grade)
                # 创建年级数据库和表
                cursor.execute("SHOW DATABASES")
                databases = [db[0] for db in cursor.fetchall()]
                if grade not in databases:
                    create_database(cursor, grade)
                con.select_db(grade)
                create_teachers_table(cursor)
                insert_teacher_data(cursor, df, grade)
    except Exception as e:
        logger.error(f"Error processing teacher Excel file {file_path}: {str(e)}")
        raise


def process_student_excel(file_path: str) -> None:
    """处理学生信息Excel文件，每个sheet为一个班，导入到各自年级数据库"""
    try:
        logger.info(f"\n处理学生信息文件: {file_path}")
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        logger.info(f"发现{len(sheet_names)}个班: {sheet_names}")
        # 从文件名中提取年级
        basename = os.path.basename(file_path)
        if '高一' in basename:
            grade = '高一'
        elif '高二' in basename:
            grade = '高二'
        elif '高三' in basename:
            grade = '高三'
        else:
            grade = '未知'
        with get_db_connection() as (con, cursor):
            cursor.execute("SHOW DATABASES")
            databases = [db[0] for db in cursor.fetchall()]
            if grade not in databases:
                create_database(cursor, grade)
            con.select_db(grade)
            create_students_table(cursor)
            create_grade_courses_table(cursor, grade)
            for class_name in sheet_names:
                logger.info(f"\n正在处理班级: {class_name}")
                df = excel_file.parse(class_name)
                insert_student_data(cursor, class_name, df, grade)
    except Exception as e:
        logger.error(f"Error processing student Excel file {file_path}: {str(e)}")
        raise


# ==================== 查询函数 ====================
def get_class_list_by_grade(grade_name: str):
    """根据年级名获取该年级下所有班级列表（用class表）"""
    try:
        with get_db_connection() as (con, cursor):
            con.select_db(grade_name)
            cursor.execute("SELECT class_name FROM class ORDER BY class_name")
            results = cursor.fetchall()
            return [row[0] for row in results]
    except Exception as e:
        logger.error(f"Error getting class list for grade {grade_name}: {str(e)}")
        return []


def get_exam_list_by_grade(grade_name: str):
    """根据年级名获取该年级下所有考试列表（用exam表），按时间顺序排列"""
    try:
        with get_db_connection() as (con, cursor):
            con.select_db(grade_name)
            cursor.execute("SELECT exam_name FROM exam ORDER BY exam_name")
            results = cursor.fetchall()
            exam_list = [row[0] for row in results]
            # 考试排序逻辑
            sorted_exams = sorted(exam_list, key=get_exam_order)
            return sorted_exams
    except Exception as e:
        logger.error(f"Error getting exam list for grade {grade_name}: {str(e)}")
        return []


# ==================== 主函数 ====================
def main_scores() -> None:
    """主函数：处理成绩数据"""
    try:
        grade_dirs = [d for d in os.listdir(SCORE_DATA_DIR) if os.path.isdir(os.path.join(SCORE_DATA_DIR, d))]
        total_grades = len(grade_dirs)
        for g_idx, grade_name in enumerate(grade_dirs, 1):
            grade_path = os.path.join(SCORE_DATA_DIR, grade_name)
            excel_files = [f for f in os.listdir(grade_path) if f.endswith('.xlsx')]
            total_classes = len(excel_files)
            print(f"\n>>> 正在导入第{g_idx}/{total_grades}个年级 [{grade_name}]，共{total_classes}个班级...")
            for c_idx, excel_file in enumerate(excel_files, 1):
                class_name = os.path.splitext(excel_file)[0]
                file_path = os.path.join(grade_path, excel_file)
                excel_file_obj = pd.ExcelFile(file_path)
                sheet_names = excel_file_obj.sheet_names
                total_exams = len(sheet_names)
                print(f"  - 正在导入第{c_idx}/{total_classes}个班级 [{class_name}]，共{total_exams}次考试...")
                with get_db_connection() as (con, cursor):
                    cursor.execute("SHOW DATABASES")
                    databases = [db[0] for db in cursor.fetchall()]
                    if grade_name not in databases:
                        create_database(cursor, grade_name)
                    con.select_db(grade_name)
                    # 自动建表
                    create_class_table(cursor)
                    create_exam_table(cursor)
                    create_students_table(cursor)
                    for e_idx, sheet_name in enumerate(sheet_names, 1):
                        table_name = sheet_name
                        print(f"    · 正在导入第{e_idx}/{total_exams}次考试 [{sheet_name}] ...")
                        create_score_table(cursor, table_name, grade_name)
                        df = excel_file_obj.parse(sheet_name)
                        df = df.sort_values(by=['总分', '数学', '语文'], ascending=[False, False, False])
                        df = df.reset_index(drop=True)
                        insert_student_scores(cursor, table_name, class_name, df)
                        update_exam_participant_absent(cursor, sheet_name, df, class_name, grade_name)
                # 导入完所有班级后，补全年级排名
                with get_db_connection() as (con, cursor):
                    con.select_db(grade_name)
                    for sheet_name in sheet_names:
                        update_grade_rank(grade_name, sheet_name, cursor)
        # 所有数据导入和排名后，最终统计数据库内的参考人数和缺考人数
        for grade_name in grade_dirs:
            with get_db_connection() as (con, cursor):
                con.select_db(grade_name)
                cursor.execute("SHOW TABLES")
                all_tables = [row[0] for row in cursor.fetchall()]
                # 排除非考试表
                for table in all_tables:
                    if table not in ('class', 'exam', 'students', 'teachers'):
                        final_update_exam_participant_absent(cursor, table)
        # 补全所有年级的class表
        for grade_name in grade_dirs:
            auto_fill_class_table(grade_name)
        # 补全所有年级的课程表
        for grade_name in grade_dirs:
            auto_fill_grade_courses_table(grade_name)
        # 补全所有年级课程表中的teacher_names
        for grade_name in grade_dirs:
            complete_teacher_names_in_grade_courses(grade_name)
        logger.info("所有成绩数据库操作完成，连接已关闭")
    except Exception as e:
        logger.error(f"Error in main_scores function: {str(e)}")
        raise


def main_accounts() -> None:
    """主函数：处理账号数据"""
    try:
        excel_files = [f for f in os.listdir(ACCOUNT_DATA_DIR) if f.endswith('.xlsx')]
        # 1. 先收集所有年级名
        all_grades = set()
        for excel_file in excel_files:
            file_path = os.path.join(ACCOUNT_DATA_DIR, excel_file)
            if '老师' in excel_file:
                excel_file_obj = pd.ExcelFile(file_path)
                all_grades.update(excel_file_obj.sheet_names)
            elif '学生' in excel_file:
                basename = os.path.basename(file_path)
                if '高一' in basename:
                    all_grades.add('高一')
                elif '高二' in basename:
                    all_grades.add('高二')
                elif '高三' in basename:
                    all_grades.add('高三')
        # 2. 先批量创建所有年级数据库和表
        with get_db_connection() as (con, cursor):
            for grade in all_grades:
                cursor.execute("SHOW DATABASES")
                databases = [db[0] for db in cursor.fetchall()]
                if grade not in databases:
                    create_database(cursor, grade)
                con.select_db(grade)
                create_class_table(cursor)
                create_exam_table(cursor)
                create_teachers_table(cursor)
                create_students_table(cursor)
                create_grade_courses_table(cursor, grade)
        # 3. 再批量导入老师和学生数据
        for excel_file in excel_files:
            file_path = os.path.join(ACCOUNT_DATA_DIR, excel_file)
            if '老师' in excel_file:
                excel_file_obj = pd.ExcelFile(file_path)
                sheet_names = excel_file_obj.sheet_names
                with get_db_connection() as (con, cursor):
                    for grade in sheet_names:
                        con.select_db(grade)
                        df = excel_file_obj.parse(grade)
                        insert_teacher_data(cursor, df, grade)
            elif '学生' in excel_file:
                basename = os.path.basename(file_path)
                if '高一' in basename:
                    grade = '高一'
                elif '高二' in basename:
                    grade = '高二'
                elif '高三' in basename:
                    grade = '高三'
                else:
                    grade = '未知'
                excel_file_obj = pd.ExcelFile(file_path)
                sheet_names = excel_file_obj.sheet_names
                with get_db_connection() as (con, cursor):
                    con.select_db(grade)
                    for class_name in sheet_names:
                        logger.info(f"正在处理班级: {class_name}")
                        df = excel_file_obj.parse(class_name)
                        insert_student_data(cursor, class_name, df, grade)
            else:
                logger.warning(f"未识别的文件类型: {excel_file}")
        # 补全所有年级的class表
        for grade in all_grades:
            auto_fill_class_table(grade)
        # 更新所有年级的grade_courses表中的teacher_names
        for grade in all_grades:
            update_grade_courses_teacher_names(grade)
        logger.info("所有账号数据库操作完成，连接已关闭")
    except Exception as e:
        logger.error(f"Error in main_accounts function: {str(e)}")
        raise


def main() -> None:
    """主函数：整合处理成绩和账号数据"""
    try:
        print("开始处理Excel文件数据转化成MySQL...")
        print("1. 处理成绩数据...")
        main_scores()
        print("2. 处理账号数据...")
        main_accounts()
        print("所有数据处理完成！")
    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        raise


if __name__ == "__main__":
    main()
