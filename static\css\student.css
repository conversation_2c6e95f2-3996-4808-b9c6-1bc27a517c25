/* =====================
   学生个人中心样式（student.css）
   ===================== */

/* ========== 1. 欢迎区域 ========== */
.welcome-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    animation: fadeInUp 0.6s ease-out;
}

.welcome-section h1 {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    font-weight: 700;
    margin-bottom: 1rem;
}

.welcome-section p {
    color: rgba(255,255,255,0.9) !important;
    font-size: 1.1rem;
    margin: 0;
}

/* ========== 2. 学生页面特定卡片样式 ========== */
/* 继承public.css中的基础卡片样式，这里只定义学生页面特有的样式 */

/* ========== 3. 学生信息面板 ========== */
#studentInfo {
    text-align: center;
}

#studentInfo .bi-person-circle {
    color: var(--primary-color, #007bff);
    margin-bottom: 1rem;
    font-size: 3rem;
}

#studentName {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

#studentClass,
#studentGrade {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

/* ========== 4. 学生页面特定按钮样式 ========== */
/* 继承public.css中的基础按钮样式，这里只定义学生页面特有的按钮样式 */

/* ========== 5. 内容面板样式 ========== */
#messagePanel,
#leavePanel,
#gradePanel {
    min-height: 500px;
    animation: fadeInRight 0.8s ease-out;
}

/* ========== 6. 消息提示样式 ========== */
.alert {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    animation: slideInUp 0.4s ease-out;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.alert-heading {
    color: #0c5460;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* ========== 7. 学生页面特定表格样式 ========== */
/* 继承public.css中的基础表格样式，这里只定义学生页面特有的表格样式 */
.table tbody tr:hover {
    transform: translateY(-1px);
}

/* ========== 8. 学生页面特定徽章样式 ========== */
/* 继承public.css中的基础徽章样式，这里只定义学生页面特有的徽章样式 */

/* ========== 9. 学生页面特定模态框样式 ========== */
/* 继承public.css中的基础模态框样式，这里只定义学生页面特有的模态框样式 */

/* ========== 10. 学生页面特定表单样式 ========== */
/* 继承public.css中的基础表单样式，这里只定义学生页面特有的表单样式 */
.form-control:focus {
    transform: translateY(-1px);
}

/* ========== 11. 学生页面特定工具样式 ========== */
/* 继承public.css中的基础工具样式，这里只定义学生页面特有的工具样式 */



/* ========== 12. 滚动条样式 ========== */
.list-group::-webkit-scrollbar {
    width: 6px;
}

.list-group::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.list-group::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.list-group::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ========== 13. 学生页面特定错误容器样式 ========== */
/* 继承public.css中的基础错误容器样式，这里只定义学生页面特有的错误容器样式 */

/* ========== 14. 成绩内容样式 ========== */
#gradeContent .card {
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

#gradeContent .card:hover {
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 4px 15px rgba(0,123,255,0.1);
    transform: translateY(-2px);
}

#gradeContent .card-title {
    color: #495057;
    font-weight: 600;
    font-size: 1.1rem;
}

#gradeContent .card-text {
    color: #6c757d;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

/* ========== 15. 学生留言文本样式 ========== */
.student-message-text {
    word-break: break-all;
    white-space: pre-line;
    font-size: 1rem;
    line-height: 1.6;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
}

/* ========== 16. 请假系统样式 ========== */
/* 请假面板基础样式 */
.leave-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    animation: fadeInUp 0.6s ease-out;
}

/* 请假表格样式 */
#leavePanel .table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    background: white;
}

#leavePanel .table thead th {
    background: linear-gradient(135deg, var(--primary-color, #007bff) 0%, #0056b3 100%);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem 0.75rem;
}

#leavePanel .table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

#leavePanel .table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

#leavePanel .table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    word-wrap: break-word;
    word-break: break-word;
}

/* 请假状态徽章增强样式 */
#leavePanel .badge {
    border-radius: 20px;
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
}

/* 请假申请模态框样式 */
#leaveModal .modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
}

#leaveModal .modal-header {
    background: linear-gradient(135deg, var(--primary-color, #007bff) 0%, #0056b3 100%);
    border: none;
    padding: 1.5rem;
}

#leaveModal .modal-title {
    color: white;
    font-weight: 600;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#leaveModal .modal-body {
    padding: 2rem;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
}

#leaveModal .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

#leaveModal .form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

#leaveModal .form-control:focus {
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: translateY(-1px);
}

#leaveModal .form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

#leaveModal .alert {
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* 销假确认模态框样式 */
#cancelLeaveModal .modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
}

#cancelLeaveModal .modal-header {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    border: none;
    padding: 1.5rem;
}

#cancelLeaveModal .modal-title {
    color: white;
    font-weight: 600;
    font-size: 1.25rem;
}

#cancelLeaveModal .modal-body {
    padding: 2rem;
    text-align: center;
}

#cancelLeaveModal .alert {
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

/* 请假操作按钮样式 */
#leavePanel .btn-outline-info {
    color: #17a2b8;
    border-color: #17a2b8;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

#leavePanel .btn-outline-info:hover {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    border-color: #17a2b8;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23,162,184,0.3);
}

/* ========== 18. 快速操作按钮 ========== */
#quickLeaveBtn {
    background: linear-gradient(135deg, var(--primary-color, #007bff) 0%, #0056b3 100%);
    border: none;
    color: white;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

#quickLeaveBtn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,123,255,0.4);
}

#quickMessageBtn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40,167,69,0.3);
}

#quickMessageBtn:hover {
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40,167,69,0.4);
}

/* 刷新按钮样式 */
#refreshLeaveBtn {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

#refreshLeaveBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 字数统计样式 */
#reasonCount {
    font-weight: 500;
    transition: color 0.3s ease;
}

#reasonCount.text-warning {
    color: #ffc107 !important;
}

#reasonCount.text-danger {
    color: #dc3545 !important;
}

/* Toast样式优化 */
.toast {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    border: none;
}

.toast .toast-body {
    font-weight: 500;
    padding: 1rem 1.25rem;
}

/* 空状态样式 */
#leavePanel .text-muted {
    color: #6c757d !important;
}

#leavePanel .bi {
    font-size: 1.2em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #leaveModal .modal-dialog {
        margin: 1rem;
    }
    
    #leaveModal .modal-body {
        padding: 1.5rem;
    }
    
    #leavePanel .table-responsive {
        border-radius: 10px;
    }
    
    #leavePanel .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}

/* 动画效果 */
#leavePanel .animate__fadeInRight {
    animation-duration: 0.8s;
}

/* 加载状态样式 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 渐变背景样式 */
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

/* 请假原因textarea动态调整样式 */
#leaveReason {
    transition: all 0.3s ease;
    border: 2px solid #e0e7ef;
    border-radius: 12px;
    background: #f8fafc;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03);
    font-size: 1rem;
    line-height: 1.6;
    padding: 12px 16px;
}

#leaveReason:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px #b3d8ff;
    background: #fff;
    outline: none;
}

#leaveReason.auto-resize {
    overflow: hidden;
    resize: none;
}

/* 请假模态框中的textarea容器 */
#leaveModal .form-control {
    border-radius: 12px;
    transition: all 0.3s ease;
}

#leaveModal .form-control:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px #b3d8ff;
}

/* 字数统计样式优化 */
#reasonCount {
    font-weight: 500;
    transition: color 0.3s ease;
    font-size: 0.875rem;
}

#reasonCount.text-warning {
    color: #ffc107 !important;
    font-weight: 600;
}

#reasonCount.text-danger {
    color: #dc3545 !important;
    font-weight: 600;
}

/* 请假表格中的原因列样式 */
#leavePanel .table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
    word-wrap: break-word;
    word-break: break-word;
}

#leavePanel .leave-reason-cell {
    max-width: 200px;
    min-width: 150px;
    position: relative;
    cursor: pointer;
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 10px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

#leavePanel .leave-reason-cell:hover {
    background: #e9ecef;
    border-color: #17a2b8;
    box-shadow: 0 3px 10px rgba(23, 162, 184, 0.15);
    transform: translateY(-2px);
}

#leavePanel .leave-reason-cell.clickable {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    position: relative;
}

#leavePanel .leave-reason-cell.clickable::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 12px 12px 0;
    border-color: transparent #17a2b8 transparent transparent;
    border-radius: 0 8px 0 0;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

#leavePanel .leave-reason-cell.clickable:hover::after {
    opacity: 1;
}

#leavePanel .leave-reason-cell.clickable:hover {
    background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
    border-color: #17a2b8;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.2);
}

#leavePanel .leave-reason-text {
    display: block;
    max-width: 100%;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-word;
    line-height: 1.5;
    min-height: 1.5em;
    max-height: 4.5em; /* 最多显示3行 */
    overflow: hidden;
    position: relative;
    color: #495057;
    font-size: 0.95em;
    font-weight: 400;
}

/* 当内容超过3行时显示省略号 */
#leavePanel .leave-reason-text.truncated::after {
    content: '...';
    position: absolute;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent 0%, #f8f9fa 40%);
    padding-left: 12px;
    color: #6c757d;
    font-weight: 600;
    font-size: 1.2em;
    line-height: 1;
}

/* 完整显示请假原因的模态框样式 */
.leave-reason-modal .modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    transform: scale(0.8);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.leave-reason-modal.show .modal-content {
    transform: scale(1);
    opacity: 1;
}

/* 模态框背景动画 */
.leave-reason-modal .modal-backdrop {
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
}

/* 模态框内容进入动画 */
.leave-reason-modal .modal-header,
.leave-reason-modal .modal-body,
.leave-reason-modal .modal-footer {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.5s ease;
    transition-delay: 0.1s;
}

.leave-reason-modal.show .modal-header,
.leave-reason-modal.show .modal-body,
.leave-reason-modal.show .modal-footer {
    opacity: 1;
    transform: translateY(0);
}

.leave-reason-modal .modal-body {
    transition-delay: 0.2s;
}

.leave-reason-modal .modal-footer {
    transition-delay: 0.3s;
}

.leave-reason-modal .modal-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-bottom: none;
    padding: 2rem 2.5rem;
    position: relative;
    overflow: hidden;
}

.leave-reason-modal .modal-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
}

.leave-reason-modal .modal-title {
    font-weight: 700;
    font-size: 1.3em;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    z-index: 1;
}

.leave-reason-modal .modal-body {
    max-height: 65vh;
    overflow-y: auto;
    padding: 2.5rem;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
}

.leave-reason-modal .reason-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.9;
    font-size: 1.15em;
    color: #2c3e50;
    background: white;
    padding: 2rem;
    border-radius: 16px;
    border-left: 8px solid #17a2b8;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: all 0.3s ease;
}

.leave-reason-modal .reason-content:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.leave-reason-modal .reason-content::before {
    content: '"';
    position: absolute;
    top: -20px;
    left: 30px;
    font-size: 5em;
    color: #17a2b8;
    opacity: 0.15;
    font-family: serif;
    line-height: 1;
    font-weight: 300;
}

.leave-reason-modal .reason-content::after {
    content: '"';
    position: absolute;
    bottom: -20px;
    right: 30px;
    font-size: 5em;
    color: #17a2b8;
    opacity: 0.15;
    font-family: serif;
    line-height: 1;
    font-weight: 300;
}

.leave-reason-modal .modal-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    padding: 1.5rem 2.5rem;
}

.leave-reason-modal .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border: none;
    border-radius: 12px;
    padding: 0.875rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.2);
}

.leave-reason-modal .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
    background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
}

/* 响应式设计中的请假原因调整 */
@media (max-width: 768px) {
    #leavePanel .leave-reason-cell {
        max-width: 120px;
        min-width: 100px;
        padding: 6px 8px;
    }
    
    #leavePanel .leave-reason-text {
        max-height: 2.8em; /* 移动端最多显示2行 */
        font-size: 0.9em;
    }
    
    .leave-reason-modal .modal-dialog {
        margin: 1rem;
    }
    
    .leave-reason-modal .modal-body {
        padding: 1rem;
    }
    
    .leave-reason-modal .reason-content {
        padding: 1rem;
        font-size: 1em;
    }
}

/* ========== 17. 成绩分析系统 ========== */
/* 成绩分析卡片 */
.grade-analysis-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-top: 2rem;
    animation: fadeInUp 0.6s ease-out;
}

.grade-analysis-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.grade-analysis-card .card-header {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
    border-radius: 20px 20px 0 0 !important;
    border: none;
    padding: 1.5rem;
}

.grade-analysis-card .card-title {
    font-weight: 600;
    margin: 0;
    font-size: 1.1rem;
}

.grade-analysis-card .card-body {
    padding: 2rem;
}

/* ========== 18. 成绩趋势分析 ========== */
/* 成绩趋势卡片 */
.grade-trend-card {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border: none;
    border-radius: 18px;
    box-shadow: 0 6px 25px rgba(0,123,255,0.1);
    transition: all 0.3s ease;
    animation: fadeInUp 0.6s ease-out;
}

.grade-trend-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 35px rgba(0,123,255,0.15);
}

.grade-trend-card .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 18px 18px 0 0 !important;
    border: none;
    padding: 1.2rem 1.8rem;
}

.grade-trend-card .card-header h6 {
    color: white;
    font-weight: 600;
    margin: 0;
    font-size: 1.1rem;
}

.grade-trend-card .card-body {
    padding: 1.8rem;
}

.analysis-item {
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #e9ecef;
}

.subject-analysis-item {
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.subject-analysis-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 趋势项目 */
.trend-item {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.trend-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.trend-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    transition: width 0.3s ease;
}

.trend-item:hover::before {
    width: 6px;
}

/* 科目趋势项目 */
.subject-trend-item {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
}

.subject-trend-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* 趋势指标 */
.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.trend-indicator.up {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.trend-indicator.down {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.trend-indicator.stable {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

/* 趋势图表容器 */
.trend-chart-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border: 1px solid #e9ecef;
}

.trend-chart-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    text-align: center;
}

/* 趋势数据展示 */
.trend-data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.trend-data-item {
    text-align: center;
    padding: 0.75rem;
    background: rgba(255,255,255,0.8);
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.trend-data-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 0.25rem;
}

.trend-data-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.stat-item {
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #e9ecef;
}

.total-score-analysis {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

.class-stats {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

/* 成绩分数项样式优化 */
.grade-score-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.grade-score-item:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.grade-score-item.my-score {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #2196f3;
}

.grade-score-item.avg-score {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-color: #4caf50;
}

.grade-total-score {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    border: 2px solid #ff9800;
    box-shadow: 0 2px 10px rgba(255,152,0,0.2);
}

.grade-rank-info {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin-top: 0.5rem;
    border: 1px solid #9c27b0;
}

.score-value {
    font-weight: 600;
    color: #495057;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .grade-analysis-card .row {
        margin: 0;
    }
    
    .grade-analysis-card .col-md-3,
    .grade-analysis-card .col-md-6 {
        padding: 0.25rem;
    }
    
    .trend-item,
    .subject-trend-item {
        margin-bottom: 0.5rem;
    }
}

/* ========== 19. 学生页面特定动画效果 ========== */
/* 继承public.css中的基础动画，这里只定义学生页面特有的动画 */

/* 应用动画到组件 */
.grade-analysis-card,
.grade-trend-card,
.learning-advice-card {
    animation: fadeInUp 0.6s ease-out;
}

/* 考试选择区域样式 */
.exam-select-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.exam-select-section .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
}

.exam-select-section .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.exam-select-section .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.exam-select-section .btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.exam-select-section .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 图表区域样式 */
.charts-section {
    background: white;
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.charts-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.charts-section .card-header {
    background: linear-gradient(135deg, #b6dde6 0%, #82afe3 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
    padding: 1rem 1.5rem;
}

.charts-section .card-body {
    padding: 1.5rem;
}

.chart-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.chart-card .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.chart-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.chart-card .btn-group .btn {
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.chart-card .card-body {
    padding: 1rem;
}

.chart-card img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.chart-card img:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .exam-select-section {
        padding: 1rem;
    }
    
    .grade-detail-card .card-body {
        padding: 1rem;
    }
    
    .grade-score-item {
        padding: 0.5rem 0.75rem;
    }
    
    .chart-card {
        margin-bottom: 1rem;
    }
    
    .chart-card img {
        max-width: 100%;
    }
    
    .overall-stat-item {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .overall-stat-item .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .overall-stat-item .stat-value {
        font-size: 1.3rem;
    }
    
    .subject-avg-item {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }
    
    .subject-avg-item .subject-score {
        font-size: 1.1rem;
    }
}

/* ========== 20. 学习建议系统 ========== */
/* 学习建议卡片 */
.learning-advice-card {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-top: 1.5rem;
    animation: fadeInUp 0.6s ease-out;
}

.learning-advice-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.learning-advice-card .card-header {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
    padding: 1rem 1.5rem;
}

.learning-advice-card .card-body {
    padding: 1.5rem;
}

/* 建议项目 */
.advice-item {
    background: white;
    border-radius: 12px;
    padding: 1.2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    margin-bottom: 1rem;
    animation: slideInFromLeft 0.5s ease-out;
}

.advice-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.advice-item:nth-child(even) {
    animation: slideInFromRight 0.5s ease-out;
}

.advice-item.strong {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.advice-item.weak {
    border-left: 4px solid #dc3545;
    background: linear-gradient(135deg, #fff8f8 0%, #ffe8e8 100%);
}

.advice-item.neutral {
    border-left: 4px solid #17a2b8;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 建议图标 */
.advice-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.advice-icon.strong {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.advice-icon.weak {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.advice-icon.neutral {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* 建议内容 */
.advice-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.advice-description {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.5;
}

.advice-suggestions {
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 分析项样式 */
.analysis-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.analysis-value {
    font-size: 1.3rem;
    font-weight: 700;
    line-height: 1;
}

/* 科目建议样式 */
.subject-suggestion {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255,255,255,0.8);
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.suggestion-label {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.suggestion-content {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.5;
}

/* 分析项样式 */
.analysis-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.analysis-value {
    font-size: 1.3rem;
    font-weight: 700;
    line-height: 1;
}

/* 科目建议样式 */
.subject-suggestion {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255,255,255,0.8);
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.suggestion-label {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.suggestion-content {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.5;
}

/* 考试选择区域样式优化 */
.exam-select-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.exam-select-section:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.exam-select-section .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
}

.exam-select-section .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.exam-select-section .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.exam-select-section .btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.exam-select-section .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .advice-item {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }
    
    .advice-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .advice-title {
        font-size: 1rem;
    }
    
    .advice-description {
        font-size: 0.9rem;
    }
    
    .advice-suggestions {
        font-size: 0.85rem;
    }
    
    .exam-select-section {
        padding: 1rem;
    }
    
    .learning-advice-card .card-body {
        padding: 1rem;
    }
}

/* 继承public.css中的动画效果 */

.advice-item, .prediction-item, .subject-detail-item {
    animation: slideInFromLeft 0.5s ease-out;
}

.advice-item:nth-child(even), .prediction-item:nth-child(even) {
    animation: slideInFromRight 0.5s ease-out;
}

/* 继承public.css中的加载状态样式 */

/* 工具提示样式 */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip-custom:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 成绩等级徽章 */
.grade-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.grade-badge.excellent {
    background: #4caf50;
    color: white;
}

.grade-badge.good {
    background: #2196f3;
    color: white;
}

.grade-badge.average {
    background: #ff9800;
    color: white;
}

.grade-badge.pass {
    background: #ff9800;
    color: white;
}

.grade-badge.weak {
    background: #f44336;
    color: white;
}

/* 趋势指示器 */
.trend-indicator-enhanced {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.trend-indicator-enhanced.up {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.trend-indicator-enhanced.down {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.trend-indicator-enhanced.stable {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

/* 班级统计卡片样式 */
.class-stats-section {
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    flex-shrink: 0;
}

.stat-card.total-students .stat-icon {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.stat-card.pass-rate .stat-icon {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.stat-card.highest-score .stat-icon {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: white;
}

.stat-card.lowest-score .stat-icon {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.stat-card .stat-content {
    flex-grow: 1;
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-card .stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* 成绩对比面板样式 */
.grade-comparison-section {
    margin-bottom: 2rem;
}

.score-panel {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
}

.score-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.score-panel .panel-header {
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
    border-bottom: 1px solid #f1f3f4;
}

.score-panel .panel-body {
    padding: 1.5rem;
}

.my-score-panel {
    border-left: 4px solid #007bff;
}

.avg-score-panel {
    border-left: 4px solid #28a745;
}

/* 成绩详情卡片样式优化 */
.grade-detail-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.grade-detail-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.grade-detail-card .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 20px 20px 0 0 !important;
    border: none;
    padding: 1.5rem;
}

.grade-detail-card .card-body {
    padding: 2rem;
}

/* 成绩项目样式优化 */
.grade-score-item {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.grade-score-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.grade-score-item.my-score {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 4px solid #007bff;
}

.grade-score-item.avg-score {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-left: 4px solid #28a745;
}

.grade-score-item .score-value {
    font-weight: 600;
    font-size: 1.1rem;
}

.grade-score-item.my-score .score-value {
    color: #007bff;
}

.grade-score-item.avg-score .score-value {
    color: #28a745;
}

/* 科目详细分析样式优化 */
.subject-analysis-item {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.subject-analysis-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.subject-analysis-item.excellent {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.subject-analysis-item.good {
    border-left: 4px solid #007bff;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
}

.subject-analysis-item.average {
    border-left: 4px solid #ffc107;
    background: linear-gradient(135deg, #fffbf0 0%, #fff3cd 100%);
}

.subject-analysis-item.pass {
    border-left: 4px solid #ff9800;
    background: linear-gradient(135deg, #fffbf0 0%, #fff3cd 100%);
}

.subject-analysis-item.weak {
    border-left: 4px solid #dc3545;
    background: linear-gradient(135deg, #fff8f8 0%, #ffe8e8 100%);
}

.subject-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.subject-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
}

.subject-level {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.subject-level.excellent {
    background: #d4edda;
    color: #155724;
}

.subject-level.good {
    background: #d1ecf1;
    color: #0c5460;
}

.subject-level.average {
    background: #fff3cd;
    color: #856404;
}

.subject-level.pass {
    background: #fff3cd;
    color: #856404;
}

.subject-level.weak {
    background: #f8d7da;
    color: #721c24;
}

.subject-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.subject-stat {
    background: rgba(255,255,255,0.8);
    border-radius: 8px;
    padding: 0.75rem;
    text-align: center;
    border: 1px solid #e9ecef;
}

.subject-stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.subject-stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.progress-section {
    margin-bottom: 1rem;
}

.progress-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.progress-label {
    width: 80px;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.progress-bar-custom {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    margin: 0 1rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.6s ease;
}

.progress-fill.excellent {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.progress-fill.good {
    background: linear-gradient(90deg, #007bff, #17a2b8);
}

.progress-fill.average {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.progress-fill.pass {
    background: linear-gradient(90deg, #ff9800, #fd7e14);
}

.progress-fill.weak {
    background: linear-gradient(90deg, #dc3545, #e83e8c);
}

.progress-value {
    width: 50px;
    text-align: right;
    font-size: 0.9rem;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stat-card {
        padding: 1rem;
        gap: 0.75rem;
    }
    
    .stat-card .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .stat-card .stat-value {
        font-size: 1.5rem;
    }
    
    .score-panel .panel-header,
    .score-panel .panel-body {
        padding: 1rem;
    }
    
    .grade-detail-card .card-body {
        padding: 1.5rem;
    }
    
    .total-score-analysis {
        padding: 1.5rem !important;
    }
    
    .subject-analysis-item {
        padding: 1rem;
    }
    
    .subject-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .progress-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .progress-bar-custom {
        width: 100%;
        margin: 0;
    }
}

/* === 整体成绩概览卡片化样式优化 === */
.overall-grade-card {
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.10);
    background: #fff;
    margin-bottom: 1.5rem;
    border: none;
}
.overall-grade-card .card-header {
    background: linear-gradient(90deg, #e3f0ff 0%, #f8f9fa 100%);
    border-radius: 18px 18px 0 0;
    border-bottom: 1px solid #e3eaf2;
    padding: 1rem 1.5rem;
}
.overall-grade-card .card-title, .overall-grade-card h6 {
    font-weight: 700;
    color: #2563eb;
    font-size: 1.18rem;
}
.overall-grade-card .card-body {
    padding: 2rem 1.5rem 1.5rem 1.5rem;
}

.overall-stat-item {
    display: flex;
    align-items: center;
    background: #f6faff;
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(80,120,255,0.04);
    padding: 1.1rem 1.2rem;
    transition: box-shadow 0.2s;
    min-height: 90px;
    margin-bottom: 0.5rem;
}
.overall-stat-item:hover {
    box-shadow: 0 6px 18px rgba(80,120,255,0.10);
}
.stat-icon {
    font-size: 2.2rem;
    margin-right: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.8rem;
    height: 2.8rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #e3f0ff 0%, #f8fafc 100%);
}
.stat-content {
    flex: 1;
}
.stat-label {
    font-size: 1.02rem;
    color: #6b7280;
    margin-bottom: 0.2rem;
}
.stat-value {
    font-size: 1.55rem;
    font-weight: 700;
    color: #22223b;
}

/* 进步幅度正负色彩 */
#improvementRange.positive { color: #22c55e; }
#improvementRange.negative { color: #ef4444; }

/* 各科平均分卡片 */
.subject-avg-item {
    background: #f8fafc;
    border-radius: 12px;
    box-shadow: 0 1px 4px rgba(80,120,255,0.04);
    padding: 1.1rem 0.5rem 0.8rem 0.5rem;
    text-align: center;
    margin-bottom: 0.5rem;
    transition: box-shadow 0.2s;
}
.subject-avg-item:hover {
    box-shadow: 0 4px 12px rgba(80,120,255,0.10);
}
.subject-name {
    font-weight: 700;
    color: #374151;
    font-size: 1.08rem;
    margin-bottom: 0.3rem;
}
.subject-score {
    font-size: 1.45rem;
    font-weight: 600;
    color: #2563eb;
    letter-spacing: 0.5px;
}
/* 低分红色，高分橙色/蓝色 */
.subject-score.low { color: #ef4444; }
.subject-score.mid { color: #f59e42; }
.subject-score.high { color: #22c55e; }
.subject-score.top { color: #2563eb; }

@media (max-width: 991.98px) {
    .overall-grade-card .card-body { padding: 1.2rem 0.7rem; }
    .overall-stat-item { padding: 0.8rem 0.7rem; }
    .subject-avg-item { padding: 0.8rem 0.3rem; }
}

/* 概览区趋势图图片样式 */
.overview-chart {
    width: 100%;
    max-width: 420px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    margin: 0 auto 16px;
    display: block;
}

/* === 各科平均分卡片优化样式 === */
.subject-avg-list {
    display: flex;
    flex-wrap: wrap;
    gap: 1.2rem;
    margin-bottom: 1.5rem;
    justify-content: flex-start;
}

.subject-avg-item {
    background: linear-gradient(135deg, #f8fafc 60%, #e3f0ff 100%);
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(80,120,255,0.08);
    padding: 1.2rem 1.1rem 1rem 1.1rem;
    text-align: center;
    min-width: 120px;
    min-height: 90px;
    margin-bottom: 0;
    transition: box-shadow 0.25s, transform 0.25s;
    border: 2px solid #e3eaf2;
    position: relative;
    overflow: hidden;
}
.subject-avg-item:hover {
    box-shadow: 0 8px 24px rgba(80,120,255,0.18);
    transform: translateY(-4px) scale(1.04);
    z-index: 2;
}

.subject-avg-item .subject-name {
    font-weight: 700;
    color: #374151;
    font-size: 1.12rem;
    margin-bottom: 0.35rem;
    letter-spacing: 0.5px;
}

.subject-avg-item .subject-score {
    font-size: 1.65rem;
    font-weight: 800;
    letter-spacing: 1px;
    margin-bottom: 0.1rem;
    transition: color 0.3s;
    line-height: 1.1;
    display: inline-block;
    border-radius: 8px;
    padding: 0.1em 0.6em;
    background: rgba(255,255,255,0.7);
    box-shadow: 0 1px 4px rgba(80,120,255,0.04);
}

.subject-score.low { color: #ef4444; background: rgba(239,68,68,0.08);}
.subject-score.mid { color: #f59e42; background: rgba(245,158,66,0.08);}
.subject-score.high { color: #22c55e; background: rgba(34,197,94,0.08);}
.subject-score.top { color: #2563eb; background: rgba(37,99,235,0.08);}

.subject-avg-item .score-label {
    font-size: 0.92rem;
    color: #6b7280;
    margin-top: 0.2rem;
    display: block;
    letter-spacing: 0.2px;
}

/* ========== 21. 响应式设计 ========== */
/* 平板设备 (991.98px 及以下) */
@media (max-width: 991.98px) {
    /* 欢迎区域 */
    .welcome-section {
        padding: 2rem 1rem;
    }

    .welcome-section h1 {
        font-size: 2rem;
    }

    /* 卡片和面板 */
    .card-body {
        padding: 1rem;
    }

    /* 按钮 */
    .btn {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }

    /* 成绩分析 */
    .grade-analysis-card .row {
        margin: 0;
    }

    .grade-analysis-card .col-md-3,
    .grade-analysis-card .col-md-6 {
        padding: 0.25rem;
    }

    .trend-item,
    .subject-trend-item {
        margin-bottom: 0.5rem;
    }

    .overall-grade-card .card-body {
        padding: 1.2rem 0.7rem;
    }

    .overall-stat-item {
        padding: 0.8rem 0.7rem;
    }

    .subject-avg-item {
        padding: 0.8rem 0.3rem;
    }

    .subject-avg-list {
        gap: 0.7rem;
    }

    .subject-avg-item {
        padding: 0.8rem 0.5rem;
        min-width: 90px;
    }

    .subject-avg-item .subject-score {
        font-size: 1.2rem;
    }

    /* 考试选择区域 */
    .exam-select-section {
        padding: 1rem;
    }

    .grade-detail-card .card-body {
        padding: 1rem;
    }

    .grade-score-item {
        padding: 0.5rem 0.75rem;
    }

    .chart-card {
        margin-bottom: 1rem;
    }

    .chart-card img {
        max-width: 100%;
    }

    .overall-stat-item {
        padding: 1rem;
    }
}

/* 手机设备 (768px 及以下) */
@media (max-width: 768px) {
    /* 欢迎区域 */
    .welcome-section {
        padding: 1.5rem 1rem;
    }

    .welcome-section h1 {
        font-size: 1.8rem;
    }

    /* 请假原因显示 */
    #leavePanel .leave-reason-cell {
        max-width: 120px;
        min-width: 100px;
        padding: 6px 8px;
    }

    #leavePanel .leave-reason-text {
        max-height: 2.8em;
        font-size: 0.9em;
    }

    .leave-reason-modal .modal-dialog {
        margin: 1rem;
    }

    .leave-reason-modal .modal-body {
        padding: 1rem;
    }

    .leave-reason-modal .reason-content {
        padding: 1rem;
        font-size: 1em;
    }

    /* 模态框 */
    .modal-content {
        margin: 0.5rem;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    /* 表格 */
    .table {
        font-size: 0.9rem;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }

    /* 徽章 */
    .badge {
        font-size: 0.7rem;
        padding: 0.4rem 0.6rem;
    }

    /* 快速操作按钮 */
    #quickLeaveBtn,
    #quickMessageBtn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        width: 100%;
    }
}

/* ========== 预测信息样式 ========== */
.prediction-info-card {
    border: 1px solid #e3f2fd;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.prediction-info-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.prediction-info-card .card-header {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-bottom: 1px solid #e1e5e9;
    border-radius: 12px 12px 0 0;
    padding: 1rem 1.5rem;
}

.prediction-info-card .card-header h6 {
    color: #1976d2;
    font-weight: 600;
    margin: 0;
}

.prediction-info-card .card-body {
    padding: 1.5rem;
}

.prediction-item {
    margin-bottom: 1rem;
}

.prediction-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0.3rem;
}

.prediction-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.prediction-value.text-success {
    color: #28a745 !important;
}

.prediction-value.text-warning {
    color: #ffc107 !important;
}

.prediction-value.text-danger {
    color: #dc3545 !important;
}

.prediction-value i {
    font-size: 1rem;
}

/* 新的预测信息样式 */
.prediction-main-info {
    margin-bottom: 1rem;
}

.prediction-explanation {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.3rem;
    line-height: 1.4;
}

.prediction-percentage {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.2rem;
}

/* 预测因素样式 */
.prediction-factors {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 0.5rem;
}

.factor-item {
    display: flex;
    align-items: center;
    padding: 0.3rem 0;
    font-size: 0.9rem;
    color: #495057;
}

.factor-item i {
    color: #007bff;
    margin-right: 0.5rem;
    font-size: 0.8rem;
}

/* 免责声明样式 */
.prediction-disclaimer {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
}

.disclaimer-content {
    display: flex;
    align-items: flex-start;
    font-size: 0.9rem;
    color: #856404;
    line-height: 1.5;
}

.disclaimer-content i {
    color: #f39c12;
    margin-top: 0.1rem;
    flex-shrink: 0;
}

.disclaimer-content strong {
    color: #856404;
    margin-right: 0.3rem;
}

/* 预测因素列表样式（保留兼容性） */
.prediction-info-card ul {
    margin-bottom: 0;
}

.prediction-info-card li {
    padding: 0.3rem 0;
    border-bottom: 1px solid #f8f9fa;
    font-size: 0.9rem;
}

.prediction-info-card li:last-child {
    border-bottom: none;
}

.prediction-info-card li strong {
    color: #495057;
    font-weight: 600;
}