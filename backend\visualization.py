import logging
import os
from typing import Dict, List, Union, Optional
import re

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.patches import RegularPolygon, Circle


# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams["font.family"] = ["SimHei"]
plt.rcParams['axes.unicode_minus'] = False

# 设置matplotlib中文字体
plt.rcParams.update({
    'font.sans-serif': ['SimHei'],
    'axes.unicode_minus': False,
    'figure.dpi': 300,
    'savefig.dpi': 300
})


def safe_filename(name):
    if not name:
        return ''
    return re.sub(r'[<>:"/\\|?*]', '_', str(name))


def get_save_dir(base_dir, grade, exam_name, class_name=None, student_name=None, chart_type='student'):
    """
    获取图表保存目录，所有路径参数都做安全过滤
    """
    grade = safe_filename(grade)
    exam_name = safe_filename(exam_name)
    class_name = safe_filename(class_name) if class_name else None
    student_name = safe_filename(student_name) if student_name else None
    # 基础路径：data/可视化/年级/考试/
    path = os.path.join(base_dir, grade, exam_name)
    
    if chart_type == 'grade_avg':
        # 年级平均分图表保存在 年级/考试/年级平均分/ 目录下
        path = os.path.join(path, '年级平均分')
    elif class_name:
        # 班级相关图表
        path = os.path.join(path, class_name)
        
        if chart_type == 'class_avg':
            # 班级平均分图表保存在 年级/考试/班级/班级平均分/ 目录下
            path = os.path.join(path, '班级平均分')
        elif student_name and chart_type == 'student':
            # 学生个人图表保存在 年级/考试/班级/学生姓名/ 目录下
            # 避免路径过长，使用学号或姓名作为文件夹名
            path = os.path.join(path, student_name)
    
    # 确保目录存在
    os.makedirs(path, exist_ok=True)
    return path


def create_radar_chart(
        student_data: Dict[str, Union[str, float]],
        full_marks: Dict[str, float],
        grade: str,
        exam_name: str,
        class_name: str = None,
        student_id: str = None,
        student_name: str = None,
        subjects: List[str] = None,
        chart_type: str = 'student'
) -> str:
    """
    生成学生成绩雷达图，保存到 /data/可视化/年级/考试/班级/学生姓名/ 下
    """
    base_dir = 'data/可视化'
    save_dir = get_save_dir(base_dir, safe_filename(grade), safe_filename(exam_name), safe_filename(class_name), safe_filename(student_name), chart_type)
    # 确保保存目录存在
    if not os.path.exists(save_dir):
        os.makedirs(save_dir, exist_ok=True)

    fig, ax = plt.subplots(figsize=(10, 10))
    fig.patch.set_facecolor('#f8f9fa')
    ax.set_facecolor('#f8f9fa')
    ax.axis('off')

    # 绘制六边形背景
    hexagon = RegularPolygon(
        (0, 0), numVertices=6, radius=1.0, orientation=0,
        facecolor='#ffffff', edgecolor='#333333', linewidth=2, alpha=0.9
    )
    ax.add_patch(hexagon)

    # 设置角度
    angles = np.linspace(0, 2 * np.pi, 7)[:-1] + np.pi / 2

    # 绘制坐标轴
    for angle in angles:
        ax.plot([0, np.cos(angle)], [0, np.sin(angle)], 'k--', linewidth=0.9, alpha=0.6)

    # 绘制内部刻度线
    for r in [0.25, 0.5, 0.75, 0.9]:
        hexagon_inner = RegularPolygon(
            (0, 0), numVertices=6, radius=r, orientation=0,
            facecolor='none', edgecolor='#cccccc', linewidth=1, alpha=0.5
        )
        ax.add_patch(hexagon_inner)
        ax.text(r * 1.05, 0, f"{int(r * 100)}%", ha='center', va='center', fontsize=9, color='#666666')

    # 计算成绩百分比
    percentages = [float(student_data[subject]) / float(full_marks[subject]) for subject in subjects]

    # 计算雷达图顶点坐标
    x = [np.cos(angles[i]) * percentages[i] for i in range(6)]
    y = [np.sin(angles[i]) * percentages[i] for i in range(6)]

    # 绘制雷达图区域
    custom_cmap = LinearSegmentedColormap.from_list('custom_cmap', ['#e6f7ff', '#1890ff'])
    polygon = plt.Polygon(
        np.column_stack([x, y]), closed=True,
        facecolor=custom_cmap(0.6), alpha=0.4,
        edgecolor='#1890ff', linewidth=2
    )
    ax.add_patch(polygon)

    # 添加数据点标记
    for i in range(6):
        ax.add_patch(Circle((x[i], y[i]), radius=0.03, color='#1890ff', alpha=1.0))

    # 添加科目名称标签
    for i, subject in enumerate(subjects):
        angle = angles[i]
        ax.text(
            np.cos(angle) * 1.25, np.sin(angle) * 1.25, subject,
            ha='center', va='center', fontsize=12, fontweight='bold',
            bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.3')
        )

    # 添加成绩标签
    for i, subject in enumerate(subjects):
        angle = angles[i]
        score = float(student_data[subject])
        full_score = float(full_marks[subject])
        percentage = float(percentages[i])

        position_factor = 1.15 if percentage > 0.9 else 1.1 if percentage > 0.7 else 1.05

        ax.text(
            np.cos(angle) * percentage * position_factor,
            np.sin(angle) * percentage * position_factor,
            f"{score}/{full_score}",
            ha='center', va='center', fontsize=10,
            fontweight='bold', color='navy',
            bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.2')
        )

    # 添加学生信息
    ax.text(0, 0.05, "成绩", ha='center', va='center', fontsize=10, color='#666666')
    
    if chart_type == 'class_avg':
        # 班级平均分图表：学号显示年级，姓名显示班级名
        ax.text(0, 1.45, f"年级：{student_data.get('学号', grade)}", ha='center', va='center', fontsize=14, fontweight='bold')
        ax.text(0, -1.45, f"班级：{student_data.get('姓名', class_name)}", ha='center', va='center', fontsize=14, fontweight='bold')
    elif chart_type == 'grade_avg':
        # 年级平均分图表：学号显示年级，姓名显示年级
        ax.text(0, 1.45, f"年级：{student_data.get('学号', grade)}", ha='center', va='center', fontsize=14, fontweight='bold')
        ax.text(0, -1.45, f"年级：{student_data.get('姓名', grade)}", ha='center', va='center', fontsize=14, fontweight='bold')
    else:
        # 学生个人图表：学号显示学号，姓名显示姓名
        ax.text(0, 1.45, f"学号：{student_id or class_name}", ha='center', va='center', fontsize=14, fontweight='bold')
        ax.text(0, -1.45, f"姓名：{student_name or class_name}", ha='center', va='center', fontsize=14, fontweight='bold')
    
    ax.text(0, -1.6, f"总分：{float(student_data['总分'])}", ha='center', va='center', fontsize=14, fontweight='bold',
            color='darkred')

    # 设置标题和布局
    if chart_type == 'class_avg':
        ax.set_title('班级平均分六边形分析图', pad=30, fontsize=18, fontweight='bold')
    elif chart_type == 'grade_avg':
        ax.set_title('年级平均分六边形分析图', pad=30, fontsize=18, fontweight='bold')
    else:
        ax.set_title('学生成绩六边形分析图', pad=30, fontsize=18, fontweight='bold')
    ax.set_xlim(-1.5, 1.5)
    ax.set_ylim(-1.5, 1.5)
    plt.tight_layout()

    # 保存雷达图 - 优化文件名生成逻辑
    if chart_type == 'student' and student_id:
        filename_radar = f"radar_{safe_filename(student_id)}.png"
    elif chart_type == 'class_avg':
        filename_radar = f"radar_{safe_filename(class_name)}.png"
    elif chart_type == 'grade_avg':
        filename_radar = f"radar_{safe_filename(grade)}年级.png"
    else:
        filename_radar = f"radar_{safe_filename(student_id) or safe_filename(class_name) or 'unknown'}.png"
    
    filepath_radar = os.path.join(save_dir, filename_radar)
    # 新增：如果文件已存在，直接返回
    if os.path.exists(filepath_radar):
        rel_path = os.path.relpath(filepath_radar, start=base_dir)
        return f"data/可视化/{rel_path.replace(os.sep, '/')}"
    plt.savefig(filepath_radar, dpi=200, bbox_inches='tight')
    plt.close(fig)
    rel_path = os.path.relpath(filepath_radar, start=base_dir)
    return f"data/可视化/{rel_path.replace(os.sep, '/')}"


def create_table_chart(
        student_data: Dict[str, Union[str, float]],
        grade: str,
        exam_name: str,
        class_name: str,
        student_id: str,
        student_name: str,
        subjects: List[str],
        chart_type: str = 'student'
) -> str:
    """
    生成学生成绩表格图

    Args:
        student_data: 学生成绩数据字典
        grade: 年级
        exam_name: 考试名称
        class_name: 班级名称
        student_id: 学号
        student_name: 姓名
        subjects: 科目列表
        chart_type: 图表类型 ('student', 'class_avg', 'grade_avg')

    Returns:
        str: 保存的图片文件路径
    """
    base_dir = 'data/可视化'
    save_dir = get_save_dir(base_dir, safe_filename(grade), safe_filename(exam_name), safe_filename(class_name), safe_filename(student_name), chart_type)
        
    try:
        fig_table, ax_table = plt.subplots(figsize=(10, 4))
        fig_table.patch.set_facecolor('#f8f9fa')
        ax_table.set_facecolor('#f8f9fa')
        ax_table.axis('off')

        # 准备表格数据
        table_data = [
            ['学号', '姓名'] + subjects + ['总分'],
            [student_id, student_name] + [str(float(student_data[subject])) for subject in subjects] + [str(float(student_data['总分']))]
        ]

        # 创建表格
        table = ax_table.table(
            cellText=table_data,
            loc='center',
            cellLoc='center',
            colWidths=[0.1] * (len(subjects) + 3)
        )
        table.auto_set_font_size(False)
        table.set_fontsize(12)
        table.scale(1, 1.5)

        # 设置标题
        ax_table.set_title('学生成绩表格', pad=20, fontsize=16, fontweight='bold')
        plt.tight_layout()

        # 保存图片 - 优化文件名生成逻辑
        if chart_type == 'student' and student_id:
            filename = f"table_{safe_filename(student_id)}.png"
        elif chart_type == 'class_avg':
            filename = f"table_{safe_filename(class_name)}.png"
        elif chart_type == 'grade_avg':
            filename = f"table_{safe_filename(grade)}年级.png"
        else:
            filename = f"table_{safe_filename(student_id) or safe_filename(class_name) or 'unknown'}.png"
        
        filepath = os.path.join(save_dir, filename)
        # 新增：如果文件已存在，直接返回
        if os.path.exists(filepath):
            rel_path = os.path.relpath(filepath, start=base_dir)
            return f"data/可视化/{rel_path.replace(os.sep, '/')}"
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close(fig_table)

        logger.debug(f"Table chart saved: {filepath}")
        rel_path = os.path.relpath(filepath, start=base_dir)
        return f"data/可视化/{rel_path.replace(os.sep, '/')}"
    except Exception as e:
        logger.error(f"Error creating table chart: {str(e)}")
        raise


def create_bar_chart(
        student_data: Dict[str, Union[str, float]],
        class_averages: Dict[str, float],
        grade: str,
        exam_name: str,
        class_name: str,
        student_id: str,
        student_name: str,
        subjects: List[str],
        chart_type: str = 'student',
        full_marks: Dict[str, float] = None
) -> str:
    """
    生成学生成绩柱状图

    Args:
        student_data: 学生成绩数据字典
        class_averages: 各科目班级平均分数据字典
        grade: 年级
        exam_name: 考试名称
        class_name: 班级名称
        student_id: 学号
        student_name: 姓名
        subjects: 科目列表
        chart_type: 图表类型 ('student', 'class_avg', 'grade_avg')
        full_marks: 各科目满分数据字典

    Returns:
        str: 保存的图片文件路径
    """
    base_dir = 'data/可视化'
    save_dir = get_save_dir(base_dir, safe_filename(grade), safe_filename(exam_name), safe_filename(class_name), safe_filename(student_name), chart_type)
        
    try:
        fig, ax = plt.subplots(figsize=(12, 6))
        fig.patch.set_facecolor('#f8f9fa')
        ax.set_facecolor('#f8f9fa')

        # 准备数据
        scores = [float(student_data[subject]) for subject in subjects]
        avg_scores = [float(class_averages[subject]) for subject in subjects]

        # 创建柱状图
        x = np.arange(len(subjects))
        width = 0.35

        # 绘制实际分数柱状图
        bars1 = ax.bar(x - width/2, scores, width, label='实际分数', color='#1890ff')
        # 绘制班级平均分柱状图
        bars2 = ax.bar(x + width/2, avg_scores, width, label='班级平均分', color='#e6f7ff', alpha=0.5)

        # 设置y轴最大值为满分（如果有full_marks）
        if full_marks is not None:
            max_full = max([full_marks.get(subj, 100) for subj in subjects])
            ax.set_ylim(0, max_full * 1.05)  # 适当加一点空间
            
            # 为每一科添加满分位置的水平线和注释
            for i, subject in enumerate(subjects):
                full_mark = full_marks.get(subject, 100)
                # 绘制满分水平线
                ax.axhline(y=full_mark, xmin=(i-0.4)/len(subjects), xmax=(i+0.4)/len(subjects), 
                          color='red', linestyle='--', alpha=0.7, linewidth=1.5)
                # 添加满分注释
                ax.text(i, full_mark + max_full * 0.02, f'{full_mark}', 
                       ha='center', va='bottom', fontsize=10, fontweight='bold', 
                       color='red', bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))

        # 添加数值标签
        def autolabel(bars):
            for bar in bars:
                height = bar.get_height()
                ax.annotate(f'{height:.1f}',
                           xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3),  # 3 points vertical offset
                           textcoords="offset points",
                           ha='center', va='bottom')

        autolabel(bars1)
        autolabel(bars2)

        # 设置图表属性
        if chart_type == 'class_avg':
            ax.set_title(f'{class_name} 班级平均分分析', pad=20, fontsize=16, fontweight='bold')
        elif chart_type == 'grade_avg':
            ax.set_title(f'{grade}年级 年级平均分分析', pad=20, fontsize=16, fontweight='bold')
        else:
            ax.set_title(f'{student_name} 成绩分析', pad=20, fontsize=16, fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(subjects)
        ax.set_ylabel('分数')
        ax.legend()

        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.3)

        # 保存图片 - 优化文件名生成逻辑
        if chart_type == 'student' and student_id:
            filename = f"bar_{safe_filename(student_id)}.png"
        elif chart_type == 'class_avg':
            filename = f"bar_{safe_filename(class_name)}.png"
        elif chart_type == 'grade_avg':
            filename = f"bar_{safe_filename(grade)}年级.png"
        else:
            filename = f"bar_{safe_filename(student_id) or safe_filename(class_name) or 'unknown'}.png"
        
        filepath = os.path.join(save_dir, filename)
        # 新增：如果文件已存在，直接返回
        if os.path.exists(filepath):
            rel_path = os.path.relpath(filepath, start=base_dir)
            return f"data/可视化/{rel_path.replace(os.sep, '/')}"
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close(fig)

        logger.debug(f"Bar chart saved: {filepath}")
        rel_path = os.path.relpath(filepath, start=base_dir)
        return f"data/可视化/{rel_path.replace(os.sep, '/')}"
    except Exception as e:
        logger.error(f"Error creating bar chart: {str(e)}")
        raise


def create_class_table_chart(
        class_data: List[Dict[str, Union[str, float]]],
        grade: str,
        exam_name: str,
        class_name: str,
        subjects: List[str],
        chart_type: str = 'class_avg'
) -> str:
    """
    生成班级成绩表格图

    Args:
        class_data: 班级学生成绩数据列表，每个元素为一个学生的成绩字典
        grade: 年级
        exam_name: 考试名称
        class_name: 班级名称
        subjects: 科目列表
        chart_type: 图表类型 ('class_avg', 'grade_avg')

    Returns:
        str: 保存的图片文件路径
    """
    base_dir = 'data/可视化'
    save_dir = get_save_dir(base_dir, safe_filename(grade), safe_filename(exam_name), safe_filename(class_name), None, chart_type)
        
    fig_table, ax_table = plt.subplots(figsize=(12, 8))
    fig_table.patch.set_facecolor('#f8f9fa')
    ax_table.set_facecolor('#f8f9fa')
    ax_table.axis('off')

    # 准备表格数据
    table_data = [['学号', '姓名'] + subjects + ['总分']]
    for student in class_data:
        table_data.append(
            [student['学号'], student['姓名']] + [str(float(student[subject])) for subject in subjects] + [
                str(float(student['总分']))]
        )

    # 创建表格
    table = ax_table.table(
        cellText=table_data,
        loc='center',
        cellLoc='center',
        colWidths=[0.1] * (len(subjects) + 3)
    )
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 1.5)

    # 设置标题
    if chart_type == 'class_avg':
        ax_table.set_title(f'{class_name} 班级平均分表格', pad=20, fontsize=16, fontweight='bold')
    elif chart_type == 'grade_avg':
        ax_table.set_title(f'{grade}年级 年级平均分表格', pad=20, fontsize=16, fontweight='bold')
    else:
        ax_table.set_title(f'{class_name} 班级成绩表格', pad=20, fontsize=16, fontweight='bold')
    plt.tight_layout()

    # 保存图片 - 优化文件名生成逻辑
    if chart_type == 'grade_avg':
        filename_table = f"class_table_{safe_filename(grade)}年级.png"
    else:
        filename_table = f"class_table_{safe_filename(class_name)}.png"
    filepath_table = os.path.join(save_dir, filename_table)
    # 新增：如果文件已存在，直接返回
    if os.path.exists(filepath_table):
        rel_path = os.path.relpath(filepath_table, start=base_dir)
        return f"data/可视化/{rel_path.replace(os.sep, '/')}"
    plt.savefig(filepath_table, dpi=200, bbox_inches='tight')
    plt.close(fig_table)

    rel_path = os.path.relpath(filepath_table, start=base_dir)
    return f"data/可视化/{rel_path.replace(os.sep, '/')}"


def create_grade_average_table_chart(
        avg_data: Dict[str, float],
        grade: str,
        exam_name: str,
        subjects: List[str]
) -> str:
    base_dir = 'data/可视化'
    save_dir = get_save_dir(base_dir, safe_filename(grade), safe_filename(exam_name), None, None, 'grade_avg')
    fig_table, ax_table = plt.subplots(figsize=(10, 2))
    fig_table.patch.set_facecolor('#f8f9fa')
    ax_table.set_facecolor('#f8f9fa')
    ax_table.axis('off')

    # 表头和数据
    table_data = [['学号', '姓名'] + subjects + ['总分']]
    table_data.append(
        ['-', '年级平均分'] + [str(float(avg_data[subject])) for subject in subjects] + [str(float(avg_data['总分']))]
    )

    table = ax_table.table(
        cellText=table_data,
        loc='center',
        cellLoc='center',
        colWidths=[0.1] * (len(subjects) + 3)
    )
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1, 1.5)

    ax_table.set_title(f'{grade}年级 {exam_name} 年级平均分表格', pad=20, fontsize=16, fontweight='bold')
    plt.tight_layout()

    filename = f"class_table_{safe_filename(grade)}年级.png"
    filepath = os.path.join(save_dir, filename)
    # 新增：如果文件已存在，直接返回
    if os.path.exists(filepath):
        rel_path = os.path.relpath(filepath, start=base_dir)
        return f"data/可视化/{rel_path.replace(os.sep, '/')}"
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close(fig_table)

    rel_path = os.path.relpath(filepath, start=base_dir)
    return f"data/可视化/{rel_path.replace(os.sep, '/')}"


def calc_grade_exam_average(con, cursor, grade, exam):
    """
    计算年级某次考试的各科平均分和总分平均分
    """
    cursor.execute("SHOW TABLES")
    tables = [row[0] for row in cursor.fetchall()]
    class_tables = [t for t in tables if t not in ('class', 'exam', 'students', 'teachers', 'grade_courses')]
    subjects = ['语文', '数学', '英语', '物理', '化学', '生物']
    class_count = 0
    avg_data = {subj: 0 for subj in subjects}
    total_scores = []
    for class_name in class_tables:
        cursor.execute(f"SELECT * FROM `{exam}`")
        results = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]
        df = pd.DataFrame(results, columns=columns)
        for idx, subj in enumerate(subjects):
            avg_data[subj] += float(df[subj].astype(float).mean())
        total_scores.extend([float(score) for score in df['总分']])
        class_count += 1
    if class_count > 0:
        for subj in subjects:
            avg_data[subj] = round(float(avg_data[subj]) / class_count, 1)
    avg_data['总分'] = round(float(np.mean(total_scores)), 1) if total_scores else 0
    return avg_data, class_count


def create_grade_trend_chart(
        trend_data: List[Dict],
        grade: str,
        student_name: str,
        subjects: List[str]
) -> str:
    """
    生成学生成绩趋势图表
    
    Args:
        trend_data: 成绩趋势数据列表
        grade: 年级
        student_name: 学生姓名
        subjects: 科目列表
        
    Returns:
        str: 保存的图片文件路径
    """
    base_dir = 'data/可视化'
    save_dir = get_save_dir(base_dir, safe_filename(grade), '趋势分析', None, safe_filename(student_name), 'trend')
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle(f'{student_name} 成绩趋势分析', fontsize=16, fontweight='bold')
    
    # 准备数据
    exam_names = [d['exam_name'] for d in trend_data]
    total_scores = [float(d['total_score']) for d in trend_data]
    ranks = [int(d['rank']) for d in trend_data if d['rank'] is not None]
    
    # 1. 总分趋势图
    ax1 = axes[0, 0]
    ax1.plot(exam_names, total_scores, 'o-', linewidth=2, markersize=8, color='#2196F3')
    ax1.fill_between(exam_names, total_scores, alpha=0.3, color='#2196F3')
    ax1.set_title('总分趋势', fontweight='bold')
    ax1.set_xlabel('考试')
    ax1.set_ylabel('总分')
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)
    
    # 添加数据标签
    for i, score in enumerate(total_scores):
        ax1.annotate(f'{score}', (i, score), textcoords="offset points", xytext=(0,10), ha='center')
    
    # 2. 排名趋势图
    ax2 = axes[0, 1]
    if ranks:
        ax2.plot(exam_names[:len(ranks)], ranks, 'o-', linewidth=2, markersize=8, color='#FF5722')
        ax2.fill_between(exam_names[:len(ranks)], ranks, alpha=0.3, color='#FF5722')
        ax2.set_title('排名趋势', fontweight='bold')
        ax2.set_xlabel('考试')
        ax2.set_ylabel('排名')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        ax2.invert_yaxis()  # 排名越小越好，所以反转Y轴
        
        # 添加数据标签
        for i, rank in enumerate(ranks):
            ax2.annotate(f'{rank}', (i, rank), textcoords="offset points", xytext=(0,10), ha='center')
    
    # 3. 各科目成绩对比图
    ax3 = axes[1, 0]
    x = np.arange(len(exam_names))
    width = 0.15
    
    for i, subject in enumerate(subjects[:4]):  # 只显示前4个科目
        subject_scores = [float(d['subjects'][subject]) for d in trend_data]
        ax3.bar(x + i * width, subject_scores, width, label=subject, alpha=0.8)
    
    ax3.set_title('各科目成绩对比', fontweight='bold')
    ax3.set_xlabel('考试')
    ax3.set_ylabel('分数')
    ax3.set_xticks(x + width * 1.5)
    ax3.set_xticklabels(exam_names, rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 成绩分布雷达图
    ax4 = axes[1, 1]
    ax4.remove()  # 移除默认的直角坐标系
    ax4 = fig.add_subplot(2, 2, 4, projection='polar')
    
    # 计算各科目平均分
    subject_avg_scores = []
    for subject in subjects:
        avg_score = np.mean([float(d['subjects'][subject]) for d in trend_data])
        subject_avg_scores.append(avg_score)
    
    # 绘制雷达图
    angles = np.linspace(0, 2 * np.pi, len(subjects), endpoint=False).tolist()
    subject_avg_scores += subject_avg_scores[:1]  # 闭合图形
    angles += angles[:1]
    
    ax4.plot(angles, subject_avg_scores, 'o-', linewidth=2, color='#4CAF50')
    ax4.fill(angles, subject_avg_scores, alpha=0.25, color='#4CAF50')
    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(subjects)
    ax4.set_title('各科目平均分分布', fontweight='bold', pad=20)
    ax4.grid(True)
    
    plt.tight_layout()
    
    # 保存图片
    filename = f"trend_{safe_filename(student_name)}.png"
    filepath = os.path.join(save_dir, filename)
    
    if os.path.exists(filepath):
        rel_path = os.path.relpath(filepath, start=base_dir)
        return f"data/可视化/{rel_path.replace(os.sep, '/')}"
    
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close(fig)
    
    logger.debug(f"Grade trend chart saved: {filepath}")
    rel_path = os.path.relpath(filepath, start=base_dir)
    return f"data/可视化/{rel_path.replace(os.sep, '/')}"


def create_subject_comparison_chart(
        student_data: Dict[str, float],
        class_averages: Dict[str, float],
        subjects: List[str],
        grade: str,
        exam_name: str,
        student_name: str
) -> str:
    """
    生成学生与班级平均分对比图表
    
    Args:
        student_data: 学生成绩数据
        class_averages: 班级平均分数据
        subjects: 科目列表
        grade: 年级
        exam_name: 考试名称
        student_name: 学生姓名
        
    Returns:
        str: 保存的图片文件路径
    """
    base_dir = 'data/可视化'
    save_dir = get_save_dir(base_dir, safe_filename(grade), safe_filename(exam_name), None, safe_filename(student_name), 'comparison')
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle(f'{student_name} vs 班级平均分对比', fontsize=16, fontweight='bold')
    
    # 准备数据
    student_scores = [float(student_data[subject]) for subject in subjects]
    class_scores = [float(class_averages[subject]) for subject in subjects]
    
    # 1. 柱状图对比
    x = np.arange(len(subjects))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, student_scores, width, label='我的成绩', color='#2196F3', alpha=0.8)
    bars2 = ax1.bar(x + width/2, class_scores, width, label='班级平均', color='#FF9800', alpha=0.8)
    
    ax1.set_title('各科目成绩对比', fontweight='bold')
    ax1.set_xlabel('科目')
    ax1.set_ylabel('分数')
    ax1.set_xticks(x)
    ax1.set_xticklabels(subjects)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax1.annotate(f'{height}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom')
    
    for bar in bars2:
        height = bar.get_height()
        ax1.annotate(f'{height}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom')
    
    # 2. 差异分析图
    differences = [float(student_scores[i]) - float(class_scores[i]) for i in range(len(subjects))]
    colors = ['green' if diff >= 0 else 'red' for diff in differences]
    
    bars3 = ax2.bar(subjects, differences, color=colors, alpha=0.7)
    ax2.set_title('与班级平均分差距', fontweight='bold')
    ax2.set_xlabel('科目')
    ax2.set_ylabel('差距分数')
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar in bars3:
        height = bar.get_height()
        ax2.annotate(f'{height:+.1f}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3 if height >= 0 else -15),
                    textcoords="offset points",
                    ha='center', va='bottom' if height >= 0 else 'top')
    
    plt.tight_layout()
    
    # 保存图片
    filename = f"comparison_{safe_filename(student_name)}.png"
    filepath = os.path.join(save_dir, filename)
    
    if os.path.exists(filepath):
        rel_path = os.path.relpath(filepath, start=base_dir)
        return f"data/可视化/{rel_path.replace(os.sep, '/')}"
    
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close(fig)
    
    logger.debug(f"Subject comparison chart saved: {filepath}")
    rel_path = os.path.relpath(filepath, start=base_dir)
    return f"data/可视化/{rel_path.replace(os.sep, '/')}"
