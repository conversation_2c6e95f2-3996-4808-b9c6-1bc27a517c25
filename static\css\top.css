/* =====================
   顶部和导航条样式（top.css）
   ===================== */

/* ========== 1. 顶部导航栏基础 ========== */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, #0a58ca 100%) !important;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
    text-decoration: none;
    color: white !important;
}
.navbar-brand i {
    font-size: 1.5rem;
}
.navbar-brand:hover {
    color: rgba(255,255,255,0.9) !important;
}

/* ========== 2. 导航链接 ========== */
.nav-link {
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    border-radius: 0.25rem;
    margin: 0 0.25rem;
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
}
.nav-link:hover {
    background-color: rgba(255,255,255,.1);
    color: white !important;
}
.nav-link.active {
    color: white !important;
}

.navbar-nav .nav-link i {
    margin-right: 0.25rem;
}

/* ========== 3. 导航栏折叠按钮 ========== */
.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}
.navbar-toggler:focus {
    box-shadow: none;
}
.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* ========== 4. 导航栏容器与布局 ========== */
.navbar .container {
    max-width: 1200px;
}
.navbar-collapse {
    justify-content: flex-end;
}

/* ========== 5. 固定定位 ========== */
.navbar.sticky-top {
    z-index: 1020;
}

/* ========== 6. 响应式设计 ========== */
@media (max-width: 767.98px) {
    .navbar-brand span {
        display: none;
    }
    .navbar-nav {
        margin-top: 0.5rem;
    }
    .nav-link {
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
    }
}
