/**
 * ========================================
 * 公共工具函数库 (public.js)
 * ========================================
 * 包含所有页面通用的工具函数，避免代码重复
 * 主要功能模块：
 * 1. 消息提示和错误处理
 * 2. API 请求和响应处理
 * 3. 图片和文件处理
 * 4. UI 组件初始化
 * 5. 数据格式化和验证
 * ========================================
 */

// ========================================
// 1. 消息提示和错误处理模块
// ========================================

/**
 * 显示错误信息到错误容器
 * @param {string} message - 错误消息
 * @param {number} duration - 显示时长（毫秒）
 */
function showError(message, duration = 5000) {
    const errorContainer = document.getElementById('error-container');
    if (!errorContainer) {
        console.error('找不到错误容器元素');
        // 如果没有错误容器，使用 SweetAlert 显示
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: '错误',
                text: message,
                icon: 'error',
                timer: duration,
                showConfirmButton: false
            });
        }
        return;
    }
    errorContainer.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <div>
                <strong>错误：</strong> ${message}
            </div>
        </div>
    `;
    errorContainer.classList.remove('d-none');
    if (errorContainer.timeoutId) {
        clearTimeout(errorContainer.timeoutId);
    }
    errorContainer.timeoutId = setTimeout(() => {
        errorContainer.classList.add('d-none');
    }, duration);
}

// ========================================
// 3. 图片和文件处理模块
// ========================================

/**
 * 创建图表卡片元素
 * @param {string} chartUrl - 图表图片 URL
 * @param {string} title - 图表标题
 * @param {string} type - 图表类型（暂未使用）
 * @param {Object} options - 选项配置
 * @returns {HTMLElement} 图表卡片元素
 */
function createChartCard(chartUrl, title, type, options = {}) {
    // options: { examName, className, studentName, chartType, useServerDownload }
    const card = document.createElement('div');
    card.className = 'col-md-6 col-lg-4';
    if (typeof chartUrl !== 'string' || !chartUrl) {
        card.innerHTML = `
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">${title}</h6>
                </div>
                <div class="card-body text-center text-muted">
                    <i class="bi bi-exclamation-triangle fs-2"></i>
                    <div>暂无图表</div>
                </div>
            </div>
        `;
        return card;
    }
    let imageUrl = chartUrl.startsWith('/images/') ? chartUrl : `/images/${chartUrl}`;
    // 加入时间戳参数，强制刷新缓存
    const cacheBuster = `t=${Date.now()}`;
    const imageUrlWithTs = imageUrl.includes('?') ? `${imageUrl}&${cacheBuster}` : `${imageUrl}?${cacheBuster}`;
    card.innerHTML = `
        <div class="card h-100 shadow-sm">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">${title}</h6>
            </div>
            <div class="card-body">
                <img src="${imageUrlWithTs}" class="img-fluid chart-image" alt="${title}"
                     style="cursor: pointer;" onclick="showEnlargedImage('${imageUrlWithTs}', '${title}')"
                     onerror="this.onerror=null; console.error('Failed to load image:', this.src);">
            </div>
            <div class="card-footer bg-light text-center">
                ${options.useServerDownload ? `
                <button class="btn btn-outline-primary btn-sm" onclick="downloadImageToServer('${imageUrl}', '${options.examName}', '${options.className}', '${options.studentName}', '${options.chartType}')">
                    <i class="bi bi-download"></i> 下载
                </button>
                ` : `
                <button class="btn btn-sm btn-outline-primary" onclick="downloadImage('${imageUrl}', '${title}')">
                    <i class="bi bi-download"></i> 下载
                </button>
                `}
            </div>
        </div>
    `;
    return card;
}

/**
 * 显示放大的图片模态框
 * @param {string} imageUrl - 图片 URL
 * @param {string} title - 图片标题
 */
function showEnlargedImage(imageUrl, title) {
    // 检查是否在管理员页面（有年级和考试选择器）
    const gradeBtn = document.querySelector('#gradeBtnGroup .btn.active');
    const examSelect = document.getElementById('examSelect');
    
    if (gradeBtn && examSelect) {
        // 管理员页面 - 使用带下载功能的版本
        const grade = gradeBtn.getAttribute('data-grade');
        const exam = examSelect.value;
        const imgPath = imageUrl.split('/images/')[1];
        
        Swal.fire({
            title: title || '图片',
            imageUrl: imageUrl,
            imageAlt: title || '图片',
            width: 900,
            showConfirmButton: false,
            showCloseButton: true,
            showDenyButton: true,
            denyButtonText: '下载',
            denyButtonColor: '#3085d6',
            customClass: {
                popup: 'swal2-image-popup',
                image: 'swal2-image'
            }
        }).then((result) => {
            if (result.isDenied) {
                // 下载功能
                if (!grade || !exam) {
                    Swal.fire('错误', '请先选择年级和考试', 'error');
                    return;
                }
                
                if (!imgPath) {
                    Swal.fire('错误', '图片路径无效', 'error');
                    return;
                }
                
                // 调用下载API
                fetch('/download_image', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        image_path: imgPath,
                        grade: grade,
                        exam: exam,
                        class_name: '年级平均分',
                        student_name: ''
                    })
                })
                .then(res => res.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('成功', `图片已下载至：${data.filepath}`, 'success');
                    } else {
                        Swal.fire('错误', data.error || '下载失败', 'error');
                    }
                })
                .catch(err => {
                    console.error('下载失败:', err);
                    Swal.fire('错误', '网络错误，下载失败', 'error');
                });
            }
        });
    } else {
        // 其他页面 - 使用简单版本
        Swal.fire({
            title: title,
            imageUrl: imageUrl,
            imageAlt: title,
            width: 900,
            customClass: {
                popup: 'swal2-image-popup',
                image: 'swal2-image'
            }
        });
    }
}

// ========================================
// 2. API 请求和响应处理模块
// ========================================

/**
 * 统一处理 API 响应
 * @param {Response} response - fetch 响应对象
 * @returns {Promise<Object>} 解析后的数据
 * @throws {Error} 当响应不成功时抛出错误
 */
async function handleApiResponse(response) {
    try {
        const data = await response.json();
        if (!response.ok) {
            throw new Error(data.message || data.error || `服务器错误 (${response.status})`);
        }
        return data;
    } catch (error) {
        if (error instanceof SyntaxError) {
            throw new Error('服务器响应格式错误');
        }
        throw error;
    }
}

/**
 * 统一的 API 请求函数
 * @param {string} url - 请求 URL
 * @param {Object} options - 请求选项
 * @param {boolean} showLoading - 是否显示加载提示
 * @returns {Promise<Object>} API 响应数据
 */
async function apiRequest(url, options = {}, showLoading = true) {
    if (showLoading) {
        showLoadingToast();
    }

    try {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        const response = await fetch(url, { ...defaultOptions, ...options });
        const data = await handleApiResponse(response);

        if (showLoading) {
            hideLoadingToast();
        }

        return data;
    } catch (error) {
        if (showLoading) {
            hideLoadingToast();
        }

        // 只有在显示加载提示时才显示错误提示，避免重复处理
        if (showLoading) {
            const errorMessage = error.message || '网络请求失败';
            showErrorToast(errorMessage);
        }
        throw error;
    }
}

/**
 * GET 请求的便捷方法
 * @param {string} url - 请求 URL
 * @param {boolean} showLoading - 是否显示加载提示
 * @returns {Promise<Object>} API 响应数据
 */
async function apiGet(url, showLoading = true) {
    return apiRequest(url, { method: 'GET' }, showLoading);
}

/**
 * POST 请求的便捷方法
 * @param {string} url - 请求 URL
 * @param {Object} data - 请求数据
 * @param {boolean} showLoading - 是否显示加载提示
 * @returns {Promise<Object>} API 响应数据
 */
async function apiPost(url, data, showLoading = true) {
    return apiRequest(url, {
        method: 'POST',
        body: JSON.stringify(data)
    }, showLoading);
}

/**
 * PUT 请求的便捷方法
 * @param {string} url - 请求 URL
 * @param {Object} data - 请求数据
 * @param {boolean} showLoading - 是否显示加载提示
 * @returns {Promise<Object>} API 响应数据
 */
async function apiPut(url, data, showLoading = true) {
    return apiRequest(url, {
        method: 'PUT',
        body: JSON.stringify(data)
    }, showLoading);
}

/**
 * DELETE 请求的便捷方法
 * @param {string} url - 请求 URL
 * @param {boolean} showLoading - 是否显示加载提示
 * @returns {Promise<Object>} API 响应数据
 */
async function apiDelete(url, showLoading = true) {
    return apiRequest(url, { method: 'DELETE' }, showLoading);
}

/**
 * 显示加载提示 Toast
 * 优先使用页面中的 loadingToast 元素，如果不存在则创建临时的
 */
function showLoadingToast() {
    const loadingToastEl = document.getElementById('loadingToast');
    if (loadingToastEl) {
        const loadingToast = bootstrap.Toast.getOrCreateInstance(loadingToastEl);
        loadingToast.show();
    } else {
        // 如果页面没有预定义的 loading toast，创建一个临时的
        showToast('正在加载...', 'info', 0, 'bi-spinner bi-spin');
    }
}

/**
 * 隐藏加载提示 Toast
 */
function hideLoadingToast() {
    const loadingToastEl = document.getElementById('loadingToast');
    if (loadingToastEl) {
        const loadingToast = bootstrap.Toast.getInstance(loadingToastEl);
        if (loadingToast) {
            loadingToast.hide();
        }
    }
}

/**
 * 统一的 Toast 提示函数
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型：success, error, warning, info
 * @param {number} delay - 自动隐藏延迟（毫秒），0 表示不自动隐藏
 * @param {string} icon - Bootstrap 图标类名
 */
function showToast(message, type = 'success', delay = 3000, icon = '') {
    // 类型对应的样式和默认图标
    const typeConfig = {
        success: { bgClass: 'bg-success', defaultIcon: 'bi-check-circle-fill' },
        error: { bgClass: 'bg-danger', defaultIcon: 'bi-exclamation-triangle-fill' },
        warning: { bgClass: 'bg-warning text-dark', defaultIcon: 'bi-exclamation-triangle-fill' },
        info: { bgClass: 'bg-info', defaultIcon: 'bi-info-circle-fill' }
    };

    const config = typeConfig[type] || typeConfig.success;
    const iconClass = icon || config.defaultIcon;

    const toastContainer = document.createElement('div');
    toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
    toastContainer.style.zIndex = '1056';

    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white ${config.bgClass} border-0 shadow-lg`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="bi ${iconClass} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close ${type === 'warning' ? '' : 'btn-close-white'} me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    document.body.appendChild(toastContainer);

    const toastOptions = delay > 0 ? { delay } : { autohide: false };
    const toastInstance = new bootstrap.Toast(toast, toastOptions);
    toastInstance.show();

    toast.addEventListener('hidden.bs.toast', () => {
        toastContainer.remove();
    });

    return toastInstance;
}

/**
 * 显示成功提示 Toast
 * @param {string} message - 成功消息
 * @param {number} delay - 自动隐藏延迟（毫秒）
 */
function showSuccessToast(message, delay = 3000) {
    return showToast(message, 'success', delay);
}

/**
 * 显示错误提示 Toast
 * @param {string} message - 错误消息
 * @param {number} delay - 自动隐藏延迟（毫秒）
 */
function showErrorToast(message, delay = 5000) {
    return showToast(message, 'error', delay);
}

/**
 * 显示警告提示 Toast
 * @param {string} message - 警告消息
 * @param {number} delay - 自动隐藏延迟（毫秒）
 */
function showWarningToast(message, delay = 4000) {
    return showToast(message, 'warning', delay);
}

/**
 * 显示信息提示 Toast
 * @param {string} message - 信息消息
 * @param {number} delay - 自动隐藏延迟（毫秒）
 */
function showInfoToast(message, delay = 3000) {
    return showToast(message, 'info', delay);
}

/**
 * 下载图片到服务器指定目录
 * @param {string} imageUrl - 图片 URL
 * @param {string} examName - 考试名称
 * @param {string} className - 班级名称
 * @param {string} studentName - 学生姓名
 * @param {string} chartType - 图表类型（暂未使用）
 */
function downloadImageToServer(imageUrl, examName, className, studentName, chartType) {
    // 从imageUrl中提取图片路径
    const imagePath = imageUrl.replace('/images/', '');
    const grade = sessionStorage.getItem('grade');
    
    fetch('/download_image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            image_path: imagePath,
            grade: grade,
            exam: examName,
            class_name: className,
            student_name: studentName || ''
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: '图片已下载',
                text: `图片已保存至：${data.filepath}`
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: '下载失败',
                text: data.error || '图片下载失败'
            });
        }
    })
    .catch(() => {
        Swal.fire({
            icon: 'error',
            title: '下载失败',
            text: '网络错误，图片下载失败'
        });
    });
}

/**
 * 从浏览器直接下载图片
 * @param {string} url - 图片 URL
 * @param {string} title - 文件名前缀
 */
function downloadImage(url, title) {
    console.log('Downloading image:', { url, title });
    const link = document.createElement('a');
    link.href = url;
    link.download = `${title}_${new Date().getTime()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// ========================================
// 4. UI 组件初始化模块
// ========================================

/**
 * 初始化所有工具提示
 */
function initTooltips() {
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltipTriggerList.forEach(tooltipTriggerEl => {
        new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 初始化所有弹出框
 */
function initPopovers() {
    const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]');
    popoverTriggerList.forEach(popoverTriggerEl => {
        new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * 统一的页面组件初始化
 */
function initPageComponents() {
    initTooltips();
    initPopovers();
}

// ========================================
// 5. 数据格式化和验证模块
// ========================================

/**
 * 格式化文本中的换行符为 HTML
 * @param {string} text - 原始文本
 * @returns {string} 格式化后的 HTML
 */
function formatTextToHtml(text) {
    if (!text) return '';
    return text.replace(/\n/g, '<br>');
}

/**
 * 格式化分数显示
 * @param {number|string} score - 分数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的分数
 */
function formatScore(score, decimals = 1) {
    if (score === undefined || score === null || score === '') {
        return '--';
    }
    const num = parseFloat(score);
    return isNaN(num) ? '--' : num.toFixed(decimals);
}

/**
 * 格式化日期时间
 * @param {string|Date} datetime - 日期时间
 * @param {string} format - 格式类型：'date', 'time', 'datetime'
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(datetime, format = 'datetime') {
    if (!datetime) return '--';

    const date = new Date(datetime);
    if (isNaN(date.getTime())) return '--';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    switch (format) {
        case 'date':
            return `${year}-${month}-${day}`;
        case 'time':
            return `${hours}:${minutes}`;
        case 'datetime':
        default:
            return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
}

/**
 * 验证表单字段
 * @param {Object} rules - 验证规则
 * @param {Object} data - 要验证的数据
 * @returns {Object} 验证结果 { isValid: boolean, errors: Array }
 */
function validateForm(rules, data) {
    const errors = [];

    for (const [field, rule] of Object.entries(rules)) {
        const value = data[field];

        // 必填验证
        if (rule.required && (!value || String(value).trim() === '')) {
            errors.push(`${rule.label || field}不能为空`);
            continue;
        }

        // 如果字段为空且不是必填，跳过其他验证
        if (!value && !rule.required) continue;

        // 长度验证
        if (rule.maxLength && String(value).length > rule.maxLength) {
            errors.push(`${rule.label || field}不能超过${rule.maxLength}个字符`);
        }

        if (rule.minLength && String(value).length < rule.minLength) {
            errors.push(`${rule.label || field}不能少于${rule.minLength}个字符`);
        }

        // 数字验证
        if (rule.type === 'number') {
            const num = parseFloat(value);
            if (isNaN(num)) {
                errors.push(`${rule.label || field}必须是数字`);
            } else {
                if (rule.min !== undefined && num < rule.min) {
                    errors.push(`${rule.label || field}不能小于${rule.min}`);
                }
                if (rule.max !== undefined && num > rule.max) {
                    errors.push(`${rule.label || field}不能大于${rule.max}`);
                }
            }
        }

        // 邮箱验证
        if (rule.type === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                errors.push(`${rule.label || field}格式不正确`);
            }
        }

        // 手机号验证
        if (rule.type === 'phone') {
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(value)) {
                errors.push(`${rule.label || field}格式不正确`);
            }
        }

        // 自定义验证函数
        if (rule.validator && typeof rule.validator === 'function') {
            const result = rule.validator(value, data);
            if (result !== true) {
                errors.push(result || `${rule.label || field}验证失败`);
            }
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

// ========================================
// 6. 业务逻辑通用函数模块
// ========================================

/**
 * 分析科目成绩并生成学习建议
 * @param {Object} subjectScores - 科目分数对象 { '语文': 85, '数学': 72, ... }
 * @param {Object} options - 选项 { weakThreshold: 60, strongThreshold: 80 }
 * @returns {Object} 分析结果 { weakSubjects: [], strongSubjects: [], advice: [] }
 */
function analyzeSubjectPerformance(subjectScores, options = {}) {
    const { weakThreshold = 60, strongThreshold = 80 } = options;
    // 从传入的成绩对象中获取科目列表，排除非科目字段
    const excludeFields = ['总分', '学号', '姓名', 'class_rank', 'grade_rank'];
    const subjects = Object.keys(subjectScores).filter(key => !excludeFields.includes(key));

    const weakSubjects = [];
    const strongSubjects = [];
    const advice = [];

    // 分析各科目表现
    subjects.forEach(subject => {
        const score = subjectScores[subject];
        if (score !== undefined && score !== null && score !== '') {
            const numScore = parseFloat(score);
            if (!isNaN(numScore)) {
                if (numScore < weakThreshold) {
                    weakSubjects.push(subject);
                } else if (numScore >= strongThreshold) {
                    strongSubjects.push(subject);
                }
            }
        }
    });

    // 生成重点提升科目建议
    if (weakSubjects.length > 0) {
        advice.push({
            type: 'weak',
            icon: 'bi-lightbulb',
            title: '重点提升科目',
            description: `建议重点提升以下科目：${weakSubjects.join('、')}`,
            suggestions: '制定详细的学习计划，每天安排固定时间复习这些科目。'
        });
    }

    // 生成优势科目建议
    if (strongSubjects.length > 0) {
        advice.push({
            type: 'strong',
            icon: 'bi-check-circle',
            title: '优势科目',
            description: `您的优势科目包括：${strongSubjects.join('、')}`,
            suggestions: '保持优势科目的学习状态，可以尝试更高难度的题目。'
        });
    }

    // 生成综合建议
    if (weakSubjects.length === 0 && strongSubjects.length > 0) {
        advice.push({
            type: 'excellent',
            icon: 'bi-trophy',
            title: '整体表现优秀',
            description: '各科目成绩均衡发展，继续保持！',
            suggestions: '可以考虑拓展课外知识，培养更广泛的兴趣。'
        });
    } else if (weakSubjects.length > strongSubjects.length) {
        advice.push({
            type: 'improvement',
            icon: 'bi-graph-up-arrow',
            title: '需要加强学习',
            description: '多个科目需要提升，建议制定系统的学习计划。',
            suggestions: '可以寻求老师或同学的帮助，找到适合自己的学习方法。'
        });
    }

    return {
        weakSubjects,
        strongSubjects,
        advice,
        totalSubjects: subjects.length,
        analyzedSubjects: subjects.filter(s => subjectScores[s] !== undefined && subjectScores[s] !== null && subjectScores[s] !== '').length
    };
}

/**
 * 渲染学习建议 HTML
 * @param {Array} advice - 建议数组
 * @param {string} containerId - 容器元素 ID
 */
function renderLearningAdvice(advice, containerId) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.warn(`找不到建议容器: ${containerId}`);
        return;
    }

    if (!advice || advice.length === 0) {
        container.innerHTML = '<div class="text-muted text-center py-3">暂无学习建议</div>';
        return;
    }

    const adviceHtml = advice.map(item => `
        <div class="advice-card advice-${item.type}">
            <div class="advice-header">
                <i class="bi ${item.icon} advice-icon"></i>
                <h6 class="advice-title">${item.title}</h6>
            </div>
            <div class="advice-content">
                <p class="advice-description">${item.description}</p>
                <p class="advice-suggestions">${item.suggestions}</p>
            </div>
        </div>
    `).join('');

    container.innerHTML = adviceHtml;
}

/**
 * 获取成功消息文本
 * @param {string} status - 状态
 * @returns {string} 成功消息
 */
function getSuccessMessage(status) {
    const messages = {
        '已批准': '请假申请已批准',
        '已拒绝': '请假申请已拒绝',
        '已销假': '请假记录已标记为销假',
        '已提交': '申请已提交',
        '已保存': '数据已保存',
        '已删除': '删除成功',
        '已更新': '更新成功'
    };
    return messages[status] || '操作完成';
}
