/* =====================
   公共全局样式（public.css）
   ===================== */

/* ========== 1. 全局变量 ========== */
:root {
    /* 基础颜色变量 */
    --primary-color: #527aaf;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;

    /* 渐变背景变量 */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, #0a58ca 100%);
    --gradient-light: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    --gradient-success: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --gradient-danger: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    --gradient-warning: linear-gradient(135deg, #ffc107 0%, #ffca2c 100%);
    --gradient-info: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    --gradient-secondary: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    --gradient-white: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);

    /* 阴影变量 */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.08);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.12);
    --shadow-xl: 0 10px 30px rgba(0,0,0,0.2);

    /* 边框圆角变量 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* ========== 2. 全局基础布局 ========== */
body {
    background-color: var(--light-color);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* ========== 3. 卡片组件 ========== */
/* 基础卡片样式 */
.card {
    border: none;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
    overflow: hidden;
    background: white;
}

/* 卡片悬停效果 */
.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 卡片头部 */
.card-header {
    border-bottom: 1px solid rgba(0,0,0,.125);
    padding: 0.75rem 1rem;
    background: var(--gradient-light);
}

.card-header .card-title {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 1rem;
    margin: 0;
}

/* 卡片主体 */
.card-body {
    padding: 1rem;
}

/* ========== 4. 按钮组件 ========== */
/* 基础按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: var(--radius-sm);
    font-size: 0.9rem;
    border-width: 2px;
    cursor: pointer;
}

/* 主要按钮 */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13,110,253,.2);
    filter: brightness(1.05);
}

/* 轮廓按钮样式 */
.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
    transform: translateY(-1px);
}

.btn-outline-success {
    color: var(--success-color);
    border-color: var(--success-color);
    background: transparent;
}

.btn-outline-success:hover,
.btn-outline-success.active {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
    transform: translateY(-1px);
}

.btn-outline-info {
    color: var(--info-color);
    border-color: var(--info-color);
    background: transparent;
}

.btn-outline-info:hover,
.btn-outline-info.active {
    background: var(--info-color);
    border-color: var(--info-color);
    color: white;
    transform: translateY(-1px);
}

.btn-outline-warning {
    color: var(--warning-color);
    border-color: var(--warning-color);
    background: transparent;
}

.btn-outline-warning:hover,
.btn-outline-warning.active {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: #212529;
    transform: translateY(-1px);
}

/* 按钮焦点和激活状态 */
.btn:active,
.btn:focus {
    box-shadow: 0 0 0 0.15rem rgba(82, 122, 175, 0.2);
    outline: none;
}

/* ========== 5. 表单（Form） ========== */
.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}
.form-select, .form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}
.form-select:focus, .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13,110,253,.15);
}

/* ========== 6. 表格组件 ========== */
/* 基础表格样式 */
.table {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    background: white;
}

/* 表格头部 */
.table thead th {
    background: var(--gradient-light);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 0.75rem;
}

/* 表格行悬停效果 */
.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
}

/* ========== 7. 徽章和状态标签组件 ========== */
/* 基础徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    background: #e9f2ff;
    color: #2b7cff;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-block;
}

/* 徽章颜色变体 */
.badge.bg-success,
.status-badge.success {
    background: var(--gradient-success) !important;
    color: white !important;
}

.badge.bg-danger,
.status-badge.danger {
    background: var(--gradient-danger) !important;
    color: white !important;
}

.badge.bg-warning,
.status-badge.warning {
    background: var(--gradient-warning) !important;
    color: #212529 !important;
}

.badge.bg-info,
.status-badge.info {
    background: var(--gradient-info) !important;
    color: white !important;
}

.badge.bg-secondary,
.status-badge.secondary {
    background: var(--gradient-secondary) !important;
    color: white !important;
}

.badge.bg-primary {
    background: var(--gradient-primary) !important;
    color: white !important;
}

/* 状态徽章组件 */
.status-badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
    box-shadow: var(--shadow-sm);
    display: inline-block;
    transition: all 0.3s ease;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* ========== 8. 模态框（Modal） ========== */
/* 基础模态框样式 */
.modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0;
    padding: 1rem 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #dee2e6;
}

/* 增强模态框样式 */
.modal-enhanced .modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    transform: scale(0.8);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enhanced.show .modal-content {
    transform: scale(1);
    opacity: 1;
}

.modal-enhanced .modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0a58ca 100%);
    color: white;
    border-bottom: none;
    padding: 2rem 2.5rem;
    position: relative;
    overflow: hidden;
}

.modal-enhanced .modal-title {
    font-weight: 700;
    font-size: 1.3em;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    z-index: 1;
}

.modal-enhanced .modal-body {
    padding: 2.5rem;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
}

.modal-enhanced .modal-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    padding: 1.5rem 2.5rem;
}

/* ========== 9. Toast 提示 ========== */
.toast {
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,.15);
    border: none;
    transition: all 0.3s ease;
}

.toast-header {
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    font-weight: 600;
}

/* ========== 10. 输入框组 ========== */
.input-group {
    border-radius: 0.375rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.input-group .form-control {
    border-radius: 0;
}

.input-group .btn {
    border-radius: 0;
}

.input-group:focus-within {
    box-shadow: 0 0 0 0.25rem rgba(13,110,253,.15);
}

/* ========== 11. 消息提示 ========== */
/* 错误提示容器 */
#error-container {
    margin: 1rem 0;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #fff3f3;
    border: 1px solid #ffcdd2;
    animation: slideInDown 0.3s ease-out;
}

/* 通用消息提示样式 */
.alert {
    border-radius: 0.5rem;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    animation: slideInDown 0.3s ease-out;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(253, 126, 20, 0.1) 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 202, 44, 0.1) 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(111, 66, 193, 0.1) 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* ========== 12. 加载和动画效果 ========== */
/* 基础加载动画 */
.spinner-border {
    margin-right: 0.5rem;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 加载遮罩层 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 15px;
}

/* 自定义加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 骨架屏加载效果 */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    height: 20px;
    margin-bottom: 0.5rem;
}

/* 动画关键帧 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 动画配置 */
.animate__animated {
    --animate-duration: 0.8s;
}

/* ========== 13. 趋势指示器组件 ========== */
/* 基础趋势指示器样式 */
.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

/* 上升趋势指示器 */
.trend-indicator.up {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

/* 下降趋势指示器 */
.trend-indicator.down {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

/* 稳定趋势指示器 */
.trend-indicator.stable {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

/* ========== 14. 统计卡片组件 ========== */
/* 基础统计卡片样式 */
.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 统计卡片悬停效果 */
.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 统计卡片图标 */
.stat-card .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    flex-shrink: 0;
}

/* 统计卡片内容区域 */
.stat-card .stat-content {
    flex-grow: 1;
}

/* 统计数值 */
.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
    line-height: 1;
    margin-bottom: 0.25rem;
}

/* 统计标签 */
.stat-card .stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* ========== 15. 空状态和工具提示组件 ========== */
/* 空状态组件 */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.empty-state .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #495057;
}

.empty-state .empty-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
}

/* 工具提示组件 */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip-custom:hover::after {
    opacity: 1;
    visibility: visible;
}

/* ========== 16. 通用小组件 ========== */
/* 可复用小组件 */
.bg-light.rounded {
    border-radius: 0.5rem;
}

.bg-light.rounded ul {
    list-style-type: none;
    padding: 0;
}

.bg-light.rounded li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.bg-light.rounded li:last-child {
    border-bottom: none;
}

/* 通用图片查看样式 */
.chart-image {
    cursor: pointer;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.chart-image:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 其它通用样式 */
.text-muted {
    color: #6c757d !important;
}

.bi {
    font-size: 1.2em;
}

.bg-pink {
    background-color: #f06292 !important;
    color: #fff !important;
}

/* ========== 17. 响应式设计 ========== */
/* 平板设备 (991.98px 及以下) */
@media (max-width: 991.98px) {
    /* 基础布局 */
    .container {
        max-width: 100%;
        padding: 0 1rem;
    }

    /* 卡片组件 */
    .card {
        margin-bottom: 1.5rem;
    }

    /* 表单组件 */
    .form-select, .form-control {
        font-size: 1rem;
        padding: 0.6rem 0.8rem;
    }

    /* 按钮组件 */
    .btn {
        padding: 0.6rem 1rem;
        font-size: 0.95rem;
    }

    /* 徽章组件 */
    .badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .status-badge {
        font-size: 0.85rem;
        padding: 0.4rem 0.6rem;
    }

    /* 趋势指示器 */
    .trend-indicator {
        font-size: 0.75rem;
        padding: 0.2rem 0.4rem;
    }

    /* 统计卡片 */
    .stat-card {
        padding: 1.25rem;
        gap: 0.8rem;
    }

    .stat-card .stat-icon {
        width: 55px;
        height: 55px;
        font-size: 1.6rem;
    }

    .stat-card .stat-value {
        font-size: 1.8rem;
    }
}

/* 手机设备 (767.98px 及以下) */
@media (max-width: 767.98px) {
    /* 基础布局 */
    .container {
        padding: 0 0.75rem;
    }

    /* 卡片组件 */
    .card {
        margin-bottom: 1rem;
        border-radius: 0.75rem;
    }

    .card-header {
        padding: 1rem;
        font-size: 0.95rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* 按钮组件 */
    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 1rem;
        border-radius: 0.5rem;
    }

    /* 表单组件 */
    .form-select, .form-control {
        font-size: 1rem;
        padding: 0.7rem 0.9rem;
    }

    .form-label {
        font-size: 1rem;
    }

    .input-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    .input-group .form-control,
    .input-group .btn {
        border-radius: 0.375rem;
    }

    /* 表格组件 */
    .table {
        font-size: 0.9rem;
    }

    .table thead th {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    .table tbody td {
        padding: 0.5rem;
    }

    /* 徽章组件 */
    .badge {
        font-size: 0.7rem;
        padding: 0.4rem 0.6rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
    }

    /* 模态框 */
    .modal-content {
        margin: 1rem;
        border-radius: 1rem;
    }

    .modal-enhanced .modal-body {
        padding: 1.5rem;
    }

    .modal-enhanced .modal-header {
        padding: 1.5rem;
    }

    .modal-enhanced .modal-footer {
        padding: 1rem 1.5rem;
    }

    /* Toast 提示 */
    .toast {
        font-size: 0.9rem;
    }

    /* 消息提示 */
    .alert {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
    }

    #error-container {
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    /* 趋势指示器 */
    .trend-indicator {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        gap: 0.2rem;
    }

    /* 统计卡片 */
    .stat-card {
        padding: 1rem;
        gap: 0.75rem;
        flex-direction: column;
        text-align: center;
    }

    .stat-card .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        margin: 0 auto;
    }

    .stat-card .stat-value {
        font-size: 1.5rem;
    }

    .stat-card .stat-label {
        font-size: 0.85rem;
    }

    /* 空状态 */
    .empty-state {
        padding: 2rem 1rem;
    }

    .empty-state .empty-icon {
        font-size: 3rem;
    }

    .empty-state .empty-title {
        font-size: 1.1rem;
    }

    .empty-state .empty-description {
        font-size: 0.9rem;
    }

    /* 工具提示 */
    .tooltip-custom::after {
        font-size: 0.75rem;
        padding: 0.4rem;
    }

    /* 通用小组件 */
    .chart-image {
        border-radius: 0.5rem;
    }

    .bg-light.rounded {
        border-radius: 0.75rem;
    }

    .bg-light.rounded li {
        padding: 0.6rem 0;
        font-size: 0.9rem;
    }
}
