import os

# 数据库配置
EXAM_DB_CONFIG = {
    'host': os.getenv('EXAM_DB_HOST', '127.0.0.1'),
    'port': int(os.getenv('EXAM_DB_PORT', 3306)),
    'user': os.getenv('EXAM_DB_USER', 'root'),
    'passwd': os.getenv('EXAM_DB_PASS', '**********'),
    'autocommit': True
}

ACCOUNT_DB_CONFIG = {
    'host': os.getenv('ACCOUNT_DB_HOST', '127.0.0.1'),
    'port': int(os.getenv('ACCOUNT_DB_PORT', 3306)),
    'user': os.getenv('ACCOUNT_DB_USER', 'root'),
    'passwd': os.getenv('ACCOUNT_DB_PASS', '**********'),
    'autocommit': True
}

# 数据目录配置
DATA_FOLDER = os.getenv('DATA_FOLDER', 'data/各班学生成绩')
IMAGES_FOLDER = os.getenv('IMAGES_FOLDER', 'data/可视化')  # 生成的图片保存路径
DOWNLOAD_FOLDER = os.getenv('DOWNLOAD_FOLDER', '网页保存')  # 下载的图片和导出的数据保存路径 