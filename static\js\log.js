/**
 * ========================================
 * 登录页面功能模块 (log.js)
 * ========================================
 * 专门处理用户登录相关功能
 * 依赖：public.js（通用工具函数）
 * ========================================
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面组件
    if (typeof initPageComponents === 'function') {
        initPageComponents();
    }

    const loginForm = document.getElementById('loginForm');

    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();

        // 使用统一的表单验证
        const validationRules = {
            username: { required: true, label: '用户名', minLength: 1 },
            password: { required: true, label: '密码', minLength: 1 }
        };

        const validationResult = validateForm(validationRules, { username, password });
        if (!validationResult.isValid) {
            showErrorToast(validationResult.errors[0]);
            return;
        }

        // 显示加载提示
        showLoadingToast();

        // 使用统一的 API 请求函数
        performLogin(username, password);
    });
});

/**
 * 执行登录操作
 * @param {string} username - 用户名
 * @param {string} password - 密码
 */
async function performLogin(username, password) {
    try {
        const data = await apiPost('/api/login', {
            username: username,
            password: password
        }, false); // 不显示默认的加载提示，因为我们已经显示了

        handleLoginSuccess(data);
    } catch (error) {
        handleLoginError(error);
    } finally {
        hideLoadingToast();
    }
}

/**
 * 处理登录成功
 * @param {Object} data - 登录响应数据
 */
function handleLoginSuccess(data) {
    console.log('Login response:', data);

    if (!data.success) {
        handleLoginError(new Error(data.message || '登录失败'));
        return;
    }

    // 保存用户会话信息
    saveUserSession(data);

    // 显示成功提示并跳转
    Swal.fire({
        title: '登录成功',
        text: '正在跳转到主页...',
        icon: 'success',
        timer: 1500,
        showConfirmButton: false
    }).then(() => {
        redirectToUserPage(data.user_type, data.is_head_teacher);
    });
}

/**
 * 保存用户会话信息
 * @param {Object} data - 用户数据
 */
function saveUserSession(data) {
    sessionStorage.setItem('isLoggedIn', 'true');
    sessionStorage.setItem('username', data.user);
    sessionStorage.setItem('grade', data.grade);
    sessionStorage.setItem('user_type', data.user_type);

    if (data.user_type === 'teacher') {
        if (data.class_name) {
            sessionStorage.setItem('class_name', data.class_name);
        }
        if (data.is_head_teacher !== undefined) {
            sessionStorage.setItem('is_head_teacher', data.is_head_teacher.toString());
        }
    } else if (data.user_type === 'student') {
        if (data.name) {
            sessionStorage.setItem('name', data.name);
        }
        if (data.class_name) {
            sessionStorage.setItem('class_name', data.class_name);
        }
    }
}

/**
 * 根据用户类型跳转到相应页面
 * @param {string} userType - 用户类型
 * @param {boolean} isHeadTeacher - 是否为班主任
 */
function redirectToUserPage(userType, isHeadTeacher) {
    const routes = {
        'admin': '/html/admin.html',
        'student': '/html/student.html',
        'teacher': isHeadTeacher ? '/html/teacher_manage.html' : '/html/teacher_analyze.html'
    };

    const targetUrl = routes[userType] || '/html/teacher_analyze.html';
    window.location.href = targetUrl;
}

/**
 * 处理登录错误
 * @param {Error} error - 错误对象
 */
function handleLoginError(error) {
    console.error('Login error:', error);

    let errorMessage = '登录失败';

    if (error.message) {
        if (error.message.includes('账号不存在') || error.message.includes('密码错误')) {
            errorMessage = '账号不存在或密码错误';
        } else if (error.message.includes('数据库') || error.message.includes('连接失败')) {
            errorMessage = '数据库错误，请联系管理员';
        } else if (error.message.includes('Failed to fetch') || error.message.includes('网络')) {
            errorMessage = '无法连接到服务器，请确保后端服务器正在运行';
        } else {
            errorMessage = error.message;
        }
    }

    Swal.fire({
        title: '登录失败',
        text: errorMessage,
        icon: 'error'
    });
}
