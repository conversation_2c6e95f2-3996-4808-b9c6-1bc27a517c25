/**
 * ========================================
 * 学生个人中心功能模块 (student.js)
 * ========================================
 * 专门处理学生个人中心页面的所有功能：
 * 1. 页面初始化和用户验证
 * 2. 标签页切换管理
 * 3. 学生留言功能
 * 4. 请假申请管理
 * 5. 成绩分析和趋势展示
 * 6. 图表可视化展示
 * ========================================
 */

// ========================================
// 1. 页面初始化和用户验证模块
// ========================================

document.addEventListener('DOMContentLoaded', function() {
    // 验证学生身份和基本信息
    if (!validateStudentAccess()) {
        return;
    }

    // 初始化页面组件
    initializeStudentPage();
});

/**
 * 验证学生访问权限
 * @returns {boolean} 是否有效的学生用户
 */
function validateStudentAccess() {
    const userType = sessionStorage.getItem('user_type') || 'student';
    const username = sessionStorage.getItem('username');
    const grade = sessionStorage.getItem('grade');

    if (userType !== 'student' || !username || !grade) {
        // 不是学生或信息不完整，跳转到登录页
        window.location.href = '/html/log.html';
        return false;
    }
    return true;
}

/**
 * 初始化学生页面所有组件
 */
function initializeStudentPage() {
    // 初始化学生基本信息显示
    initStudentInfo();

    // 初始化标签页切换功能
    initTabSwitching();

    // 初始化各功能模块的事件监听器
    initEventListeners();
}

/**
 * 初始化学生基本信息显示
 */
function initStudentInfo() {
    const name = sessionStorage.getItem('name');
    const className = sessionStorage.getItem('class_name');
    const grade = sessionStorage.getItem('grade');

    if (name) document.getElementById('studentName').textContent = name;
    if (className) document.getElementById('studentClass').textContent = `班级：${className}`;
    if (grade) document.getElementById('studentGrade').textContent = `年级：${grade}`;
}

// ========================================
// 2. 标签页切换管理模块
// ========================================

/**
 * 初始化标签页切换功能
 */
function initTabSwitching() {
    const tabConfig = [
        { btnId: 'messageTabBtn', panelId: 'messagePanel', loadFunction: loadMessageTab },
        { btnId: 'leaveTabBtn', panelId: 'leavePanel', loadFunction: loadLeaveTab },
        { btnId: 'gradeTabBtn', panelId: 'gradePanel', loadFunction: loadGradeTab }
    ];

    tabConfig.forEach(config => {
        const btn = document.getElementById(config.btnId);
        const panel = document.getElementById(config.panelId);

        if (btn && panel) {
            btn.addEventListener('click', function() {
                setActiveTab(btn, panel);
                config.loadFunction();
            });
        }
    });
}

/**
 * 设置活动标签页
 * @param {HTMLElement} activeBtn - 激活的按钮
 * @param {HTMLElement} activePanel - 激活的面板
 */
function setActiveTab(activeBtn, activePanel) {
    const allBtns = ['messageTabBtn', 'leaveTabBtn', 'gradeTabBtn'];
    const allPanels = ['messagePanel', 'leavePanel', 'gradePanel'];

    // 重置所有按钮状态
    allBtns.forEach(btnId => {
        const btn = document.getElementById(btnId);
        if (btn) {
            btn.classList.remove('active');
            btn.classList.add('btn-outline-info', 'btn-outline-success', 'btn-outline-warning');
        }
    });

    // 隐藏所有面板
    allPanels.forEach(panelId => {
        const panel = document.getElementById(panelId);
        if (panel) {
            panel.style.display = 'none';
        }
    });

    // 激活当前按钮和面板
    activeBtn.classList.add('active');
    activeBtn.classList.remove('btn-outline-info', 'btn-outline-success', 'btn-outline-warning');
    activePanel.style.display = 'block';
}

/**
 * 加载留言标签页内容
 */
function loadMessageTab() {
    loadTeacherMessage();
    loadStudentMessages();
}

/**
 * 加载请假标签页内容
 */
function loadLeaveTab() {
    loadLeaveTable();
}

/**
 * 加载成绩标签页内容
 */
function loadGradeTab() {
    loadGradeData();
}
    
// ========================================
// 3. 学生留言功能模块
// ========================================

/**
 * 加载教师留言（占位函数，具体实现可能在其他地方）
 */
function loadTeacherMessage() {
    // 此函数在原代码中被调用但未定义，保留以维持兼容性
    console.log('加载教师留言功能');
}

/**
 * 加载学生留言列表
 */
async function loadStudentMessages() {
    try {
        const grade = sessionStorage.getItem('grade');
        const student_id = sessionStorage.getItem('username');

        const response = await fetch(`/api/student-messages?grade=${encodeURIComponent(grade)}&student_id=${encodeURIComponent(student_id)}`);
        const data = await response.json();

        displayStudentMessages(data);
    } catch (error) {
        console.error('加载学生留言失败:', error);
        showErrorToast('加载留言失败');
    }
}

/**
 * 显示学生留言列表
 * @param {Object} data - 留言数据
 */
function displayStudentMessages(data) {
    const messageContent = document.getElementById('messageContent');
    if (!messageContent) return;

    if (data.success && data.data && data.data.length > 0) {
        messageContent.innerHTML = data.data.map(msg => createMessageHTML(msg)).join('');
    } else {
        messageContent.innerHTML = createEmptyMessageHTML();
    }
}

/**
 * 创建单条留言的HTML
 * @param {Object} msg - 留言对象
 * @returns {string} 留言HTML字符串
 */
function createMessageHTML(msg) {
    return `
        <div class="alert alert-secondary mb-2 d-flex justify-content-between align-items-center">
            <div style="word-break:break-all;white-space:pre-line;max-width:80%;">
                <strong>${msg.name}</strong>
                <small class="text-muted">${msg.create_time}</small>
                <div class="student-message-text mt-1">${formatTextToHtml(msg.message)}</div>
            </div>
            ${msg.is_self ? `<button class='btn btn-sm btn-danger ms-2' onclick='deleteStudentMessage(${msg.id})'>删除</button>` : ''}
        </div>
    `;
}

/**
 * 创建空留言状态的HTML
 * @returns {string} 空状态HTML字符串
 */
function createEmptyMessageHTML() {
    return `
        <div class="col-12 text-center py-5">
            <i class="bi bi-chat-dots text-muted fs-3"></i>
            <p class="mt-2 text-muted">暂无留言</p>
        </div>
    `;
}

/**
 * 删除学生留言
 * @param {number} id - 留言ID
 */
window.deleteStudentMessage = async function(id) {
    if (!confirm('确定要删除这条留言吗？')) return;

    try {
        const grade = sessionStorage.getItem('grade');
        const student_id = sessionStorage.getItem('username');

        const response = await fetch(`/api/student-messages/${id}?grade=${encodeURIComponent(grade)}&student_id=${encodeURIComponent(student_id)}`, {
            method: 'DELETE'
        });
        const data = await response.json();

        if (data.success) {
            showSuccessToast('留言删除成功');
            loadStudentMessages(); // 重新加载留言列表
        } else {
            showErrorToast('删除失败：' + (data.error || '未知错误'));
        }
    } catch (error) {
        console.error('删除留言失败:', error);
        showErrorToast('删除留言失败');
    }
};
    
// ========================================
// 4. 成绩分析功能模块
// ========================================

/**
 * 加载成绩数据主函数
 */
async function loadGradeData() {
    const studentInfo = getStudentBasicInfo();

    if (!validateStudentInfo(studentInfo)) {
        showErrorToast('无法获取学生信息，请重新登录');
        return;
    }

    try {
        // 并行加载考试列表和整体成绩概览
        await Promise.all([
            loadExamList(studentInfo.grade),
            loadOverallGradeData(studentInfo.grade, studentInfo.studentId)
        ]);

        // 初始化考试选择相关事件
        initExamSelectionEvents(studentInfo);

    } catch (error) {
        console.error('加载成绩数据失败:', error);
        showErrorToast('加载成绩数据失败');
    }
}

/**
 * 获取学生基本信息
 * @returns {Object} 学生信息对象
 */
function getStudentBasicInfo() {
    return {
        studentId: sessionStorage.getItem('username'),
        grade: sessionStorage.getItem('grade'),
        className: sessionStorage.getItem('class_name')
    };
}

/**
 * 验证学生信息完整性
 * @param {Object} studentInfo - 学生信息
 * @returns {boolean} 信息是否完整
 */
function validateStudentInfo(studentInfo) {
    return studentInfo.studentId && studentInfo.grade && studentInfo.className;
}

/**
 * 初始化考试选择相关事件
 * @param {Object} studentInfo - 学生信息
 */
function initExamSelectionEvents(studentInfo) {
    const examSelect = document.getElementById('examSelect');
    const loadExamDataBtn = document.getElementById('loadExamDataBtn');

    if (examSelect && loadExamDataBtn) {
        // 考试选择变化事件
        examSelect.addEventListener('change', function() {
            loadExamDataBtn.disabled = !this.value;
        });

        // 加载考试数据按钮事件
        loadExamDataBtn.addEventListener('click', function() {
            const selectedExam = examSelect.value;
            if (selectedExam) {
                loadExamDetail(studentInfo.grade, studentInfo.className, selectedExam, studentInfo.studentId);
            }
        });
    }
}

/**
 * 加载考试列表
 * @param {string} grade - 年级
 */
async function loadExamList(grade) {
    try {
        const response = await fetch(`/api/grade-exams?grade=${encodeURIComponent(grade)}`);
        const data = await response.json();

        populateExamSelect(data.exams);
    } catch (error) {
        console.error('加载考试列表失败:', error);
        showErrorToast('加载考试列表失败');
    }
}

/**
 * 填充考试选择下拉框
 * @param {Array} exams - 考试列表
 */
function populateExamSelect(exams) {
    const examSelect = document.getElementById('examSelect');
    if (!examSelect) return;

    examSelect.innerHTML = '<option value="">请选择考试</option>';

    if (exams && Array.isArray(exams)) {
        exams.forEach(exam => {
            const option = document.createElement('option');
            option.value = exam;
            option.textContent = exam;
            examSelect.appendChild(option);
        });
    }
}
    
/**
 * 加载整体成绩概览数据
 * @param {string} grade - 年级
 * @param {string} studentId - 学生ID
 */
async function loadOverallGradeData(grade, studentId) {
    try {
        showLoadingToast();
        const response = await fetch(`/api/student-grade-trend?grade=${encodeURIComponent(grade)}&student_id=${encodeURIComponent(studentId)}`);
        const data = await response.json();

        console.log('student-grade-trend返回', data);

        if (data.success) {
            displayOverallGradeData(data);
        } else {
            console.warn('整体成绩数据加载失败:', data.error);
        }
    } catch (error) {
        console.error('加载整体成绩数据失败:', error);
        showErrorToast('加载整体成绩数据失败');
    } finally {
        hideLoadingToast();
    }
}

/**
 * 显示整体成绩概览数据
 * @param {Object} data - 成绩趋势数据
 */
function displayOverallGradeData(data) {
    console.log('显示整体成绩概览数据:', data);

    // 更新基础统计信息
    updateBasicStats(data);

    // 更新各科平均分显示
    updateSubjectAverages(data);

    // 渲染趋势图表或显示提示信息
    renderTrendAnalysis(data);
}

/**
 * 更新基础统计信息
 * @param {Object} data - 成绩数据
 */
function updateBasicStats(data) {
    // 总考试次数
    const examCount = data.data ? data.data.length : 0;
    updateElementText('totalExamCount', examCount || '-');

    // 平均总分
    const avgTotal = calculateAverageTotal(data.data);
    updateElementText('overallAvgTotal', avgTotal);

    // 最佳排名
    const bestRank = getBestRank(data.trend_analysis);
    updateElementText('bestRank', bestRank);

    // 进步幅度
    const improvement = getImprovementText(data.trend_analysis);
    updateElementText('improvementRange', improvement);
}

/**
 * 计算平均总分
 * @param {Array} examData - 考试数据数组
 * @returns {string} 格式化的平均分
 */
function calculateAverageTotal(examData) {
    if (!examData || examData.length === 0) return '-';

    let totalSum = 0, count = 0;
    examData.forEach(exam => {
        if (exam.total_score !== null && exam.total_score !== undefined) {
            totalSum += Number(exam.total_score);
            count++;
        }
    });

    return count > 0 ? (totalSum / count).toFixed(1) : '-';
}

/**
 * 获取最佳排名
 * @param {Object} trendAnalysis - 趋势分析数据
 * @returns {string} 最佳排名文本
 */
function getBestRank(trendAnalysis) {
    if (trendAnalysis && trendAnalysis.best_class_rank !== undefined && trendAnalysis.best_class_rank !== null) {
        return trendAnalysis.best_class_rank;
    }
    return '-';
}

/**
 * 获取进步幅度文本
 * @param {Object} trendAnalysis - 趋势分析数据
 * @returns {string} 进步幅度文本
 */
function getImprovementText(trendAnalysis) {
    if (trendAnalysis && trendAnalysis.improvement !== undefined && trendAnalysis.improvement !== null) {
        const improvement = trendAnalysis.improvement;
        return improvement > 0 ? `+${improvement}` : improvement.toString();
    }
    return '-';
}

/**
 * 更新元素文本内容
 * @param {string} elementId - 元素ID
 * @param {string} text - 文本内容
 */
function updateElementText(elementId, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = text;
    }
}

/**
 * 更新各科平均分显示
 * @param {Object} data - 成绩数据
 */
function updateSubjectAverages(data) {
    const subjectMapping = {
        '语文': 'overallAvgChinese',
        '数学': 'overallAvgMath',
        '英语': 'overallAvgEnglish',
        '物理': 'overallAvgPhysics',
        '化学': 'overallAvgChemistry',
        '生物': 'overallAvgBiology'
    };

    const subjectAverages = getSubjectAverages(data.trend_analysis);

    Object.entries(subjectMapping).forEach(([subject, elementId]) => {
        updateSubjectScore(elementId, subjectAverages[subject]);
    });
}

/**
 * 获取各科平均分数据
 * @param {Object} trendAnalysis - 趋势分析数据
 * @returns {Object} 各科平均分对象
 */
function getSubjectAverages(trendAnalysis) {
    return (trendAnalysis && trendAnalysis.subject_averages) ? trendAnalysis.subject_averages : {};
}

/**
 * 更新单科成绩显示
 * @param {string} elementId - 元素ID
 * @param {number} score - 分数
 */
function updateSubjectScore(elementId, score) {
    const element = document.getElementById(elementId);
    if (!element) return;

    if (score !== undefined && score !== null) {
        element.textContent = score.toFixed(1);
        element.className = getScoreColorClass(score);
    } else {
        element.textContent = '-';
        element.className = 'subject-score text-muted';
    }
}

/**
 * 根据分数获取对应的CSS类名
 * @param {number} score - 分数
 * @returns {string} CSS类名
 */
function getScoreColorClass(score) {
    if (score >= 80) return 'subject-score text-success fw-bold';
    if (score >= 70) return 'subject-score text-primary fw-semibold';
    if (score >= 60) return 'subject-score text-warning';
    return 'subject-score text-danger';
}

/**
 * 渲染趋势分析
 * @param {Object} data - 成绩数据
 */
function renderTrendAnalysis(data) {
    if (data.data && data.data.length >= 2) {
        displayGradeTrend(data);
    } else {
        displayInsufficientDataMessage();
    }
}

/**
 * 显示数据不足的提示信息
 */
function displayInsufficientDataMessage() {
    const trendContainer = document.getElementById('gradeTrendContainer');
    if (trendContainer) {
        trendContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                需要至少2次考试数据才能显示趋势分析
            </div>
        `;
    }
}

// ========================================
// 5. 请假申请管理模块
// ========================================

// 全局变量存储当前要销假的请假ID
let currentCancelLeaveId = null;

/**
 * 加载请假记录表格
 */
async function loadLeaveTable() {
    const studentId = sessionStorage.getItem('username');
    const grade = sessionStorage.getItem('grade');
    const tbody = document.getElementById('leaveTableBody');

    if (!studentId || !grade) {
        displayLeaveTableError(tbody, '无法获取学生信息，请重新登录');
        return;
    }

    try {
        const response = await fetch(`/api/leaves?grade=${encodeURIComponent(grade)}&student_id=${encodeURIComponent(studentId)}`);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || '获取请假记录失败');
        }

        if (data.data && data.data.length > 0) {
            renderLeaveTableData(tbody, data.data);
        } else {
            displayEmptyLeaveTable(tbody);
        }
    } catch (error) {
        console.error('加载请假记录失败:', error);
        displayLeaveTableError(tbody, '加载失败，请稍后重试');
    }
}

/**
 * 渲染请假表格数据
 * @param {HTMLElement} tbody - 表格体元素
 * @param {Array} leaveData - 请假数据数组
 */
function renderLeaveTableData(tbody, leaveData) {
    const today = new Date().toISOString().split('T')[0];

    const tableRows = leaveData.map(record => {
        const statusHtml = getLeaveStatusHtml(record.status);
        const canCancel = record.status === '已批准' && record.end_date >= today;
        const reasonDisplay = formatLeaveReasonDisplay(record.reason);
        const actionHtml = getLeaveActionHtml(record, canCancel);

        return `
            <tr>
                <td>${record.start_date}</td>
                <td>${record.end_date}</td>
                <td class="${reasonDisplay.cellClass}" title="${reasonDisplay.title}">
                    <div class="${reasonDisplay.reasonClass}"
                         ${reasonDisplay.needsTruncation ? `onclick="showFullReason('${record.reason.replace(/'/g, "\\'")}', '${record.student_name || ''}')"` : ''}>
                        ${reasonDisplay.displayText}
                    </div>
                </td>
                <td>${statusHtml}</td>
                <td>${actionHtml}</td>
            </tr>
        `;
    }).join('');

    tbody.innerHTML = tableRows;
}

/**
 * 获取请假状态HTML
 * @param {string} status - 状态
 * @returns {string} 状态HTML
 */
function getLeaveStatusHtml(status) {
    const statusMap = {
        '已批准': '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>已批准</span>',
        '已拒绝': '<span class="badge bg-danger"><i class="bi bi-x-circle me-1"></i>已拒绝</span>',
        '已销假': '<span class="badge bg-secondary"><i class="bi bi-arrow-repeat me-1"></i>已销假</span>',
        '待审批': '<span class="badge bg-warning text-dark"><i class="bi bi-hourglass-split me-1"></i>待审批</span>'
    };
    return statusMap[status] || `<span class="badge bg-secondary">${status}</span>`;
}

/**
 * 格式化请假原因显示
 * @param {string} reason - 请假原因
 * @returns {Object} 格式化结果
 */
function formatLeaveReasonDisplay(reason) {
    const needsTruncation = reason.length > 60 || reason.includes('\n');
    const reasonClass = needsTruncation ? 'leave-reason-text truncated' : 'leave-reason-text';
    const reasonTitle = needsTruncation ? `点击查看完整内容\n${reason}` : reason;
    const cellClass = needsTruncation ? 'leave-reason-cell clickable' : 'leave-reason-cell';
    const displayText = needsTruncation ? reason.substring(0, 60) + '...' : reason;

    return {
        needsTruncation,
        reasonClass,
        title: reasonTitle,
        cellClass,
        displayText
    };
}

/**
 * 获取请假操作HTML
 * @param {Object} record - 请假记录
 * @param {boolean} canCancel - 是否可以销假
 * @returns {string} 操作HTML
 */
function getLeaveActionHtml(record, canCancel) {
    if (canCancel) {
        return `<button class="btn btn-sm btn-outline-info" onclick="showCancelLeaveModal(${record.id})">
            <i class="bi bi-arrow-repeat me-1"></i>销假
        </button>`;
    } else if (record.status === '待审批') {
        return '<span class="text-muted small">等待审批</span>';
    } else if (record.status === '已拒绝') {
        return '<span class="text-muted small">已拒绝</span>';
    } else {
        return '<span class="text-muted small">已完成</span>';
    }
}

/**
 * 显示空的请假表格
 * @param {HTMLElement} tbody - 表格体元素
 */
function displayEmptyLeaveTable(tbody) {
    tbody.innerHTML = `
        <tr>
            <td colspan="5" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                    <i class="bi bi-calendar-x text-muted fs-1 mb-2"></i>
                    <p class="text-muted mb-0">暂无请假记录</p>
                    <small class="text-muted">点击"申请请假"按钮可提交申请</small>
                </div>
            </td>
        </tr>
    `;
}

/**
 * 显示请假表格错误信息
 * @param {HTMLElement} tbody - 表格体元素
 * @param {string} message - 错误消息
 */
function displayLeaveTableError(tbody, message) {
    tbody.innerHTML = `
        <tr>
            <td colspan="5" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                    <i class="bi bi-exclamation-triangle text-danger fs-1 mb-2"></i>
                    <p class="text-danger mb-2">${message}</p>
                    <button class="btn btn-sm btn-outline-primary" onclick="loadLeaveTable()">
                        <i class="bi bi-arrow-clockwise me-1"></i>重新加载
                    </button>
                </div>
            </td>
        </tr>
    `;
}

/**
 * 提交请假申请
 * @param {Object} formData - 表单数据
 * @returns {Promise} 提交结果
 */
async function submitLeaveApplication(formData) {
    const response = await fetch('/api/leaves', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
    });

    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
}

/**
 * 销假操作
 * @param {number} leaveId - 请假记录ID
 * @returns {Promise} 销假结果
 */
async function cancelLeaveApplication(leaveId) {
    const studentId = sessionStorage.getItem('username');
    const grade = sessionStorage.getItem('grade');

    const response = await fetch(`/api/leaves/${leaveId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            grade: grade,
            student_id: studentId,
            action: 'cancel'
        })
    });

    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
}

/**
 * 显示销假确认模态框
 * @param {number} leaveId - 请假记录ID
 */
window.showCancelLeaveModal = function(leaveId) {
    currentCancelLeaveId = leaveId;
    const cancelModal = new bootstrap.Modal(document.getElementById('cancelLeaveModal'));
    cancelModal.show();
};

/**
 * 请假原因动态调整函数
 * @param {HTMLElement} textarea - 文本域元素
 */
window.autoResizeLeaveReason = function(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 300) + 'px';
};

/**
 * 请假原因字数统计函数
 */
window.updateReasonCount = function() {
    const textarea = document.getElementById('leaveReason');
    const counter = document.getElementById('reasonCount');
    if (!textarea || !counter) return;

    const count = textarea.value.length;
    const max = textarea.getAttribute('maxlength') || 200;
    counter.textContent = `${count}/${max}`;

    if (count > max * 0.9) {
        counter.classList.add('text-danger');
        counter.classList.remove('text-warning');
    } else if (count > max * 0.7) {
        counter.classList.add('text-warning');
        counter.classList.remove('text-danger');
    } else {
        counter.classList.remove('text-danger');
        counter.classList.remove('text-warning');
    }
};

/**
 * 初始化请假表单事件
 */
function initLeaveFormEvents() {
    const leaveForm = document.getElementById('leaveForm');
    if (!leaveForm) return;

    // 初始化请假原因textarea的动态调整
    const reasonTextarea = document.getElementById('leaveReason');
    if (reasonTextarea) {
        // 初始化高度
        autoResizeLeaveReason(reasonTextarea);
        updateReasonCount();

        // 添加焦点事件
        reasonTextarea.addEventListener('focus', function() {
            this.style.borderColor = '#409eff';
            this.style.boxShadow = '0 0 0 2px #b3d8ff';
        });

        reasonTextarea.addEventListener('blur', function() {
            this.style.borderColor = '#e0e7ef';
            this.style.boxShadow = '0 1px 2px rgba(0,0,0,0.03)';
        });
    }

    // 绑定表单提交事件
    leaveForm.addEventListener('submit', handleLeaveFormSubmit);
}

/**
 * 处理请假表单提交
 * @param {Event} e - 表单提交事件
 */
async function handleLeaveFormSubmit(e) {
    e.preventDefault();

    // 获取表单数据
    const formData = getLeaveFormData();

    // 验证表单数据
    const validation = validateLeaveForm(formData);
    if (!validation.isValid) {
        showErrorToast(validation.message);
        return;
    }

    // 显示加载状态
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>提交中...';

    try {
        const result = await submitLeaveApplication(formData);

        if (result.success) {
            // 关闭模态框并重置表单
            bootstrap.Modal.getInstance(document.getElementById('leaveModal')).hide();
            document.getElementById('leaveForm').reset();

            // 重置textarea高度和字数统计
            const reasonTextarea = document.getElementById('leaveReason');
            if (reasonTextarea) {
                reasonTextarea.style.height = 'auto';
                updateReasonCount();
            }

            loadLeaveTable();
            showSuccessToast(result.message || '请假申请提交成功！');
        } else {
            throw new Error(result.error || '申请失败');
        }
    } catch (error) {
        console.error('请假申请失败:', error);
        showErrorToast(error.message || '网络错误，请重试');
    } finally {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

/**
 * 获取请假表单数据
 * @returns {Object} 表单数据
 */
function getLeaveFormData() {
    const studentId = sessionStorage.getItem('username');
    const grade = sessionStorage.getItem('grade');
    const name = sessionStorage.getItem('name');
    const className = sessionStorage.getItem('class_name');

    return {
        grade: grade,
        student_id: studentId,
        name: name,
        class_name: className,
        start_date: document.getElementById('leaveStartDate').value,
        end_date: document.getElementById('leaveEndDate').value,
        reason: document.getElementById('leaveReason').value.trim()
    };
}

/**
 * 验证请假表单数据
 * @param {Object} formData - 表单数据
 * @returns {Object} 验证结果
 */
function validateLeaveForm(formData) {
    if (!formData.start_date || !formData.end_date || !formData.reason) {
        return { isValid: false, message: '请填写完整信息' };
    }

    if (formData.end_date < formData.start_date) {
        return { isValid: false, message: '结束日期不能早于开始日期' };
    }

    // 检查是否选择过去日期（允许选择今天）
    const today = new Date().toISOString().split('T')[0];
    if (formData.start_date < today) {
        return { isValid: false, message: '不能选择过去的日期' };
    }

    if (!formData.student_id || !formData.grade || !formData.name || !formData.class_name) {
        return { isValid: false, message: '用户信息不完整，请重新登录' };
    }

    return { isValid: true };
}

/**
 * 显示完整请假原因模态框
 * @param {string} reason - 请假原因
 * @param {string} studentName - 学生姓名（未使用但保持兼容性）
 */
window.showFullReason = function(reason, studentName) {
    // 解码换行符
    const decodedReason = reason.replace(/\\n/g, '\n');

    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade leave-reason-modal" id="fullReasonModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-chat-text me-2"></i>我的请假原因
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="reason-content">${decodedReason.replace(/\n/g, '<br>')}</div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i>关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('fullReasonModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('fullReasonModal'));
    modal.show();

    // 模态框关闭后移除DOM元素
    document.getElementById('fullReasonModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });

    // 添加进入动画
    setTimeout(() => {
        const modalContent = document.querySelector('.leave-reason-modal .modal-content');
        if (modalContent) {
            modalContent.style.transform = 'scale(1)';
            modalContent.style.opacity = '1';
        }
    }, 100);
};

// ========================================
// 6. 事件监听器初始化模块
// ========================================

/**
 * 初始化所有事件监听器
 */
function initEventListeners() {
    initLeaveApplicationEvents();
    initLeaveFormEvents();
    initDataRefreshEvents();
}

/**
 * 初始化请假申请相关事件
 */
function initLeaveApplicationEvents() {
    // 刷新请假按钮
    const refreshLeaveBtn = document.getElementById('refreshLeaveBtn');
    if (refreshLeaveBtn) {
        refreshLeaveBtn.addEventListener('click', loadLeaveTable);
    }

    // 申请请假按钮
    const applyLeaveBtn = document.getElementById('applyLeaveBtn');
    if (applyLeaveBtn) {
        applyLeaveBtn.addEventListener('click', showLeaveModal);
    }

    // 快速请假按钮
    const quickLeaveBtn = document.getElementById('quickLeaveBtn');
    if (quickLeaveBtn) {
        quickLeaveBtn.addEventListener('click', showLeaveModal);
    }
}

/**
 * 显示请假申请模态框
 */
function showLeaveModal() {
    // 设置默认日期为今天
    const today = new Date().toISOString().split('T')[0];
    const startDateInput = document.getElementById('leaveStartDate');
    const endDateInput = document.getElementById('leaveEndDate');

    if (startDateInput) startDateInput.value = today;
    if (endDateInput) endDateInput.value = today;

    const leaveModal = document.getElementById('leaveModal');
    if (leaveModal) {
        const modal = new bootstrap.Modal(leaveModal);
        modal.show();
    }
}

/**
 * 初始化数据刷新相关事件
 */
function initDataRefreshEvents() {
    // 刷新数据按钮
    const refreshDataBtn = document.getElementById('refreshDataBtn');
    if (refreshDataBtn) {
        refreshDataBtn.addEventListener('click', function() {
            // 重新加载当前激活的标签页数据
            const activeTab = document.querySelector('.nav-tabs .btn.active');
            if (activeTab) {
                const tabId = activeTab.id;
                switch (tabId) {
                    case 'messageTabBtn':
                        loadMessageTab();
                        break;
                    case 'leaveTabBtn':
                        loadLeaveTab();
                        break;
                    case 'gradeTabBtn':
                        loadGradeTab();
                        break;
                }
            }
        });
    }
}
    
    // 显示成绩趋势分析（整体趋势图表）
    function displayGradeTrend(data) {
        // 获取趋势分析容器
        const trendContainer = document.getElementById('gradeTrendContainer');
        if (!trendContainer) return;
        
        // 清空内容
        trendContainer.innerHTML = '';
        
        // 检查数据
        if (!data.data || data.data.length < 2) {
            trendContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    需要至少2次考试数据才能显示趋势分析
                </div>
            `;
            return;
        }
        
        // 组装数据
        const exams = data.data.map(d => d.exam_name);
        const totalScores = data.data.map(d => Number(d.total_score) || 0);
        const subjects = ['语文', '数学', '英语', '物理', '化学', '生物'];
        const subjectSeries = {};
        
        subjects.forEach(subj => {
            subjectSeries[subj] = data.data.map(d => {
                if (d.scores && d.scores[subj] !== undefined && d.scores[subj] !== null) {
                    return Number(d.scores[subj]);
                }
                return null;
            });
        });
        
        // 创建趋势分析HTML结构
        trendContainer.innerHTML = `
            <h6 class="text-primary mb-3">
                <i class="bi bi-graph-up me-2"></i>成绩趋势分析
            </h6>
            <div class="row g-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">总分趋势</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="trend_total_score_chart" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">各科成绩趋势  （点击对应科目，图中移去对应科目显示）</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="trend_subjects_chart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 创建总分趋势图
        const totalCtx = document.getElementById('trend_total_score_chart').getContext('2d');
        new Chart(totalCtx, {
            type: 'line',
            data: {
                labels: exams,
                datasets: [{
                    label: '总分',
                    data: totalScores,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0,123,255,0.1)',
                    fill: true,
                    tension: 0.3,
                    pointRadius: 6,
                    pointBackgroundColor: '#007bff',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return `总分: ${context.parsed.y}分`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: '分数'
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
        
        // 创建各科趋势图
        const subjectsCtx = document.getElementById('trend_subjects_chart').getContext('2d');
        const colors = ['#dc3545', '#fd7e14', '#ffc107', '#198754', '#0d6efd', '#6f42c1'];
        
        const subjectDatasets = subjects.map((subject, index) => ({
            label: subject,
            data: subjectSeries[subject],
            borderColor: colors[index],
            backgroundColor: colors[index] + '20',
            fill: false,
            tension: 0.3,
            pointRadius: 4,
            pointBackgroundColor: colors[index],
            pointBorderColor: '#fff',
            pointBorderWidth: 1
        }));
        
        new Chart(subjectsCtx, {
            type: 'line',
            data: {
                labels: exams,
                datasets: subjectDatasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${context.parsed.y}分`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: '分数'
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
        
        // 添加趋势分析统计信息
        const trendStats = document.createElement('div');
        trendStats.className = 'row g-3 mt-3';
        trendStats.innerHTML = `
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <div class="text-primary fw-bold">${data.data.length}</div>
                        <div class="text-muted small">考试次数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <div class="text-success fw-bold">${Math.max(...totalScores)}</div>
                        <div class="text-muted small">最高总分</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <div class="text-warning fw-bold">${Math.min(...totalScores)}</div>
                        <div class="text-muted small">最低总分</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <div class="text-info fw-bold">${(totalScores[totalScores.length - 1] - totalScores[0]).toFixed(1)}</div>
                        <div class="text-muted small">总分变化</div>
                    </div>
                </div>
            </div>
        `;
        trendContainer.appendChild(trendStats);
    }
    
    // 显示整体成绩概览的学习建议
    function displayOverallLearningAdvice(subjectAverages) {
        const trendContainer = document.getElementById('gradeTrendContainer');
        if (!trendContainer) return;
        
        // 分析各科平均分，生成建议
        const subjects = ['语文', '数学', '英语', '物理', '化学', '生物'];
        const weakSubjects = [];
        const strongSubjects = [];
        
        subjects.forEach(subject => {
            const avg = subjectAverages[subject];
            if (avg !== undefined && avg !== null) {
                if (avg < 60) {
                    weakSubjects.push(subject);
                } else if (avg >= 80) {
                    strongSubjects.push(subject);
                }
            }
        });
        
        // 生成建议内容
        const advice = [];
        
        // 生成重点提升科目建议
        if (weakSubjects.length > 0) {
            advice.push({
                type: 'weak',
                icon: 'bi-lightbulb',
                title: '重点提升科目',
                description: `建议重点提升以下科目：${weakSubjects.join('、')}`,
                suggestions: '制定详细的学习计划，每天安排固定时间复习这些科目。'
            });
        }
        
        // 生成优势科目建议
        if (strongSubjects.length > 0) {
            advice.push({
                type: 'strong',
                icon: 'bi-check-circle',
                title: '优势科目',
                description: `您的优势科目包括：${strongSubjects.join('、')}`,
                suggestions: '保持优势科目的学习状态，可以尝试更高难度的题目。'
            });
        }
        
        // 如果没有特殊建议，生成一般性建议
        if (advice.length === 0) {
            advice.push({
                type: 'neutral',
                icon: 'bi-arrow-up-circle',
                title: '学习建议',
                description: '各科成绩相对均衡，建议继续保持当前的学习状态。',
                suggestions: '可以尝试挑战更高难度的题目，进一步提升学习水平。'
            });
        }
        
        // 创建建议HTML
        const adviceHTML = `
            <div class="mt-4">
                <div class="card learning-advice-card">
                    <div class="card-header">
                        <h6 class="mb-0 d-flex align-items-center">
                            <i class="bi bi-lightbulb me-2"></i>个性化学习建议
                        </h6>
                    </div>
                    <div class="card-body">
                        ${advice.map(item => `
                            <div class="advice-item ${item.type || 'neutral'} mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="advice-icon ${item.type || 'neutral'} me-3">
                                        <i class="bi ${item.icon || 'bi-lightbulb'}"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="advice-title mb-2">${item.title || '学习建议'}</h6>
                                        <p class="advice-description mb-2">${item.description || ''}</p>
                                        <p class="advice-suggestions mb-0 text-muted">
                                            <strong>建议：</strong>${item.suggestions || ''}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
        
        // 将建议添加到趋势容器中
        trendContainer.insertAdjacentHTML('beforeend', adviceHTML);
    }
    
    // 加载考试详情
    async function loadExamDetail(grade, className, examName, studentId) {
        try {
            showLoadingToast();
            
            // 获取班级成绩数据
            const res = await fetch('/generate_charts', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    grade: grade,
                    exam: examName,
                    class_name: className,
                    student_id: studentId
                })
            });
            
            const data = await res.json();
            
            if (!data.success) {
                throw new Error(data.error || '获取成绩数据失败');
            }
            
            // 显示成绩详情
            displayGradeDetail(data, studentId);
            
            // 显示可视化图表
            displayCharts(data);
            
            // 隐藏默认内容，显示详情和图表
            document.getElementById('gradeDefaultContent').style.display = 'none';
            document.getElementById('gradeDetailSection').style.display = 'block';
            document.getElementById('chartsSection').style.display = 'block';
            
            hideLoadingToast();
            
        } catch (error) {
            console.error('加载考试详情失败:', error);
            showErrorToast(error.message || '加载考试详情失败');
            hideLoadingToast();
        }
    }
    
    /**
     * 显示成绩详情
     * @param {Object} data - 成绩数据
     * @param {string} studentId - 学生ID（保留参数以维持接口兼容性）
     */
    function displayGradeDetail(data, studentId) {
        // 使用后端返回的真实数据
        const studentData = data.student_data;
        const studentAnalysis = data.student_analysis;
        const classAverages = data.class_averages;
        const classStats = data.class_stats;
        const studentRank = data.student_rank;
        
        // 更新班级统计信息
        if (classStats) {
            document.getElementById('totalStudents').textContent = classStats.total_students || '-';
            document.getElementById('highestScore').textContent = classStats.highest_score || '-';
            document.getElementById('lowestScore').textContent = classStats.lowest_score || '-';
            
            // 计算并显示合格率
            const passRate = classStats.pass_rate || 0;
            const passRateElement = document.getElementById('passRate');
            passRateElement.textContent = `${passRate}%`;

            // 合格率图标切换
            const passRateIcon = document.querySelector('.stat-card.pass-rate .stat-icon i');
            if (passRate > 0) {
                passRateElement.className = 'stat-value text-success';
                passRateIcon.className = 'bi bi-check-circle-fill';
                passRateIcon.style.color = '#22c55e';
            } else {
                passRateElement.className = 'stat-value text-danger';
                passRateIcon.className = 'bi bi-x-circle-fill';
                passRateIcon.style.color = '#ef4444';
            }
        }
        
        // 更新我的成绩显示
        if (studentData) {
            document.getElementById('myChinese').textContent = studentData['语文'];
            document.getElementById('myMath').textContent = studentData['数学'];
            document.getElementById('myEnglish').textContent = studentData['英语'];
            document.getElementById('myPhysics').textContent = studentData['物理'];
            document.getElementById('myChemistry').textContent = studentData['化学'];
            document.getElementById('myBiology').textContent = studentData['生物'];
            document.getElementById('myTotal').textContent = studentData['总分'];
            
            // 正确显示排名信息
            if (studentRank !== null && studentRank !== undefined) {
                document.getElementById('myRank').textContent = `${studentRank}/${classStats.total_students}`;
                
                // 添加排名分析
                const rankPercentage = Math.round((studentRank / classStats.total_students) * 100);
                let rankLevel = '';
                if (rankPercentage <= 10) rankLevel = '优秀';
                else if (rankPercentage <= 30) rankLevel = '良好';
                else if (rankPercentage <= 60) rankLevel = '中等';
                else rankLevel = '需努力';
                
                // 更新排名显示样式
                const rankElement = document.getElementById('myRank');
                rankElement.className = rankPercentage <= 10 ? 'text-success fw-bold' : 
                                       rankPercentage <= 30 ? 'text-primary fw-bold' : 
                                       rankPercentage <= 60 ? 'text-warning fw-bold' : 'text-danger fw-bold';
                rankElement.title = `排名前${rankPercentage}%，${rankLevel}`;
            } else {
                document.getElementById('myRank').textContent = '未排名';
                document.getElementById('myRank').className = 'text-muted';
            }
        }
        
        // 更新班级平均分显示
        if (classAverages) {
            document.getElementById('avgChinese').textContent = classAverages['语文'].toFixed(1);
            document.getElementById('avgMath').textContent = classAverages['数学'].toFixed(1);
            document.getElementById('avgEnglish').textContent = classAverages['英语'].toFixed(1);
            document.getElementById('avgPhysics').textContent = classAverages['物理'].toFixed(1);
            document.getElementById('avgChemistry').textContent = classAverages['化学'].toFixed(1);
            document.getElementById('avgBiology').textContent = classAverages['生物'].toFixed(1);
            document.getElementById('avgTotal').textContent = classAverages['总分'].toFixed(1);
        }
        
        // 添加成绩分析信息
        if (studentAnalysis) {
            displayGradeAnalysis(studentAnalysis, classStats);
        }
        
        // 显示预测信息
        if (studentAnalysis && studentAnalysis.prediction_info) {
            displayPredictionInfo(studentAnalysis.prediction_info);
        } else {
            // 隐藏预测信息区域
            const predictionSection = document.getElementById('predictionInfoSection');
            if (predictionSection) {
                predictionSection.style.display = 'none';
            }
        }

        // 显示后端返回的学习建议
        if (data.learning_advice) {
            displayLearningAdvice(data.learning_advice);
        } else {
            // 如果没有后端建议，隐藏建议区域
            const adviceSection = document.getElementById('examLearningAdviceSection');
            if (adviceSection) {
                adviceSection.style.display = 'none';
            }
        }
    }

    // 显示预测信息
    function displayPredictionInfo(predictionInfo) {
        const predictionSection = document.getElementById('predictionInfoSection');
        const predictionContent = document.getElementById('predictionInfoContent');

        if (!predictionSection || !predictionContent) {
            return;
        }

        const confidence = predictionInfo.confidence || 0.5;
        const type = predictionInfo.type || 'unknown';
        const factors = predictionInfo.factors || {};

        // 根据预测类型生成通俗易懂的描述
        let typeDescription = '';
        let typeExplanation = '';
        let confidenceClass = '';
        let confidenceIcon = '';
        let confidenceText = '';

        switch (type) {
            case 'first_exam':
                typeDescription = '🎯 新生预测';
                typeExplanation = '根据你在班级和年级中的排名位置，预测可能的成绩表现';
                break;
            case 'multiple_exams':
                typeDescription = '📊 智能分析';
                typeExplanation = '基于你的历史考试成绩和学习趋势，智能预测未来表现';
                break;
            case 'fallback':
                typeDescription = '📋 基础预测';
                typeExplanation = '使用标准算法进行的基础成绩预测';
                break;
            case 'fallback_percentage':
                typeDescription = '📈 简单预测';
                typeExplanation = '基于当前成绩水平的简单预测分析';
                break;
            default:
                typeDescription = '🤖 AI预测';
                typeExplanation = '人工智能算法分析你的学习情况并给出预测';
        }

        // 根据置信度设置样式和通俗说明
        if (confidence >= 0.8) {
            confidenceClass = 'text-success';
            confidenceIcon = 'bi-check-circle-fill';
            confidenceText = '预测很准确';
        } else if (confidence >= 0.6) {
            confidenceClass = 'text-warning';
            confidenceIcon = 'bi-exclamation-triangle-fill';
            confidenceText = '预测比较准确';
        } else {
            confidenceClass = 'text-danger';
            confidenceIcon = 'bi-x-circle-fill';
            confidenceText = '预测仅供参考';
        }

        // 生成通俗易懂的预测因素说明
        let factorsHTML = '';
        if (Object.keys(factors).length > 0) {
            factorsHTML = '<div class="mt-3"><h6 class="text-muted">📋 预测依据：</h6><div class="prediction-factors">';

            for (const [key, value] of Object.entries(factors)) {
                const friendlyFactor = getFriendlyFactorDescription(key, value);
                if (friendlyFactor) {
                    factorsHTML += `<div class="factor-item"><i class="bi bi-dot"></i> ${friendlyFactor}</div>`;
                }
            }
            factorsHTML += '</div></div>';
        }

        predictionContent.innerHTML = `
            <div class="prediction-main-info">
                <div class="row">
                    <div class="col-md-8">
                        <div class="prediction-item">
                            <div class="prediction-label">🎯 预测方式</div>
                            <div class="prediction-value">${typeDescription}</div>
                            <div class="prediction-explanation">${typeExplanation}</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="prediction-item">
                            <div class="prediction-label">📊 准确度</div>
                            <div class="prediction-value ${confidenceClass}">
                                <i class="bi ${confidenceIcon} me-1"></i>
                                ${confidenceText}
                            </div>
                            <div class="prediction-percentage">${(confidence * 100).toFixed(0)}%</div>
                        </div>
                    </div>
                </div>
            </div>
            ${factorsHTML}
            <div class="mt-3 prediction-disclaimer">
                <div class="disclaimer-content">
                    <i class="bi bi-lightbulb me-2"></i>
                    <strong>提示</strong>预测分数是根据你的学习情况智能分析得出的，帮助你了解自己的学习趋势。努力学习，实际成绩会更好哦！
                </div>
            </div>
        `;

        predictionSection.style.display = 'block';
    }

    // 将技术性的预测因素转换为通俗易懂的描述
    function getFriendlyFactorDescription(key, value) {
        switch (key) {
            case 'class_rank':
                return `班级排名第${value}名`;
            case 'grade_rank':
                return `年级排名第${value}名`;
            case 'class_percentile':
                return `在班级中超过了${value}%的同学`;
            case 'grade_percentile':
                return `在年级中超过了${value}%的同学`;
            case 'trend_analysis':
                return `成绩趋势：${value}`;
            case 'stability_score':
                const stabilityText = value >= 0.8 ? '成绩很稳定' : value >= 0.6 ? '成绩比较稳定' : '成绩波动较大';
                return `学习状态：${stabilityText}`;
            case 'bias_detected':
                if (Array.isArray(value) && value.length > 0) {
                    return `学科特点：${value.join('、')}`;
                }
                return null;
            case 'historical_count':
                return `参考了${value}次历史考试数据`;
            case 'note':
                return value;
            case 'error':
                return null; // 不显示错误信息给用户
            default:
                // 对于其他未知的因素，尝试友好显示
                if (typeof value === 'string') {
                    return `${key}：${value}`;
                } else if (typeof value === 'number') {
                    return `${key}：${value}`;
                }
                return null;
        }
    }

    // 显示成绩分析信息
    function displayGradeAnalysis(studentAnalysis, classStats) {
        // 创建或更新成绩分析区域
        let analysisSection = document.getElementById('gradeAnalysisSection');
        if (!analysisSection) {
            analysisSection = document.createElement('div');
            analysisSection.id = 'gradeAnalysisSection';
            analysisSection.className = 'mt-4';
            
            // 插入到成绩详情卡片后面
            const gradeDetailCard = document.querySelector('.grade-detail-card');
            if (gradeDetailCard) {
                gradeDetailCard.parentNode.insertBefore(analysisSection, gradeDetailCard.nextSibling);
            }
        }
        
        // 生成分析内容
        const subjects = ['语文', '数学', '英语', '物理', '化学', '生物'];
        let analysisHTML = `
            <div class="card grade-analysis-card">
                <div class="card-header">
                    <h6 class="mb-0 d-flex align-items-center">
                        <i class="bi bi-graph-up-arrow me-2"></i>成绩分析
                    </h6>
                </div>
                <div class="card-body">
                        <!-- 总分分析 -->
                    <div class="total-score-analysis">
                        <h6 class="text-primary mb-3">
                            <i class="bi bi-trophy me-2"></i>总分分析
                        </h6>
                        <div class="row g-3">
                                    <div class="col-md-3">
                                        <div class="analysis-item">
                                    <div class="analysis-label">我的总分</div>
                                    <div class="analysis-value text-primary">${studentAnalysis['总分'].score}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="analysis-item">
                                    <div class="analysis-label">班级平均</div>
                                    <div class="analysis-value text-success">${studentAnalysis['总分'].class_avg}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="analysis-item">
                                    <div class="analysis-label">与平均分差距</div>
                                    <div class="analysis-value ${studentAnalysis['总分'].vs_class_avg >= 0 ? 'text-success' : 'text-danger'}">
                                                ${studentAnalysis['总分'].vs_class_avg >= 0 ? '+' : ''}${studentAnalysis['总分'].vs_class_avg}
                                    </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="analysis-item">
                                    <div class="analysis-label">预测分数</div>
                                    <div class="analysis-value text-info">${studentAnalysis['总分'].percentage}分</div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="analysis-item">
                                    <div class="analysis-label">班级排名</div>
                                    <div class="analysis-value">
                                        <span class="fw-bold">${studentAnalysis['总分'].rank || '未排名'}</span>
                                        <span class="text-muted ms-2">/ ${studentAnalysis['总分'].total_students}人</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 各科目详细分析 -->
                    <div class="mt-4">
                        <h6 class="text-success mb-3">
                            <i class="bi bi-book me-2"></i>各科目详细分析
                        </h6>
                            <div class="row g-3">
        `;
        
        subjects.forEach(subject => {
            const analysis = studentAnalysis[subject];
            const percentage = analysis.percentage; // 这是预测分数
            const actualScore = analysis.score; // 实际成绩
            const fullMark = analysis.full_mark; // 满分

            // 基于实际成绩计算表现等级
            const actualPercentage = (actualScore / fullMark) * 100;
            let levelClass = 'average';
            let levelText = '中等';

            if (actualPercentage >= 90) {
                levelClass = 'excellent';
                levelText = '优秀';
            } else if (actualPercentage >= 80) {
                levelClass = 'good';
                levelText = '良好';
            } else if (actualPercentage >= 70) {
                levelClass = 'average';
                levelText = '中等';
            } else if (actualPercentage >= 60) {
                levelClass = 'pass';
                levelText = '及格';
            } else {
                levelClass = 'weak';
                levelText = '不及格';
            }
            
            const vsClassAvg = analysis.vs_class_avg;
            const vsClassAvgClass = vsClassAvg >= 0 ? 'text-success' : 'text-danger';
            const vsClassAvgSign = vsClassAvg >= 0 ? '+' : '';
            
            // 计算科目排名百分比（用于后续可能的扩展功能）
            const rankInSubject = analysis.rank_in_subject;
            const rankPercentage = rankInSubject ? Math.round((rankInSubject / classStats.total_students) * 100) : null;

            // 根据排名百分比确定等级（预留功能）
            if (rankPercentage !== null) {
                // 可以在这里添加基于排名的特殊样式或提示
                console.log(`${subject}排名百分比: ${rankPercentage}%`);
            }
            
            // 生成改进建议
            let improvementSuggestion = '';
            if (percentage < 60) {
                improvementSuggestion = `建议加强${subject}基础知识，多做练习题，及时请教老师。`;
            } else if (percentage < 70) {
                improvementSuggestion = `建议增加${subject}练习时间，重点突破薄弱环节。`;
            } else if (percentage < 80) {
                improvementSuggestion = `建议保持${subject}学习状态，适当增加难度。`;
            } else {
                improvementSuggestion = `建议保持${subject}优势，尝试更高难度内容。`;
            }
            
            analysisHTML += `
                <div class="col-md-6">
                    <div class="subject-analysis-item ${levelClass}">
                        <div class="subject-header">
                            <div class="subject-name">${subject}</div>
                            <div class="subject-level ${levelClass}">${levelText}</div>
                        </div>
                        
                        <div class="subject-stats">
                            <div class="subject-stat">
                                <div class="subject-stat-label">我的分数</div>
                                <div class="subject-stat-value">${analysis.score}</div>
                                </div>
                            <div class="subject-stat">
                                <div class="subject-stat-label">班级平均</div>
                                <div class="subject-stat-value">${analysis.class_avg}</div>
                            </div>
                            <div class="subject-stat">
                                <div class="subject-stat-label">预测分数</div>
                                <div class="subject-stat-value">${percentage}分</div>
                                </div>
                            <div class="subject-stat">
                                <div class="subject-stat-label">与平均分差距</div>
                                <div class="subject-stat-value ${vsClassAvgClass}">${vsClassAvgSign}${vsClassAvg}</div>
                            </div>
                        </div>
                        
                        <div class="progress-section">
                            <div class="progress-item">
                                <div class="progress-label">预测分数</div>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill ${levelClass}" style="width: ${Math.min(percentage, 100)}%"></div>
                                </div>
                                <div class="progress-value">${percentage}分</div>
                            </div>
                        </div>
                        
                        <div class="subject-suggestion">
                            <div class="suggestion-label">学习建议：</div>
                            <div class="suggestion-content">${improvementSuggestion}</div>
                                </div>
                    </div>
                </div>
            `;
        });
        
        analysisHTML += `
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        analysisSection.innerHTML = analysisHTML;
    }
    
    /**
     * 生成学习建议
     * @param {Object} studentAnalysis - 学生分析数据
     * @param {Object} classStats - 班级统计数据（保留参数以维持接口兼容性）
     */
    function generateLearningAdvice(studentAnalysis, classStats) {
        const adviceSection = document.getElementById('examLearningAdviceSection');
        const adviceContent = document.getElementById('examLearningAdviceContent');

        if (!adviceSection || !adviceContent) return;

        // 生成学习建议
        const advice = generatePersonalizedAdvice(studentAnalysis);

        // 过滤出需要的建议类型
        const filteredAdvice = advice.filter(item => {
            const title = item.title || '';
            return title.includes('总分') ||
                   title.includes('重点提升') ||
                   title.includes('优势科目') ||
                   title.includes('排名');
        });

        if (filteredAdvice.length === 0) {
            adviceSection.style.display = 'none';
            return;
        }
        
        adviceContent.innerHTML = filteredAdvice.map(item => `
            <div class="advice-item ${item.type || 'neutral'} mb-3">
                <div class="d-flex align-items-start">
                    <div class="advice-icon ${item.type || 'neutral'} me-3">
                        <i class="bi ${item.icon || 'bi-lightbulb'}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="advice-title mb-2">${item.title || '学习建议'}</h6>
                        <p class="advice-description mb-2">${item.description || ''}</p>
                        <p class="advice-suggestions mb-0 text-muted">
                            <strong>建议：</strong>${item.suggestions || ''}
                        </p>
                    </div>
                </div>
            </div>
        `).join('');
        
        adviceSection.style.display = 'block';
    }
    
    // 生成个性化学习建议
    function generatePersonalizedAdvice(studentAnalysis) {
        const advice = [];
        const subjects = ['语文', '数学', '英语', '物理', '化学', '生物'];
        
        // 分析总分表现 - 基于实际成绩判定
        const totalAnalysis = studentAnalysis['总分'];
        const actualTotalScore = totalAnalysis.score;
        const fullTotalMark = totalAnalysis.full_mark;
        const actualTotalPercentage = (actualTotalScore / fullTotalMark) * 100;

        if (actualTotalPercentage >= 90) {
            advice.push({
                type: 'strong',
                icon: 'bi-trophy',
                title: '总分表现优秀',
                description: `您的总分得分为${totalAnalysis.score}分，预测分数为${totalAnalysis.percentage}分，实际得分率为${actualTotalPercentage.toFixed(1)}%，在班级中表现优秀。`,
                suggestions: '继续保持当前的学习状态，可以尝试挑战更高难度的题目。'
            });
        } else if (actualTotalPercentage >= 80) {
            advice.push({
                type: 'neutral',
                icon: 'bi-arrow-up-circle',
                title: '总分表现良好',
                description: `您的总分得分为${totalAnalysis.score}分，预测分数为${totalAnalysis.percentage}分，实际得分率为${actualTotalPercentage.toFixed(1)}%，有提升空间。`,
                suggestions: '重点关注薄弱科目，制定针对性的学习计划。'
            });
        } else if (actualTotalPercentage >= 70) {
            advice.push({
                type: 'neutral',
                icon: 'bi-info-circle',
                title: '总分表现中等',
                description: `您的总分得分为${totalAnalysis.score}分，预测分数为${totalAnalysis.percentage}分，实际得分率为${actualTotalPercentage.toFixed(1)}%，还有较大提升空间。`,
                suggestions: '建议重点复习薄弱科目，制定详细的学习计划。'
            });
        } else if (actualTotalPercentage >= 60) {
            advice.push({
                type: 'weak',
                icon: 'bi-exclamation-triangle',
                title: '总分刚好及格',
                description: `您的总分得分为${totalAnalysis.score}分，预测分数为${totalAnalysis.percentage}分，实际得分率为${actualTotalPercentage.toFixed(1)}%，需要加强学习。`,
                suggestions: '建议制定详细的学习计划，寻求老师或同学的帮助，重点提升基础知识。'
            });
        } else {
            advice.push({
                type: 'weak',
                icon: 'bi-x-circle',
                title: '总分需要大幅提升',
                description: `您的总分得分为${totalAnalysis.score}分，预测分数为${totalAnalysis.percentage}分，实际得分率为${actualTotalPercentage.toFixed(1)}%，急需加强学习。`,
                suggestions: '建议立即制定详细的学习计划，寻求老师和同学的帮助，从基础知识开始系统复习。'
            });
        }
        
        // 分析各科目表现，生成重点提升和优势科目建议 - 基于实际成绩
        const weakSubjects = [];
        const strongSubjects = [];

        subjects.forEach(subject => {
            const analysis = studentAnalysis[subject];
            const actualScore = analysis.score;
            const fullMark = analysis.full_mark;
            const actualPercentage = (actualScore / fullMark) * 100;

            if (actualPercentage < 60) {
                weakSubjects.push(subject);
            } else if (actualPercentage >= 80) {
                strongSubjects.push(subject);
            }
        });
        
        // 生成重点提升科目建议
        if (weakSubjects.length > 0) {
                advice.push({
                    type: 'weak',
                icon: 'bi-lightbulb',
                title: '重点提升科目',
                description: `建议重点提升以下科目：${weakSubjects.join('、')}`,
                suggestions: '制定详细的学习计划，每天安排固定时间复习这些科目。'
                });
        }
        
        // 生成优势科目建议
        if (strongSubjects.length > 0) {
                advice.push({
                    type: 'strong',
                icon: 'bi-check-circle',
                title: '优势科目',
                description: `您的优势科目包括：${strongSubjects.join('、')}`,
                suggestions: '保持优势科目的学习状态，可以尝试更高难度的题目。'
                });
            }
        
        // 分析排名情况
        if (totalAnalysis.rank && totalAnalysis.rank <= 5) {
            advice.push({
                type: 'strong',
                icon: 'bi-award',
                title: '班级排名优秀',
                description: `您在班级中排名第${totalAnalysis.rank}名，表现非常优秀。`,
                suggestions: '继续保持领先优势，可以尝试参加学科竞赛。'
            });
        }
        
        return advice;
    }
    
    // 显示后端返回的学习建议
    function displayLearningAdvice(advice) {
        const adviceSection = document.getElementById('examLearningAdviceSection');
        const adviceContent = document.getElementById('examLearningAdviceContent');
        
        if (!adviceSection || !adviceContent) return;
        
        if (!advice || !Array.isArray(advice) || advice.length === 0) {
            adviceSection.style.display = 'none';
            return;
        }
        
        // 过滤出需要的建议类型
        const filteredAdvice = advice.filter(item => {
            const title = item.title || '';
            return title.includes('总分') || 
                   title.includes('重点提升') || 
                   title.includes('优势科目') ||
                   title.includes('排名');
        });
        
        if (filteredAdvice.length === 0) {
            adviceSection.style.display = 'none';
            return;
        }
        
        adviceContent.innerHTML = filteredAdvice.map(item => `
            <div class="advice-item ${item.type || 'neutral'} mb-3">
                <div class="d-flex align-items-start">
                    <div class="advice-icon ${item.type || 'neutral'} me-3">
                        <i class="bi ${item.icon || 'bi-lightbulb'}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="advice-title mb-2">${item.title || '学习建议'}</h6>
                        <p class="advice-description mb-2">${item.description || ''}</p>
                        <p class="advice-suggestions mb-0 text-muted">
                            <strong>建议：</strong>${item.suggestions || ''}
                        </p>
                    </div>
                </div>
            </div>
        `).join('');
        
        adviceSection.style.display = 'block';
    }
    
    // 显示可视化图表
    function displayCharts(data) {
        const chartsContainer = document.getElementById('chartsContainer');
        const studentName = sessionStorage.getItem('name');
        if (!data.charts) {
            chartsContainer.innerHTML = '<div class="col-12 text-center py-4"><i class="bi bi-exclamation-triangle text-warning fs-3"></i><p class="mt-2 text-muted">未找到图表数据</p></div>';
            return;
        }
        chartsContainer.innerHTML = '';
        
        // 优先用后端返回的学号字段
        let studentId = (data.student_data && (data.student_data.学号 || data.student_data.student_id))
            || data.student_id
            || sessionStorage.getItem('student_id')
            || sessionStorage.getItem('username');
        studentId = String(studentId);
        const examName = document.getElementById('examSelect').value;
        const className = sessionStorage.getItem('class_name');
        
        // 检查是否有该学生的图表
        if (data.charts[`radar_${studentId}`] && data.charts[`bar_${studentId}`] && data.charts[`table_${studentId}`]) {
            const radarCard = createChartCard(
                data.charts[`radar_${studentId}`],
                '个人雷达图',
                'radar',
                { examName, className, studentName, chartType: '雷达图', useServerDownload: true }
            );
            const barCard = createChartCard(
                data.charts[`bar_${studentId}`],
                '个人柱状图',
                'bar',
                { examName, className, studentName, chartType: '柱状图', useServerDownload: true }
            );
            const tableCard = createChartCard(
                data.charts[`table_${studentId}`],
                '个人成绩表',
                'table',
                { examName, className, studentName, chartType: '表格图', useServerDownload: true }
            );
            chartsContainer.appendChild(radarCard);
            chartsContainer.appendChild(barCard);
            chartsContainer.appendChild(tableCard);
        } else if (data.student_info) {
            const top3 = Object.entries(data.student_info)
                .sort((a, b) => a[1].排名 - b[1].排名)
                .slice(0, 3);
            top3.forEach(([stuId, stuInfo]) => {
                if (data.charts[`radar_${stuId}`] && data.charts[`bar_${stuId}`] && data.charts[`table_${stuId}`]) {
                    const radarCard = createChartCard(
                        data.charts[`radar_${stuId}`],
                        `${stuInfo.姓名} 雷达图`,
                        'radar',
                        { examName, className, studentName: stuInfo.姓名, chartType: '雷达图', useServerDownload: true }
                    );
                    const barCard = createChartCard(
                        data.charts[`bar_${stuId}`],
                        `${stuInfo.姓名} 柱状图`,
                        'bar',
                        { examName, className, studentName: stuInfo.姓名, chartType: '柱状图', useServerDownload: true }
                    );
                    const tableCard = createChartCard(
                        data.charts[`table_${stuId}`],
                        `${stuInfo.姓名} 成绩表`,
                        'table',
                        { examName, className, studentName: stuInfo.姓名, chartType: '表格图', useServerDownload: true }
                    );
                    chartsContainer.appendChild(radarCard);
                    chartsContainer.appendChild(barCard);
                    chartsContainer.appendChild(tableCard);
                }
            });
            if (chartsContainer.innerHTML === '') {
                chartsContainer.innerHTML = '<div class="col-12 text-center py-4"><i class="bi bi-info-circle text-muted fs-3"></i><p class="mt-2 text-muted">未找到个人图表数据</p></div>';
            }
        } else {
            chartsContainer.innerHTML = '<div class="col-12 text-center py-4"><i class="bi bi-info-circle text-muted fs-3"></i><p class="mt-2 text-muted">未找到个人图表数据</p></div>';
        }
    }
    
    // 注意：请假相关的事件监听器已在第6模块的 initLeaveApplicationEvents() 函数中统一处理
    
    // 刷新数据按钮
    const refreshDataBtn = document.getElementById('refreshDataBtn');
    if (refreshDataBtn) {
        refreshDataBtn.addEventListener('click', function() {
            loadStudentMessages();
            loadLeaveTable();
            loadGradeData();
        });
    }
    
    // 刷新留言按钮
    const refreshMessageBtn = document.getElementById('refreshMessageBtn');
    if (refreshMessageBtn) {
        refreshMessageBtn.addEventListener('click', function() {
            loadTeacherMessage();
            loadStudentMessages();
        });
    }
    
    // 刷新成绩按钮
    const refreshGradeBtn = document.getElementById('refreshGradeBtn');
    if (refreshGradeBtn) {
        refreshGradeBtn.addEventListener('click', function() {
            loadGradeData();
            // 如果当前显示了具体考试详情，也刷新它
            const examSelect = document.getElementById('examSelect');
            if (examSelect && examSelect.value) {
                const studentId = sessionStorage.getItem('username');
                const grade = sessionStorage.getItem('grade');
                const className = sessionStorage.getItem('class_name');
                loadExamDetail(grade, className, examSelect.value, studentId);
            }
        });
    }
    
    // 提交请假申请表单
    const leaveForm = document.getElementById('leaveForm');
    if (leaveForm) {
        // 初始化请假原因textarea的动态调整
        const reasonTextarea = document.getElementById('leaveReason');
        if (reasonTextarea) {
            // 初始化高度
            autoResizeLeaveReason(reasonTextarea);
            updateReasonCount();
            
            // 添加焦点事件
            reasonTextarea.addEventListener('focus', function() {
                this.style.borderColor = '#409eff';
                this.style.boxShadow = '0 0 0 2px #b3d8ff';
            });
            
            reasonTextarea.addEventListener('blur', function() {
                this.style.borderColor = '#e0e7ef';
                this.style.boxShadow = '0 1px 2px rgba(0,0,0,0.03)';
            });
        }
    }
    
    // 页面加载时拉取请假数据并渲染到表格
    async function loadLeaveTable() {
        const studentId = sessionStorage.getItem('username');
        const grade = sessionStorage.getItem('grade');
        const tbody = document.getElementById('leaveTableBody');
        
        if (!studentId || !grade) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <div class="d-flex flex-column align-items-center">
                            <i class="bi bi-exclamation-triangle text-warning fs-1 mb-2"></i>
                            <p class="text-warning mb-0">无法获取学生信息</p>
                            <small class="text-muted">请重新登录</small>
                        </div>
                    </td>
                </tr>`;
            return;
        }
        
        try {
            const res = await fetch(`/api/leaves?grade=${encodeURIComponent(grade)}&student_id=${encodeURIComponent(studentId)}`);
            
            if (!res.ok) {
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            
            const data = await res.json();
            
            if (!data.success) {
                throw new Error(data.error || '获取请假记录失败');
            }
            
            if (data.data && data.data.length > 0) {
                const today = new Date().toISOString().split('T')[0];
                
                tbody.innerHTML = data.data.map(r => {
                    // 状态颜色和icon
                    let statusHtml;
                    let canCancel = false;
                    
                    if (r.status === '已批准') {
                        statusHtml = '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>已批准</span>';
                        // 检查是否可以销假：已批准且已结束
                        if (r.end_date <= today) {
                            canCancel = true;
                        }
                    } else if (r.status === '已拒绝') {
                        statusHtml = '<span class="badge bg-danger"><i class="bi bi-x-circle me-1"></i>已拒绝</span>';
                    } else if (r.status === '已销假') {
                        statusHtml = '<span class="badge bg-info text-dark"><i class="bi bi-arrow-repeat me-1"></i>已销假</span>';
                    } else {
                        statusHtml = '<span class="badge bg-warning text-dark"><i class="bi bi-hourglass-split me-1"></i>待审批</span>';
                    }
                    
                    // 检查是否需要截断显示
                    const needsTruncation = r.reason.length > 60 || r.reason.includes('\n');
                    const reasonClass = needsTruncation ? 'leave-reason-text truncated' : 'leave-reason-text';
                    const reasonTitle = needsTruncation ? `点击查看完整内容\n${r.reason}` : r.reason;
                    const cellClass = needsTruncation ? 'leave-reason-cell clickable' : 'leave-reason-cell';
                    
                    // 操作按钮
                    let actionHtml = '';
                    if (canCancel) {
                        actionHtml = `<button class="btn btn-sm btn-outline-info" onclick="showCancelLeaveModal(${r.id})">
                            <i class="bi bi-arrow-repeat me-1"></i>销假
                        </button>`;
                    } else if (r.status === '待审批') {
                        actionHtml = '<span class="text-muted small">等待审批</span>';
                    } else if (r.status === '已拒绝') {
                        actionHtml = '<span class="text-muted small">申请被拒绝</span>';
                    } else if (r.status === '已销假') {
                        actionHtml = '<span class="text-muted small">已完成销假</span>';
                    } else {
                        actionHtml = '<span class="text-muted small">-</span>';
                    }
                    
                    return `
                        <tr>
                            <td>
                                <div class="d-flex flex-column">
                                    <span class="fw-semibold">${r.start_date}</span>
                                    <span class="text-muted small">至 ${r.end_date}</span>
                                </div>
                            </td>
                            <td>
                                <div class="${cellClass}" title="${reasonTitle}" ${needsTruncation ? 'onclick="showFullReason(\'' + r.reason.replace(/'/g, '\\\'').replace(/\n/g, '\\n') + '\', \'' + r.name + '\')"' : ''}>
                                    <span class="${reasonClass}">${r.reason}</span>
                                </div>
                            </td>
                            <td>${statusHtml}</td>
                            <td>
                                <div class="d-flex flex-column">
                                    <span class="small">${r.create_time.split(' ')[0]}</span>
                                    <span class="text-muted small">${r.create_time.split(' ')[1]}</span>
                                </div>
                            </td>
                            <td>${actionHtml}</td>
                        </tr>
                    `;
                }).join('');
            } else {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-4">
                            <div class="d-flex flex-column align-items-center">
                                <i class="bi bi-calendar-x text-muted fs-1 mb-2"></i>
                                <p class="text-muted mb-0">暂无请假记录</p>
                                <small class="text-muted">点击"申请请假"按钮可提交申请</small>
                            </div>
                        </td>
                    </tr>
                `;
            }
        } catch (error) {
            console.error('加载请假记录失败:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <div class="d-flex flex-column align-items-center">
                            <i class="bi bi-exclamation-triangle text-danger fs-1 mb-2"></i>
                            <p class="text-danger mb-0">加载失败</p>
                            <small class="text-muted">${error.message || '请检查网络连接后重试'}</small>
                        </div>
                    </td>
                </tr>
            `;
        }
    }
    
    // 提交留言
    const studentMessageForm = document.getElementById('studentMessageForm');
    if (studentMessageForm) {
        studentMessageForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const message = document.getElementById('studentMessageInput').value.trim();
            const isAnonymous = document.getElementById('anonymousCheck').checked;
            if (!message) {
                alert('留言内容不能为空');
                return;
            }
            const grade = sessionStorage.getItem('grade');
            const student_id = sessionStorage.getItem('username');
            const name = sessionStorage.getItem('name');
            const class_name = sessionStorage.getItem('class_name');
            const res = await fetch('/api/student-messages', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    grade, student_id, name, class_name,
                    is_anonymous: isAnonymous,
                    message
                })
            });
            const data = await res.json();
            if (data.success) {
                document.getElementById('studentMessageInput').value = '';
                document.getElementById('anonymousCheck').checked = false;
                updateMsgCount(); // 更新字数计数器
                loadStudentMessages(); // 重新加载留言
            } else {
                alert('留言失败：' + (data.error || '未知错误'));
            }
        });
    }
    
    // 加载老师留言
    async function loadTeacherMessage() {
        const grade = sessionStorage.getItem('grade');
        const student_id = sessionStorage.getItem('username');
        const teacherMessageContent = document.getElementById('teacherMessageContent');
        if (!teacherMessageContent) return;
        try {
            const res = await fetch(`/api/messages?grade=${encodeURIComponent(grade)}&student_id=${encodeURIComponent(student_id)}`);
            const data = await res.json();
            if (data.success && data.data && data.data.length > 0) {
                teacherMessageContent.innerHTML = data.data.map(msg => `
                    <div class="alert alert-primary mb-2">
                        <strong>${msg.teacher_name || '班主任'}</strong>
                        <span class="text-muted ms-2">${msg.create_time || ''}</span>
                        <div class="mt-1">${msg.message}</div>
                    </div>
                `).join('');
            } else {
                teacherMessageContent.innerHTML = '<div class="text-muted">暂无老师留言</div>';
            }
        } catch (e) {
            teacherMessageContent.innerHTML = '<div class="text-danger">加载失败</div>';
        }
    }
    
    // 注意：以下函数已在第5模块中定义，此处不再重复：
    // - loadLeaveTable()
    // - autoResizeLeaveReason()
    // - updateReasonCount()
    // - showCancelLeaveModal()
    // - showFullReason()

    // 初始化加载（仅保留必要的初始化调用）
    loadTeacherMessage();
    loadStudentMessages();
    loadLeaveTable();
    
    // 注意：销假确认事件监听器需要在这里绑定，因为它依赖于 currentCancelLeaveId 变量
    const confirmCancelLeaveBtn = document.getElementById('confirmCancelLeave');
    if (confirmCancelLeaveBtn) {
        confirmCancelLeaveBtn.addEventListener('click', async function() {
            if (!currentCancelLeaveId) {
                showErrorToast('无效的销假操作');
                return;
            }

            const btn = this;
            const originalText = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>处理中...';

            try {
                const data = await cancelLeaveApplication(currentCancelLeaveId);

                if (data.success) {
                    bootstrap.Modal.getInstance(document.getElementById('cancelLeaveModal')).hide();
                    loadLeaveTable();
                    showSuccessToast('销假操作成功！');
                } else {
                    throw new Error(data.error || '销假失败');
                }
            } catch (error) {
                console.error('销假失败:', error);
                showErrorToast(error.message || '网络错误，请重试');
            } finally {
                btn.disabled = false;
                btn.innerHTML = originalText;
                currentCancelLeaveId = null;
            }
        });
    }