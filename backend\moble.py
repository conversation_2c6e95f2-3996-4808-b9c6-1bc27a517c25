"""
成绩预测模型模块
基于学生历史成绩数据进行成绩预测，支持单次考试和多次考试的不同情况
"""

import logging
from typing import Dict, List, Any

from backend.MySQL_to_pythonr import (
    get_exam_db_connection, get_exam_list,
    get_student_scores, get_full_marks
)

# 配置日志
logger = logging.getLogger(__name__)


class GradePredictionModel:
    """成绩预测模型类"""

    def __init__(self, grade: str):
        self.grade = grade
        self.subjects = ['语文', '数学', '英语', '物理', '化学', '生物']

    def predict_student_scores(self, student_id: str, class_name: str,
                               current_exam: str) -> Dict[str, Any]:
        """
        预测学生成绩

        Args:
            student_id: 学生学号
            class_name: 班级名称
            current_exam: 当前考试名称

        Returns:
            包含预测分数的字典
        """
        try:
            # 获取学生历史成绩数据
            historical_data = self._get_student_historical_data(student_id, class_name, current_exam)

            # 获取班级和年级历史数据
            class_data = self._get_class_historical_data(class_name, current_exam)
            grade_data = self._get_grade_historical_data(current_exam)

            # 获取满分信息
            full_marks = get_full_marks(current_exam, self.grade)

            if not historical_data:
                # 单次考试情况：基于班级和年级数据预测
                return self._predict_for_first_exam(student_id, class_name,
                                                    current_exam, class_data,
                                                    grade_data, full_marks)
            else:
                # 多次考试情况：基于历史成绩预测
                return self._predict_for_multiple_exams(student_id, class_name,
                                                        current_exam, historical_data,
                                                        class_data, grade_data, full_marks)

        except Exception as e:
            logger.error(f"预测学生成绩失败: {str(e)}")
            return self._get_fallback_prediction(current_exam, full_marks)

    def _get_student_historical_data(self, student_id: str, class_name: str,
                                     current_exam: str) -> List[Dict[str, Any]]:
        """获取学生历史成绩数据"""
        try:
            exam_list = get_exam_list(self.grade)
            historical_data = []

            for exam in exam_list:
                if exam == current_exam:
                    continue  # 跳过当前考试

                try:
                    # 先尝试新表结构
                    try:
                        results = get_student_scores(self.grade, exam, class_name)
                    except Exception:
                        # 如果失败，尝试旧表结构
                        with get_exam_db_connection(self.grade) as (con, cursor):
                            # 检查表结构
                            cursor.execute(f"SHOW COLUMNS FROM `{exam}`")
                            columns = [col[0] for col in cursor.fetchall()]

                            if 'class_name' in columns:
                                # 新表结构
                                cursor.execute(f"SELECT * FROM `{exam}` WHERE class_name = %s", (class_name,))
                                results = cursor.fetchall()
                            else:
                                # 旧表结构，表名就是班级名
                                cursor.execute(f"SELECT * FROM `{class_name}`")
                                results = cursor.fetchall()

                    for result in results:
                        if len(result) >= 10:
                            # 根据表结构确定字段位置
                            student_id_field = result[1] if 'class_name' in str(result) else result[1]
                            if str(student_id_field).strip() == str(student_id).strip():
                                # 确定字段位置（新表结构有class_name字段，旧表结构没有）
                                base_idx = 3 if 'class_name' in str(result) else 3
                                exam_data = {
                                    'exam_name': exam,
                                    'scores': {
                                        '语文': float(result[base_idx]) if result[base_idx] is not None else 0,
                                        '数学': float(result[base_idx + 1]) if result[base_idx + 1] is not None else 0,
                                        '英语': float(result[base_idx + 2]) if result[base_idx + 2] is not None else 0,
                                        '物理': float(result[base_idx + 3]) if result[base_idx + 3] is not None else 0,
                                        '化学': float(result[base_idx + 4]) if result[base_idx + 4] is not None else 0,
                                        '生物': float(result[base_idx + 5]) if result[base_idx + 5] is not None else 0,
                                    },
                                    'total_score': float(result[base_idx + 6]) if result[
                                                                                      base_idx + 6] is not None else 0,
                                    'class_rank': int(result[0]) if result[0] is not None else None
                                }
                                historical_data.append(exam_data)
                                break
                except Exception as e:
                    logger.warning(f"获取考试 {exam} 数据失败: {str(e)}")
                    continue

            return historical_data

        except Exception as e:
            logger.error(f"获取学生历史数据失败: {str(e)}")
            return []

    def _get_class_historical_data(self, class_name: str, current_exam: str) -> List[Dict[str, Any]]:
        """获取班级历史数据"""
        try:
            exam_list = get_exam_list(self.grade)
            class_data = []

            for exam in exam_list:
                if exam == current_exam:
                    continue

                try:
                    results = get_student_scores(self.grade, exam, class_name)
                    if results:
                        # 计算班级平均分
                        scores_by_subject = {subject: [] for subject in self.subjects}
                        total_scores = []

                        for result in results:
                            if len(result) >= 10:
                                for i, subject in enumerate(self.subjects, 3):
                                    if result[i] is not None:
                                        scores_by_subject[subject].append(float(result[i]))
                                if result[9] is not None:
                                    total_scores.append(float(result[9]))

                        # 计算平均分
                        avg_scores = {}
                        for subject in self.subjects:
                            if scores_by_subject[subject]:
                                avg_scores[subject] = sum(scores_by_subject[subject]) / len(scores_by_subject[subject])
                            else:
                                avg_scores[subject] = 0

                        avg_total = sum(total_scores) / len(total_scores) if total_scores else 0

                        class_data.append({
                            'exam_name': exam,
                            'avg_scores': avg_scores,
                            'avg_total': avg_total,
                            'student_count': len(results)
                        })

                except Exception as e:
                    logger.warning(f"获取班级考试 {exam} 数据失败: {str(e)}")
                    continue

            return class_data

        except Exception as e:
            logger.error(f"获取班级历史数据失败: {str(e)}")
            return []

    def _get_grade_historical_data(self, current_exam: str) -> List[Dict[str, Any]]:
        """获取年级历史数据"""
        try:
            exam_list = get_exam_list(self.grade)
            grade_data = []

            for exam in exam_list:
                if exam == current_exam:
                    continue

                try:
                    # 获取年级所有班级数据
                    with get_exam_db_connection(self.grade) as (con, cursor):
                        cursor.execute(f"SELECT * FROM `{exam}`")
                        results = cursor.fetchall()

                        if results:
                            # 计算年级平均分
                            scores_by_subject = {subject: [] for subject in self.subjects}
                            total_scores = []

                            for result in results:
                                if len(result) >= 10:
                                    for i, subject in enumerate(self.subjects, 3):
                                        if result[i] is not None:
                                            scores_by_subject[subject].append(float(result[i]))
                                    if result[9] is not None:
                                        total_scores.append(float(result[9]))

                            # 计算平均分
                            avg_scores = {}
                            for subject in self.subjects:
                                if scores_by_subject[subject]:
                                    avg_scores[subject] = sum(scores_by_subject[subject]) / len(
                                        scores_by_subject[subject])
                                else:
                                    avg_scores[subject] = 0

                            avg_total = sum(total_scores) / len(total_scores) if total_scores else 0

                            grade_data.append({
                                'exam_name': exam,
                                'avg_scores': avg_scores,
                                'avg_total': avg_total,
                                'student_count': len(results)
                            })

                except Exception as e:
                    logger.warning(f"获取年级考试 {exam} 数据失败: {str(e)}")
                    continue

            return grade_data

        except Exception as e:
            logger.error(f"获取年级历史数据失败: {str(e)}")
            return []

    def _predict_for_first_exam(self, student_id: str, class_name: str, current_exam: str,
                                class_data: List[Dict], grade_data: List[Dict],
                                full_marks: Dict[str, float]) -> Dict[str, Any]:
        """单次考试情况的预测（基于班级和年级数据）"""
        try:
            # 获取当前考试的班级排名和年级排名
            current_rank_info = self._get_current_exam_rank_info(student_id, class_name, current_exam)

            if not current_rank_info:
                return self._get_fallback_prediction(current_exam, full_marks)

            class_rank = current_rank_info.get('class_rank')
            grade_rank = current_rank_info.get('grade_rank')
            class_size = current_rank_info.get('class_size', 50)
            grade_size = current_rank_info.get('grade_size', 500)

            # 计算排名百分位
            class_percentile = (class_size - class_rank + 1) / class_size if class_rank else 0.5
            grade_percentile = (grade_size - grade_rank + 1) / grade_size if grade_rank else 0.5

            # 基于历史班级和年级数据预测
            predicted_scores = {}

            for subject in self.subjects:
                # 获取历史班级平均分
                class_avg_history = [data['avg_scores'].get(subject, 0) for data in class_data if
                                     data['avg_scores'].get(subject, 0) > 0]
                grade_avg_history = [data['avg_scores'].get(subject, 0) for data in grade_data if
                                     data['avg_scores'].get(subject, 0) > 0]

                if class_avg_history:
                    class_avg = sum(class_avg_history) / len(class_avg_history)
                else:
                    class_avg = full_marks.get(subject, 100) * 0.7  # 默认70%

                if grade_avg_history:
                    grade_avg = sum(grade_avg_history) / len(grade_avg_history)
                else:
                    grade_avg = full_marks.get(subject, 100) * 0.65  # 默认65%

                # 基于排名预测分数
                # 使用正态分布假设，排名越高分数越高
                base_score = (class_avg * 0.7 + grade_avg * 0.3)  # 班级权重更高

                # 根据排名调整分数
                rank_factor = self._calculate_rank_factor(class_percentile, grade_percentile)
                predicted_score = base_score * rank_factor

                # 确保分数在合理范围内
                max_score = full_marks.get(subject, 100)
                predicted_score = max(0, min(predicted_score, max_score))

                predicted_scores[subject] = round(predicted_score, 1)

            # 计算总分
            total_predicted = sum(predicted_scores.values())
            predicted_scores['总分'] = round(total_predicted, 1)

            return {
                'predicted_scores': predicted_scores,
                'confidence': 0.75,  # 单次考试预测置信度较低
                'prediction_type': 'first_exam',
                'factors': {
                    'class_rank': class_rank,
                    'grade_rank': grade_rank,
                    'class_percentile': round(class_percentile * 100, 1),
                    'grade_percentile': round(grade_percentile * 100, 1)
                }
            }

        except Exception as e:
            logger.error(f"单次考试预测失败: {str(e)}")
            return self._get_fallback_prediction(current_exam, full_marks)

    def _predict_for_multiple_exams(self, student_id: str, class_name: str, current_exam: str,
                                    historical_data: List[Dict], class_data: List[Dict],
                                    grade_data: List[Dict], full_marks: Dict[str, float]) -> Dict[str, Any]:
        """多次考试情况的预测（基于历史成绩）"""
        try:
            if len(historical_data) < 1:
                return self._predict_for_first_exam(student_id, class_name, current_exam,
                                                    class_data, grade_data, full_marks)

            # 分析学生历史成绩趋势
            predicted_scores = {}

            for subject in self.subjects:
                subject_scores = [data['scores'].get(subject, 0) for data in historical_data]
                subject_scores = [score for score in subject_scores if score > 0]  # 过滤无效分数

                if not subject_scores:
                    # 如果该科目没有历史数据，使用班级平均分预测
                    predicted_scores[subject] = self._predict_subject_by_average(subject, class_data, grade_data,
                                                                                 full_marks)
                    continue

                # 计算趋势和稳定性
                trend = self._calculate_trend(subject_scores)
                stability = self._calculate_stability(subject_scores)

                # 基于历史成绩预测
                if len(subject_scores) == 1:
                    # 只有一次历史成绩
                    base_score = subject_scores[0]
                    predicted_score = base_score + trend * 0.5  # 轻微调整
                else:
                    # 多次历史成绩，使用加权平均
                    weights = self._get_time_weights(len(subject_scores))
                    weighted_avg = sum(score * weight for score, weight in zip(subject_scores, weights))
                    predicted_score = weighted_avg + trend

                # 考虑偏科情况的调整
                bias_adjustment = self._calculate_bias_adjustment(subject, subject_scores, historical_data)
                predicted_score += bias_adjustment

                # 确保分数在合理范围内
                max_score = full_marks.get(subject, 100)
                predicted_score = max(0, min(predicted_score, max_score))

                predicted_scores[subject] = round(predicted_score, 1)

            # 计算总分
            total_predicted = sum(predicted_scores.values())
            predicted_scores['总分'] = round(total_predicted, 1)

            # 计算置信度
            confidence = self._calculate_confidence(historical_data, predicted_scores)

            return {
                'predicted_scores': predicted_scores,
                'confidence': confidence,
                'prediction_type': 'multiple_exams',
                'historical_count': len(historical_data),
                'factors': {
                    'trend_analysis': self._analyze_overall_trend(historical_data),
                    'stability_score': self._calculate_overall_stability(historical_data),
                    'bias_detected': self._detect_subject_bias(historical_data)
                }
            }

        except Exception as e:
            logger.error(f"多次考试预测失败: {str(e)}")
            return self._get_fallback_prediction(current_exam, full_marks)

    def _get_current_exam_rank_info(self, student_id: str, class_name: str,
                                    current_exam: str) -> Dict[str, Any]:
        """获取当前考试的排名信息"""
        try:
            with get_exam_db_connection(self.grade) as (_, cursor):
                # 获取学生在当前考试中的排名
                cursor.execute(
                    f"SELECT class_rank, grade_rank FROM `{current_exam}` WHERE student_id = %s AND class_name = %s",
                    (student_id, class_name))
                result = cursor.fetchone()

                if not result:
                    return {}

                class_rank, grade_rank = result

                # 获取班级总人数
                cursor.execute(f"SELECT COUNT(*) FROM `{current_exam}` WHERE class_name = %s", (class_name,))
                class_size = cursor.fetchone()[0]

                # 获取年级总人数
                cursor.execute(f"SELECT COUNT(*) FROM `{current_exam}`")
                grade_size = cursor.fetchone()[0]

                return {
                    'class_rank': int(class_rank) if class_rank else None,
                    'grade_rank': int(grade_rank) if grade_rank else None,
                    'class_size': int(class_size),
                    'grade_size': int(grade_size)
                }

        except Exception as e:
            logger.error(f"获取排名信息失败: {str(e)}")
            return {}

    def _calculate_rank_factor(self, class_percentile: float, grade_percentile: float) -> float:
        """根据排名百分位计算分数调整因子"""
        # 综合班级和年级排名，班级权重更高
        combined_percentile = class_percentile * 0.7 + grade_percentile * 0.3

        # 使用sigmoid函数将百分位转换为分数因子
        # 排名越高（百分位越大），分数因子越大
        if combined_percentile >= 0.9:
            return 1.15  # 前10%，分数较高
        elif combined_percentile >= 0.7:
            return 1.05  # 前30%，分数略高
        elif combined_percentile >= 0.3:
            return 1.0  # 中等，正常分数
        elif combined_percentile >= 0.1:
            return 0.9  # 后30%，分数略低
        else:
            return 0.8  # 后10%，分数较低

    def _calculate_trend(self, scores: List[float]) -> float:
        """计算成绩趋势"""
        if len(scores) < 2:
            return 0

        # 简单线性趋势计算
        n = len(scores)
        x = list(range(n))

        # 计算斜率
        x_mean = sum(x) / n
        y_mean = sum(scores) / n

        numerator = sum((x[i] - x_mean) * (scores[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))

        if denominator == 0:
            return 0

        slope = numerator / denominator
        return slope * 2  # 放大趋势影响

    def _calculate_stability(self, scores: List[float]) -> float:
        """计算成绩稳定性（标准差的倒数）"""
        if len(scores) < 2:
            return 1.0

        mean_score = sum(scores) / len(scores)
        variance = sum((score - mean_score) ** 2 for score in scores) / len(scores)
        std_dev = variance ** 0.5

        # 标准差越小，稳定性越高
        if std_dev == 0:
            return 1.0
        else:
            return 1.0 / (1.0 + std_dev / mean_score)  # 归一化稳定性

    def _get_time_weights(self, count: int) -> List[float]:
        """获取时间权重，越近的考试权重越大"""
        if count == 1:
            return [1.0]

        # 指数衰减权重
        weights = []
        total = 0
        for i in range(count):
            weight = 0.8 ** (count - 1 - i)  # 最近的权重最大
            weights.append(weight)
            total += weight

        # 归一化
        return [w / total for w in weights]

    def _calculate_bias_adjustment(self, subject: str, subject_scores: List[float],
                                   historical_data: List[Dict]) -> float:
        """计算偏科调整"""
        if len(historical_data) < 2:
            return 0

        # 计算该科目相对于总分的表现
        subject_percentages = []
        for data in historical_data:
            total_score = data.get('total_score', 0)
            subject_score = data['scores'].get(subject, 0)
            if total_score > 0:
                percentage = subject_score / total_score
                subject_percentages.append(percentage)

        if not subject_percentages:
            return 0

        # 计算平均占比
        avg_percentage = sum(subject_percentages) / len(subject_percentages)
        expected_percentage = 1.0 / len(self.subjects)  # 期望占比（假设各科满分相同）

        # 如果该科目占比明显偏低或偏高，进行调整
        bias_ratio = avg_percentage / expected_percentage

        if bias_ratio < 0.8:  # 偏科较弱
            return -2.0  # 降低预测分数
        elif bias_ratio > 1.2:  # 偏科较强
            return 2.0  # 提高预测分数
        else:
            return 0

    def _predict_subject_by_average(self, subject: str, class_data: List[Dict],
                                    grade_data: List[Dict], full_marks: Dict[str, float]) -> float:
        """基于班级和年级平均分预测科目分数"""
        # 获取历史班级平均分
        class_avg_history = [data['avg_scores'].get(subject, 0) for data in class_data if
                             data['avg_scores'].get(subject, 0) > 0]
        grade_avg_history = [data['avg_scores'].get(subject, 0) for data in grade_data if
                             data['avg_scores'].get(subject, 0) > 0]

        if class_avg_history:
            class_avg = sum(class_avg_history) / len(class_avg_history)
        else:
            class_avg = full_marks.get(subject, 100) * 0.7

        if grade_avg_history:
            grade_avg = sum(grade_avg_history) / len(grade_avg_history)
        else:
            grade_avg = full_marks.get(subject, 100) * 0.65

        # 班级平均分权重更高
        predicted_score = class_avg * 0.7 + grade_avg * 0.3
        max_score = full_marks.get(subject, 100)
        return max(0, min(predicted_score, max_score))

    def _calculate_confidence(self, historical_data: List[Dict],
                              predicted_scores: Dict[str, float]) -> float:
        """计算预测置信度"""
        if not historical_data:
            return 0.6

        # 基于历史数据的稳定性计算置信度
        total_scores = [data['total_score'] for data in historical_data]
        stability = self._calculate_stability(total_scores)

        # 基于数据量调整置信度
        data_factor = min(len(historical_data) / 5.0, 1.0)  # 5次考试达到最高置信度

        # 综合置信度
        confidence = 0.6 + stability * 0.3 + data_factor * 0.1
        return min(confidence, 0.95)  # 最高95%置信度

    def _analyze_overall_trend(self, historical_data: List[Dict]) -> str:
        """分析整体成绩趋势"""
        if len(historical_data) < 2:
            return "数据不足"

        total_scores = [data['total_score'] for data in historical_data]
        trend = self._calculate_trend(total_scores)

        if trend > 5:
            return "显著上升"
        elif trend > 2:
            return "轻微上升"
        elif trend > -2:
            return "基本稳定"
        elif trend > -5:
            return "轻微下降"
        else:
            return "显著下降"

    def _calculate_overall_stability(self, historical_data: List[Dict]) -> float:
        """计算整体稳定性"""
        if not historical_data:
            return 0.5

        total_scores = [data['total_score'] for data in historical_data]
        return self._calculate_stability(total_scores)

    def _detect_subject_bias(self, historical_data: List[Dict]) -> List[str]:
        """检测偏科情况"""
        if len(historical_data) < 2:
            return []

        bias_subjects = []

        for subject in self.subjects:
            subject_scores = [data['scores'].get(subject, 0) for data in historical_data]
            total_scores = [data['total_score'] for data in historical_data]

            if not subject_scores or not total_scores:
                continue

            # 计算该科目占总分的平均比例
            percentages = []
            for i in range(len(subject_scores)):
                if total_scores[i] > 0:
                    percentages.append(subject_scores[i] / total_scores[i])

            if not percentages:
                continue

            avg_percentage = sum(percentages) / len(percentages)
            expected_percentage = 1.0 / len(self.subjects)

            # 检测偏科
            if avg_percentage < expected_percentage * 0.8:
                bias_subjects.append(f"{subject}(弱)")
            elif avg_percentage > expected_percentage * 1.2:
                bias_subjects.append(f"{subject}(强)")

        return bias_subjects

    def _get_fallback_prediction(self, current_exam: str, full_marks: Dict[str, float]) -> Dict[str, Any]:
        """备用预测方案"""
        predicted_scores = {}

        # 使用默认分数（满分的70%）
        for subject in self.subjects:
            max_score = full_marks.get(subject, 100)
            predicted_scores[subject] = round(max_score * 0.7, 1)

        predicted_scores['总分'] = round(sum(predicted_scores.values()), 1)

        return {
            'predicted_scores': predicted_scores,
            'confidence': 0.5,
            'prediction_type': 'fallback',
            'factors': {
                'note': '使用默认预测方案'
            }
        }


# 创建预测模型实例的工厂函数
def create_prediction_model(grade: str) -> GradePredictionModel:
    """创建预测模型实例"""
    return GradePredictionModel(grade)


# 主要的预测接口函数
def predict_student_performance(grade: str, student_id: str, class_name: str,
                                current_exam: str) -> Dict[str, Any]:
    """
    预测学生成绩的主要接口函数

    Args:
        grade: 年级
        student_id: 学生学号
        class_name: 班级名称
        current_exam: 当前考试名称

    Returns:
        预测结果字典
    """
    try:
        model = create_prediction_model(grade)
        return model.predict_student_scores(student_id, class_name, current_exam)
    except Exception as e:
        logger.error(f"预测学生成绩失败: {str(e)}")
        # 返回默认预测
        try:
            full_marks = get_full_marks(current_exam, grade)
            return GradePredictionModel(grade)._get_fallback_prediction(current_exam, full_marks)
        except:
            return {
                'predicted_scores': {subject: 70.0 for subject in
                                     ['语文', '数学', '英语', '物理', '化学', '生物', '总分']},
                'confidence': 0.5,
                'prediction_type': 'error_fallback',
                'factors': {'error': str(e)}
            }
