<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生管理分析系统</title>
    <link rel="stylesheet" href="/css/public.css">
    <link rel="stylesheet" href="/css/top.css">
    <link rel="stylesheet" href="/css/teacher_analyze.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
    .sidebar-sticky {
        position: sticky;
        top: 80px; /* 适配你的导航栏高度 */
        z-index: 100;
    }

    @media (max-width: 991.98px) {
        .sidebar-sticky {
            position: static;
            top: auto;
        }
    }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="#">
                <i class="bi bi-bar-chart-line me-2"></i>
                <span>学生管理分析系统</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#"><i class="bi bi-house-door me-1"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="helpBtn"><i class="bi bi-question-circle me-1"></i> 帮助</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="exportDataBtn"><i class="bi bi-download me-1"></i> 导出数据</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right me-1"></i> 退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        <!-- 欢迎信息 -->
        <div class="welcome-section text-center mb-5 animate__animated animate__fadeIn">
            <h1 class="display-5 fw-bold text-primary">学生管理分析系统</h1>
            <p class="lead text-muted">选择对应功能，管理学生或者分析学生成绩</p>
        </div>

        <div class="row g-4">
            <!-- 左侧控制面板 -->
            <div class="col-lg-3 d-flex flex-column">
                <!-- 学生管理面板（仅班主任可见，不固定） -->
                <div id="studentManagePanel" class="card shadow-sm animate__animated animate__fadeInLeft mb-3" style="display: none;">
                    <div class="card-header bg-light">
                        <a href="/html/teacher_manage.html" class="btn btn-link p-0 d-flex align-items-center text-decoration-none w-100" type="button">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-person-gear me-2"></i> 学生管理面板
                            </h5>
                            <i class="bi bi-chevron-right ms-auto"></i>
                        </a>
                    </div>
                </div>
                <!-- sticky部分：成绩分析面板+学生列表面板 -->
                <div class="sidebar-sticky">
                    <!-- 成绩分析面板 -->
                    <div class="card shadow-sm animate__animated animate__fadeInLeft">
                        <div class="card-header bg-light">
                            <button class="btn btn-link p-0 d-flex align-items-center text-decoration-none w-100" type="button" data-bs-toggle="collapse" data-bs-target="#analysisPanelCollapse" aria-expanded="true" aria-controls="analysisPanelCollapse">
                                <h5 class="card-title mb-0 d-flex align-items-center">
                                    <i class="bi bi-sliders me-2"></i> 成绩分析面板
                                </h5>
                                <i class="bi bi-chevron-down ms-auto"></i>
                            </button>
                        </div>
                        <div id="analysisPanelCollapse" class="collapse show">
                            <div class="card-body p-3">
                                <form id="analysis-form">
                                    <div class="mb-3">
                                        <label for="examSelect" class="form-label d-flex align-items-center">
                                            <i class="bi bi-calendar-event me-1"></i> 考试名称
                                        </label>
                                        <select id="examSelect" class="form-select">
                                            <option value="">请选择考试</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="classSelect" class="form-label d-flex align-items-center">
                                            <i class="bi bi-people me-1"></i> 班级
                                        </label>
                                        <select id="classSelect" class="form-select">
                                            <option value="">请选择班级</option>
                                        </select>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="button" id="generateBtn" class="btn btn-primary">
                                            <i class="bi bi-graph-up me-1"></i> 生成分析图表
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 学生列表面板 -->
                    <div class="card shadow-sm mt-3 animate__animated animate__fadeInLeft">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-people-fill me-2"></i> 学生列表
                            </h5>
                            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#studentListCollapse">
                                <i class="bi bi-chevron-down"></i>
                            </button>
                        </div>
                        <div class="collapse show" id="studentListCollapse">
                            <div class="card-body p-3">
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control form-control-sm" id="studentListSearch" placeholder="搜索学生...">
                                    <button class="btn btn-outline-secondary btn-sm" type="button">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                                <div id="studentList" class="list-group list-group-flush" style="max-height: calc(100vh - 400px); overflow-y: auto;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧图表展示区 -->
            <div class="col-lg-9">
                <div id="error-container" class="alert alert-danger d-none animate__animated animate__fadeIn" role="alert"></div>

                <!-- 总体分析卡片 -->
                <div class="card shadow-sm mb-4 animate__animated animate__fadeInRight">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0 d-flex align-items-center">
                            <i class="bi bi-bar-chart me-2"></i> 班级总体成绩分析
                        </h5>
                        <div class="btn-group">
                            <button type="button" id="refreshOverallBtn" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="刷新数据">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="classStatsInfo" class="mb-3"></div>
                        <div id="chartsContainer" class="row g-4">
                            <div class="col-12 text-center py-5">
                                <p>查看全部学生的成绩，请点击导出数据，导出excel表或者csv表</p>
                                <i class="bi bi-info-circle text-muted fs-3"></i>
                                <p class="mt-2 text-muted">选择考试和班级后显示班级总体分析</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 个人分析卡片 -->
                <div class="card shadow-sm animate__animated animate__fadeInRight">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0 d-flex align-items-center">
                            <i class="bi bi-person-lines-fill me-2"></i> 学生个人成绩分析
                        </h5>
                        <div class="input-group" style="width: 300px;">
                            <input type="text" id="studentNameInput" class="form-control form-control-sm" placeholder="输入学生姓名搜索...">
                            <button class="btn btn-primary btn-sm" type="button" id="searchStudentBtn">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="individual-charts" class="row g-4">
                            <div class="col-12 text-center py-5">
                                <i class="bi bi-info-circle text-muted fs-3"></i>
                                <p class="mt-2 text-muted">选择学生后显示个人成绩分析图表</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-question-circle me-2"></i>使用帮助</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">基本操作</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="bi bi-1-circle me-2"></i>选择考试和班级</li>
                                <li class="mb-2"><i class="bi bi-2-circle me-2"></i>点击生成分析图表</li>
                                <li class="mb-2"><i class="bi bi-3-circle me-2"></i>查看总体分析结果</li>
                                <li class="mb-2"><i class="bi bi-4-circle me-2"></i>搜索查看个人分析</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">功能说明</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>支持多考试数据对比</li>
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>提供多种图表展示</li>
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>支持数据导出功能</li>
                                <li class="mb-2"><i class="bi bi-check-circle me-2"></i>支持打印分析报告</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="position-fixed bottom-0 end-0 p-3 z-50">
        <div id="loadingToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="bi bi-info-circle me-2"></i>
                <strong class="me-auto">系统提示</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                正在生成分析图表，请稍候...
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script src="/js/public.js"></script>
    <script src="/js/top.js"></script>
    <script src="/js/teacher_analyze.js"></script>
</body>
</html>