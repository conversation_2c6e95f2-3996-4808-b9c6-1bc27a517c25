/**
 * ========================================
 * 教师管理页面功能模块 (teacher_manage.js)
 * ========================================
 * 专门处理教师管理页面的所有功能：
 * 1. 页面初始化和权限验证
 * 2. 班级数据管理和切换
 * 3. 学生信息管理
 * 4. 请假审批管理
 * 5. 留言管理功能
 * 6. 搜索和筛选功能
 *
 * 依赖：public.js（通用工具函数）、top.js（导航和认证）
 * ========================================
 */

// ========================================
// 1. 页面初始化和权限验证模块
// ========================================

// 全局状态变量
let currentClass = '';
let currentLeaveClassName = '';

/**
 * 页面初始化入口函数
 */
document.addEventListener('DOMContentLoaded', async () => {
    await initializeTeacherManagePage();
});

/**
 * 初始化教师管理页面
 */
async function initializeTeacherManagePage() {
    // 验证班主任权限
    if (!validateHeadTeacherAccess()) {
        return;
    }

    // 加载教师班级数据
    await loadTeacherClasses();

    // 初始化页面组件
    initializePageComponents();

    // 绑定事件监听器
    bindEventListeners();
}

/**
 * 验证班主任访问权限
 * @returns {boolean} 是否有权限访问
 */
function validateHeadTeacherAccess() {
    const isHeadTeacher = sessionStorage.getItem('is_head_teacher') === 'true';

    if (!isHeadTeacher) {
        Swal.fire({
            title: '权限不足',
            text: '只有班主任才能访问学生管理页面',
            icon: 'warning',
            confirmButtonText: '跳转到分析页面'
        }).then(() => {
            window.location.href = '/html/teacher_analyze.html';
        });
        return false;
    }

    return true;
}

/**
 * 初始化页面组件
 */
function initializePageComponents() {
    // 初始化标签页切换
    initializeTabSwitching();
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 绑定请假审批表格的搜索
    const leaveSearchInput = document.getElementById('leaveStudentSearchInput');
    if (leaveSearchInput) {
        leaveSearchInput.addEventListener('input', function() {
            filterLeaveTableRows(this.value);
        });
    }

    // 绑定班级切换事件
    bindClassSwitchEvents();
}

/**
 * 加载教师班级数据
 */
async function loadTeacherClasses() {
    const grade = sessionStorage.getItem('grade');
    const teacherUsername = sessionStorage.getItem('username');

    if (!grade || !teacherUsername) {
        loadClasses('');
        return;
    }

    try {
        const response = await fetch(`/api/grade-classes?grade=${encodeURIComponent(grade)}&teacher_username=${encodeURIComponent(teacherUsername)}`);
        const data = await response.json();

        if (data.error) {
            console.error('获取教师班级失败:', data.error);
            loadClasses('');
            return;
        }

        // 将班级列表转换为逗号分隔的字符串
        const teacherClasses = data.classes ? data.classes.join(',') : '';
        loadClasses(teacherClasses);
    } catch (error) {
        console.error('获取教师班级失败:', error);
        loadClasses('');
    }
}

// ========================================
// 6. 搜索和筛选功能模块
// ========================================

/**
 * 过滤请假表格行
 * @param {string} keyword - 搜索关键词
 */
function filterLeaveTableRows(keyword) {
    const tbody = document.getElementById('leaveApproveTableBody');
    const rows = tbody.querySelectorAll('tr.leave-row');
    
    if (!keyword.trim()) {
        // 如果搜索框为空，显示所有行
        rows.forEach(row => {
            row.style.display = '';
            row.classList.remove('filtered-out');
        });
        return;
    }
    
    const searchTerm = keyword.toLowerCase().trim();
    let visibleCount = 0;
    
    rows.forEach(row => {
        const studentName = row.querySelector('td:first-child .fw-semibold')?.textContent?.toLowerCase() || '';
        const studentId = row.querySelector('td:first-child .text-muted')?.textContent?.toLowerCase() || '';
        const reason = row.querySelector('.leave-reason-text')?.textContent?.toLowerCase() || '';
        
        const isMatch = studentName.includes(searchTerm) || 
                       studentId.includes(searchTerm) || 
                       reason.includes(searchTerm);
        
        if (isMatch) {
            row.style.display = '';
            row.classList.remove('filtered-out');
            visibleCount++;
            
            // 高亮匹配的文本
            highlightSearchTerm(row, searchTerm);
        } else {
            row.style.display = 'none';
            row.classList.add('filtered-out');
        }
    });
    
    // 如果没有匹配结果，显示提示
    if (visibleCount === 0) {
        const noResultsRow = tbody.querySelector('tr.no-results');
        if (!noResultsRow) {
            const newRow = document.createElement('tr');
            newRow.className = 'no-results';
            newRow.innerHTML = `
                <td colspan="6" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                        <i class="bi bi-search text-muted fs-1 mb-2"></i>
                        <p class="text-muted mb-1">未找到匹配的请假记录</p>
                        <small class="text-muted">请尝试其他关键词</small>
                    </div>
                </td>
            `;
            tbody.appendChild(newRow);
        }
    } else {
        // 移除无结果提示
        const noResultsRow = tbody.querySelector('tr.no-results');
        if (noResultsRow) {
            noResultsRow.remove();
        }
    }
}

// 高亮搜索关键词
function highlightSearchTerm(row, searchTerm) {
    // 移除之前的高亮
    row.querySelectorAll('.highlight').forEach(el => {
        const parent = el.parentNode;
        parent.replaceChild(document.createTextNode(el.textContent), el);
        parent.normalize();
    });
    
    // 高亮学生姓名
    const nameElement = row.querySelector('td:first-child .fw-semibold');
    if (nameElement && nameElement.textContent.toLowerCase().includes(searchTerm)) {
        highlightTextInElement(nameElement, searchTerm);
    }
    
    // 高亮学号
    const idElement = row.querySelector('td:first-child .text-muted');
    if (idElement && idElement.textContent.toLowerCase().includes(searchTerm)) {
        highlightTextInElement(idElement, searchTerm);
    }
    
    // 高亮请假原因
    const reasonElement = row.querySelector('.leave-reason-text');
    if (reasonElement && reasonElement.textContent.toLowerCase().includes(searchTerm)) {
        highlightTextInElement(reasonElement, searchTerm);
    }
}

// 在元素中高亮文本
function highlightTextInElement(element, searchTerm) {
    const text = element.textContent;
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const highlightedText = text.replace(regex, '<span class="highlight bg-warning text-dark px-1 rounded">$1</span>');
    element.innerHTML = highlightedText;
}

// tab切换逻辑
function initializeTabSwitching() {
    const infoBtn = document.getElementById('studentInfoTabBtn');
    const leaveBtn = document.getElementById('studentLeaveTabBtn');
    const infoPanel = document.getElementById('studentInfoPanel');
    const leavePanel = document.getElementById('studentLeavePanel');
    if (infoBtn && leaveBtn && infoPanel && leavePanel) {
        infoBtn.addEventListener('click', () => {
            infoPanel.style.display = '';
            leavePanel.style.display = 'none';
            infoBtn.classList.add('active');
            leaveBtn.classList.remove('active');
        });
        leaveBtn.addEventListener('click', () => {
            infoPanel.style.display = 'none';
            leavePanel.style.display = '';
            leaveBtn.classList.add('active');
            infoBtn.classList.remove('active');
        });
    }
}

// 设置请假审批卡片右上角班级展示
function setLeaveClassDisplay(className) {
    const badge = document.getElementById('leaveClassDisplay');
    if (badge) badge.textContent = className || '';
}

// ========================================
// 4. 请假审批管理模块
// ========================================

/**
 * 加载请假审批表格
 */
async function loadLeaveApproveTable() {
    const leaveApproveTableBody = document.getElementById('leaveApproveTableBody');
    const grade = sessionStorage.getItem('grade');
    const className = currentLeaveClassName;
    setLeaveClassDisplay(className);
    
    if (!grade) {
        leaveApproveTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">无法获取年级信息，请重新登录</td></tr>';
        return;
    }
    
    if (!className) {
        leaveApproveTableBody.innerHTML = '<tr><td colspan="6" class="text-muted">请选择班级</td></tr>';
        return;
    }
    
    leaveApproveTableBody.innerHTML = '<tr><td colspan="6" class="text-center"><span class="spinner-border spinner-border-sm"></span> 加载中...</td></tr>';
    
    try {
        const res = await fetch(`/api/leaves?grade=${encodeURIComponent(grade)}&class_name=${encodeURIComponent(className)}`);
        
        if (!res.ok) {
            throw new Error(`HTTP ${res.status}: ${res.statusText}`);
        }
        
        const data = await res.json();
        
        if (!data.success) {
            throw new Error(data.error || '获取请假记录失败');
        }
        
        if (data.data && data.data.length > 0) {
            // 更新统计信息
            updateLeaveStats(data.data);
            
            leaveApproveTableBody.innerHTML = data.data.map(r => {
                let statusHtml = '';
                if (r.status === '已批准') statusHtml = '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>已批准</span>';
                else if (r.status === '已拒绝') statusHtml = '<span class="badge bg-danger"><i class="bi bi-x-circle me-1"></i>已拒绝</span>';
                else if (r.status === '已销假') statusHtml = '<span class="badge bg-info text-dark"><i class="bi bi-arrow-repeat me-1"></i>已销假</span>';
                else statusHtml = '<span class="badge bg-warning text-dark"><i class="bi bi-hourglass-split me-1"></i>待审批</span>';
                
                // 检查是否需要截断显示
                const needsTruncation = r.reason.length > 60 || r.reason.includes('\n');
                const reasonClass = needsTruncation ? 'leave-reason-text truncated' : 'leave-reason-text';
                const reasonTitle = needsTruncation ? `点击查看完整内容\n${r.reason}` : r.reason;
                const cellClass = needsTruncation ? 'leave-reason-cell clickable' : 'leave-reason-cell';
                
                let opBtns = '';
                if (r.status === '待审批') {
                    opBtns = `
                        <div class="d-flex flex-column gap-1">
                            <button class="btn btn-success btn-sm approve-btn" onclick="approveLeave(${r.id},'已批准')" title="批准请假">
                                <i class="bi bi-check-circle me-1"></i>批准
                            </button>
                            <button class="btn btn-danger btn-sm reject-btn" onclick="approveLeave(${r.id},'已拒绝')" title="拒绝请假">
                                <i class="bi bi-x-circle me-1"></i>拒绝
                            </button>
                        </div>`;
                } else if (r.status === '已批准') {
                    const today = new Date().toISOString().split('T')[0];
                    const canCancel = r.end_date <= today;
                    
                    if (canCancel) {
                        opBtns = `
                            <div class="d-flex flex-column gap-1">
                                <button class="btn btn-info btn-sm cancel-btn" onclick="approveLeave(${r.id},'已销假')" title="标记为已销假">
                                    <i class="bi bi-arrow-repeat me-1"></i>销假
                                </button>
                                <small class="text-muted">请假已结束</small>
                            </div>`;
                    } else {
                        opBtns = `
                            <div class="d-flex flex-column gap-1">
                                <span class="text-muted small">请假进行中</span>
                                <small class="text-muted">${r.end_date} 结束</small>
                            </div>`;
                    }
                } else if (r.status === '已拒绝') {
                    opBtns = `
                        <div class="d-flex flex-column gap-1">
                            <span class="badge bg-secondary">已处理</span>
                            <small class="text-muted">申请被拒绝</small>
                        </div>`;
                } else if (r.status === '已销假') {
                    opBtns = `
                        <div class="d-flex flex-column gap-1">
                            <span class="badge bg-secondary">已完成</span>
                            <small class="text-muted">销假完成</small>
                        </div>`;
                } else {
                    opBtns = `
                        <div class="d-flex flex-column gap-1">
                            <span class="text-muted small">--</span>
                        </div>`;
                }
                
                return `<tr class="leave-row" data-leave-id="${r.id}">
                    <td>
                        <div class="d-flex flex-column">
                            <span class="fw-semibold">${r.name}</span>
                            <span class="text-muted small">${r.student_id}</span>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex flex-column">
                            <span class="fw-semibold">${r.start_date}</span>
                            <span class="text-muted small">至 ${r.end_date}</span>
                        </div>
                    </td>
                    <td>
                        <div class="${cellClass}" title="${reasonTitle}" ${needsTruncation ? 'onclick="showFullReason(\'' + r.reason.replace(/'/g, '\\\'').replace(/\n/g, '\\n') + '\', \'' + r.name + '\')"' : ''}>
                            <span class="${reasonClass}">${r.reason}</span>
                        </div>
                    </td>
                    <td>${statusHtml}</td>
                    <td>
                        <div class="d-flex flex-column">
                            <span class="small">${r.create_time.split(' ')[0]}</span>
                            <span class="text-muted small">${r.create_time.split(' ')[1]}</span>
                        </div>
                    </td>
                    <td class="text-center">${opBtns}</td>
                </tr>`;
            }).join('');
        } else {
            leaveApproveTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-5">
                        <div class="d-flex flex-column align-items-center">
                            <i class="bi bi-calendar-x text-muted fs-1 mb-3"></i>
                            <p class="text-muted mb-1">暂无请假记录</p>
                            <small class="text-muted">该班级目前没有学生申请请假</small>
                        </div>
                    </td>
                </tr>`;
            // 清空统计信息
            updateLeaveStats([]);
        }
    } catch (error) {
        console.error('加载请假记录失败:', error);
        leaveApproveTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                        <i class="bi bi-exclamation-triangle text-danger fs-1 mb-2"></i>
                        <p class="text-danger mb-0">加载失败</p>
                        <small class="text-muted">${error.message || '请检查网络连接后重试'}</small>
                    </div>
                </td>
            </tr>`;
        // 清空统计信息
        updateLeaveStats([]);
    }
}

// 审批请假（暴露到全局作用域）
window.approveLeave = async function(id, status) {
    const grade = sessionStorage.getItem('grade');
    if (!grade) {
        showError("无法获取年级信息，请重新登录。");
        return;
    }
    
    // 根据操作类型设置确认信息
    let confirmConfig = {
        title: '',
        icon: 'question',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonColor: '#28a745'
    };
    
    if (status === '已批准') {
        confirmConfig.title = '确认批准该请假申请吗？';
        confirmConfig.icon = 'success';
        confirmConfig.confirmButtonColor = '#28a745';
    } else if (status === '已拒绝') {
        confirmConfig.title = '确认拒绝该请假申请吗？';
        confirmConfig.icon = 'warning';
        confirmConfig.confirmButtonColor = '#dc3545';
    } else if (status === '已销假') {
        confirmConfig.title = '确认将该请假记录标记为已销假吗？';
        confirmConfig.icon = 'info';
        confirmConfig.confirmButtonColor = '#17a2b8';
    } else {
        confirmConfig.title = '确认执行此操作吗？';
        confirmConfig.icon = 'question';
    }
    
    const result = await Swal.fire({
        ...confirmConfig,
        showCancelButton: true,
        reverseButtons: true
    });
    
    if (!result.isConfirmed) return;
    
    // 找到对应的行和按钮
    const leaveRow = document.querySelector(`tr[data-leave-id="${id}"]`);
    if (!leaveRow) {
        showError("未找到对应的请假记录，请刷新页面重试。");
        return;
    }
    
    // 禁用该行的所有操作按钮
    const buttons = leaveRow.querySelectorAll('.approve-btn, .reject-btn, .cancel-btn');
    const originalContents = [];
    
    buttons.forEach((btn, index) => {
        originalContents[index] = btn.innerHTML;
        btn.disabled = true;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm"></span>';
        btn.classList.add('processing');
    });
    
    try {
        const res = await fetch(`/api/leaves/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ grade: grade, status: status })
        });
        
        if (!res.ok) {
            throw new Error(`HTTP ${res.status}: ${res.statusText}`);
        }
        
        const data = await res.json();
        
        if (data.success) {
            // 显示成功提示
            await Swal.fire({
                title: '操作成功！',
                text: getSuccessMessage(status),
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
            });
            
            // 重新加载表格
            await loadLeaveApproveTable();
        } else {
            throw new Error(data.error || '操作失败');
        }
    } catch (error) {
        console.error('审批请假失败:', error);
        
        // 显示错误提示
        await Swal.fire({
            title: '操作失败',
            text: error.message || '网络错误，请重试',
            icon: 'error',
            confirmButtonText: '确定'
        });
        
        // 恢复按钮状态
        buttons.forEach((btn, index) => {
            btn.disabled = false;
            btn.innerHTML = originalContents[index];
            btn.classList.remove('processing');
        });
    }
};

// 更新请假统计信息
function updateLeaveStats(leaveData) {
    const pendingCount = document.getElementById('pendingCount');
    const approvedCount = document.getElementById('approvedCount');
    const rejectedCount = document.getElementById('rejectedCount');
    const cancelledCount = document.getElementById('cancelledCount');
    
    if (!leaveData || leaveData.length === 0) {
        pendingCount.textContent = '0';
        approvedCount.textContent = '0';
        rejectedCount.textContent = '0';
        cancelledCount.textContent = '0';
        return;
    }
    
    const stats = {
        '待审批': 0,
        '已批准': 0,
        '已拒绝': 0,
        '已销假': 0
    };
    
    leaveData.forEach(leave => {
        if (leave.status in stats) {
            stats[leave.status]++;
        }
    });
    
    pendingCount.textContent = stats['待审批'];
    approvedCount.textContent = stats['已批准'];
    rejectedCount.textContent = stats['已拒绝'];
    cancelledCount.textContent = stats['已销假'];
    
    // 添加动画效果
    animateCounters();
}

// 数字动画效果
function animateCounters() {
    const counters = ['leaveCount', 'pendingCount', 'approvedCount', 'rejectedCount', 'cancelledCount'];
    
    counters.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.style.transform = 'scale(1.1)';
            element.style.transition = 'transform 0.3s ease';
            
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 300);
        }
    });
}

// getSuccessMessage 函数已移至 public.js

// ========================================
// 2. 班级数据管理和切换模块
// ========================================

/**
 * 加载班级列表
 * @param {string} teacherClasses - 教师任教的班级列表（逗号分隔）
 */
function loadClasses(teacherClasses) {
    const classSwitchContainer = document.getElementById('class-switch-container');
    classSwitchContainer.innerHTML = ''; // 清空

    if (!teacherClasses) {
        classSwitchContainer.innerHTML = '<p class="text-danger text-center">未找到您的班级信息</p>';
        return;
    }

    const classList = teacherClasses.split(',').map(c => c.trim()).filter(Boolean);

    if (classList.length > 0) {
        classList.forEach((className, index) => {
            const btn = document.createElement('button');
            btn.className = `btn btn-outline-primary class-switch-btn ${index === 0 ? 'active' : ''}`;
            btn.textContent = className;
            btn.dataset.className = className;
            classSwitchContainer.appendChild(btn);
        });
        // 自动加载第一个班级的数据
        currentClass = classList[0];
        currentLeaveClassName = classList[0]; // 设置请假审批的当前班级
        // 保存当前班级到sessionStorage
        sessionStorage.setItem('currentClass', currentClass);
        loadStudents(currentClass);
        loadLeaveApproveTable();
    } else {
        classSwitchContainer.innerHTML = '<p class="text-danger text-center">班级列表为空</p>';
    }
}

/**
 * 绑定班级切换事件
 */
function bindClassSwitchEvents() {
    const classSwitchContainer = document.getElementById('class-switch-container');
    if (!classSwitchContainer) return;

    classSwitchContainer.addEventListener('click', handleClassSwitchClick);
}

/**
 * 处理班级切换点击事件
 * @param {Event} e - 点击事件
 */
function handleClassSwitchClick(e) {
    if (!e.target.classList.contains('class-switch-btn')) return;

    // 更新按钮状态
    updateClassSwitchButtonState(e.target);

    // 切换班级
    const className = e.target.dataset.className;
    if (className && className !== currentClass) {
        switchToClass(className);
    }
}

/**
 * 更新班级切换按钮状态
 * @param {HTMLElement} activeButton - 激活的按钮
 */
function updateClassSwitchButtonState(activeButton) {
    // 移除其他按钮的 active 状态
    document.querySelectorAll('.class-switch-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // 为当前点击的按钮添加 active 状态
    activeButton.classList.add('active');
}

/**
 * 切换到指定班级
 * @param {string} className - 班级名称
 */
function switchToClass(className) {
    currentClass = className;
    currentLeaveClassName = className; // 更新请假审批的当前班级

    // 保存当前班级到sessionStorage
    sessionStorage.setItem('currentClass', currentClass);

    // 加载班级相关数据
    loadStudents(currentClass);
    loadLeaveApproveTable();
}

// ========================================
// 5. 留言管理功能模块
// ========================================

/**
 * 加载全班留言（未选学生时）
 * @param {string} className - 班级名称
 */
async function loadClassMessagesForAdmin(className) {
    const grade = sessionStorage.getItem('grade');
    const realNameDiv = document.getElementById('realNameMessages');
    const anonymousDiv = document.getElementById('anonymousMessages');
    realNameDiv.innerHTML = '<div class="text-center py-3 text-muted"><span class="spinner-border spinner-border-sm"></span> 加载中...</div>';
    anonymousDiv.innerHTML = '<div class="text-center py-3 text-muted"><span class="spinner-border spinner-border-sm"></span> 加载中...</div>';
    try {
        const res = await fetch(`/api/student-messages?grade=${encodeURIComponent(grade)}&class_name=${encodeURIComponent(className)}`);
        const data = await res.json();
        if (!data.success || !Array.isArray(data.data)) {
            realNameDiv.innerHTML = '<div class="text-muted">暂无实名留言</div>';
            anonymousDiv.innerHTML = '<div class="text-muted">暂无匿名留言</div>';
            return;
        }
        const realMsgs = data.data.filter(msg => !msg.is_anonymous);
        const anonMsgs = data.data.filter(msg => msg.is_anonymous);
        realNameDiv.innerHTML = realMsgs.length ? realMsgs.map(msg => renderStudentMsg(msg)).join('') : '<div class="text-muted">暂无实名留言</div>';
        anonymousDiv.innerHTML = anonMsgs.length ? anonMsgs.map(msg => renderStudentMsg(msg)).join('') : '<div class="text-muted">暂无匿名留言</div>';
        
        // 恢复两列布局，显示匿名留言部分
        const anonymousContainer = anonymousDiv.closest('.col-md-6');
        const realNameContainer = realNameDiv.closest('.col-md-6');
        if (anonymousContainer) {
            anonymousContainer.style.display = '';
        }
        if (realNameContainer) {
            realNameContainer.className = 'col-md-6';
        }
    } catch (e) {
        realNameDiv.innerHTML = '<div class="text-danger">加载失败</div>';
        anonymousDiv.innerHTML = '<div class="text-danger">加载失败</div>';
    }
}

// ========================================
// 3. 学生信息管理模块
// ========================================

/**
 * 加载学生列表
 * @param {string} className - 班级名称
 */
async function loadStudents(className) {
    const studentListContainer = document.getElementById('studentList');
    if (!studentListContainer) return;

    // 显示加载状态
    displayLoadingState(studentListContainer);

    const grade = sessionStorage.getItem('grade');
    if (!grade) {
        displayErrorState(studentListContainer, '无法获取年级信息');
        return;
    }

    try {
        const response = await fetch(`/api/student-info?grade=${encodeURIComponent(grade)}&class_name=${encodeURIComponent(className)}`);
        const data = await handleApiResponse(response);

        if (data.students && data.students.length > 0) {
            renderStudentList(data.students, studentListContainer);
            // 默认显示全班留言
            loadClassMessagesForAdmin(className);
            clearStudentDetailPanel();
        } else {
            displayEmptyStudentState(data.error);
        }
    } catch (error) {
        console.error('加载学生列表失败:', error);
        displayStudentLoadError(error.message);
    }
}

/**
 * 显示加载状态
 * @param {HTMLElement} container - 容器元素
 */
function displayLoadingState(container) {
    container.innerHTML = '<div class="text-center py-3"><span class="spinner-border spinner-border-sm"></span> 加载中...</div>';
}

/**
 * 显示错误状态
 * @param {HTMLElement} container - 容器元素
 * @param {string} message - 错误消息
 */
function displayErrorState(container, message) {
    container.innerHTML = `<div class="text-center py-3 text-danger">${message}</div>`;
}

/**
 * 渲染学生列表
 * @param {Array} students - 学生数组
 * @param {HTMLElement} container - 容器元素
 */
function renderStudentList(students, container) {
    container.innerHTML = '';

    students.forEach(student => {
        const studentItem = createStudentListItem(student, container);
        container.appendChild(studentItem);
    });
}

/**
 * 创建学生列表项
 * @param {Object} student - 学生对象
 * @param {HTMLElement} container - 容器元素
 * @returns {HTMLElement} 学生列表项元素
 */
function createStudentListItem(student, container) {
    const studentItem = document.createElement('a');
    studentItem.href = '#';
    studentItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';
    studentItem.dataset.studentId = student.id;

    studentItem.innerHTML = `
        <div>
            <h6 class="mb-1">${student.name}</h6>
            <small class="text-muted">学号: ${student.student_id}</small>
        </div>
        <i class="bi bi-chevron-right"></i>
    `;

    // 绑定点击事件
    studentItem.addEventListener('click', (event) => {
        event.preventDefault();
        selectStudent(studentItem, container, student);
    });

    return studentItem;
}

/**
 * 选择学生
 * @param {HTMLElement} studentItem - 学生列表项
 * @param {HTMLElement} container - 容器元素
 * @param {Object} student - 学生对象
 */
function selectStudent(studentItem, container, student) {
    // 更新选中状态
    container.querySelectorAll('.list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    studentItem.classList.add('active');

    // 显示学生详细信息
    showStudentInfo(student);
}

/**
 * 清空学生详细信息面板
 */
function clearStudentDetailPanel() {
    const detailPanel = document.getElementById('studentDetailPanel');
    if (detailPanel) {
        detailPanel.innerHTML = `
            <div class="col-12 text-center py-5 h-100 d-flex flex-column justify-content-center align-items-center">
                <i class="bi bi-info-circle text-muted fs-3"></i>
                <p class="mt-2 text-muted">请从左侧列表中选择一个学生以查看详细信息</p>
            </div>
        `;
    }
}

/**
 * 显示空学生状态
 * @param {string} error - 错误信息
 */
function displayEmptyStudentState(error) {
    const studentListContainer = document.getElementById('studentList');
    const realNameMessages = document.getElementById('realNameMessages');
    const anonymousMessages = document.getElementById('anonymousMessages');

    if (studentListContainer) {
        studentListContainer.innerHTML = `<div class="text-center py-3 text-muted">${error || '该班级下没有学生信息。'}</div>`;
    }

    clearStudentDetailPanel();

    if (realNameMessages) realNameMessages.innerHTML = '<div class="text-muted">暂无实名留言</div>';
    if (anonymousMessages) anonymousMessages.innerHTML = '<div class="text-muted">暂无匿名留言</div>';
}

/**
 * 显示学生加载错误
 * @param {string} errorMessage - 错误消息
 */
function displayStudentLoadError(errorMessage) {
    const studentListContainer = document.getElementById('studentList');
    const realNameMessages = document.getElementById('realNameMessages');
    const anonymousMessages = document.getElementById('anonymousMessages');

    if (studentListContainer) {
        studentListContainer.innerHTML = `<div class="text-center py-3 text-danger">加载学生列表失败: ${errorMessage}</div>`;
    }

    if (realNameMessages) realNameMessages.innerHTML = '<div class="text-danger">加载失败</div>';
    if (anonymousMessages) anonymousMessages.innerHTML = '<div class="text-danger">加载失败</div>';
}

// 在右侧显示学生详细信息
function showStudentInfo(student) {
    try {
        window.currentDetailStudent = student; // 保证全局可用
        // 清除当前班级信息，表示当前显示的是单个学生信息
        sessionStorage.removeItem('currentClass');
        // 清空留言区，避免全班留言残留
        document.getElementById('realNameMessages').innerHTML = '<div class="text-center py-3 text-muted"><span class="spinner-border spinner-border-sm"></span> 加载中...</div>';
        document.getElementById('anonymousMessages').innerHTML = '<div class="text-center py-3 text-muted"><span class="spinner-border spinner-border-sm"></span> 加载中...</div>';
        // 1. 加载学生留言
        loadStudentMessagesForAdmin(student);
        // 2. 加载教师留言（含编辑）
        loadTeacherMessageForAdmin(student, true); // 新增参数true，表示允许编辑当前留言

        const detailContainer = document.getElementById('studentDetailPanel');
        // 只读模式下的账户和密码
        const passwordDisplay = (student.password && student.password.trim()) ? student.password : '未设置';
        detailContainer.innerHTML = `
                    <form id="studentDetailForm" autocomplete="off">
                        <input type="hidden" id="studentDbId" value="${student.id}">
                        <ul class="list-group list-group-flush mb-3">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong>姓名</strong>
                                <span>${student.name}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong>学号</strong>
                                <span>${student.student_id}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong>班级</strong>
                                <span>${student.class_name}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong>年级</strong>
                                <span>${student.grade}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong>登录账号</strong>
                                <span id="readonlyUsername">${student.username || ''}</span>
                                <input type="text" class="form-control form-control-sm w-auto d-none" id="editUsername" value="${student.username || ''}" required>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong>密码</strong>
                                <span id="readonlyPassword">${passwordDisplay}</span>
                                <input type="text" class="form-control form-control-sm w-auto d-none" id="editPassword" value="${student.password || ''}" required>
                            </li>
                        </ul>
                        <div class="text-end">
                            <button type="button" class="btn btn-outline-secondary btn-sm me-2" id="editAccountBtn"><i class="bi bi-pencil"></i> 修改</button>
                            <button type="submit" class="btn btn-primary btn-sm d-none" id="saveAccountBtn"><i class="bi bi-save"></i> 保存</button>
                        </div>
                    </form>
                `;

        // 切换到编辑模式
        document.getElementById('editAccountBtn').onclick = function() {
            document.getElementById('readonlyUsername').classList.add('d-none');
            document.getElementById('readonlyPassword').classList.add('d-none');
            document.getElementById('editUsername').classList.remove('d-none');
            document.getElementById('editPassword').classList.remove('d-none');
            document.getElementById('editUsername').focus();
            this.classList.add('d-none');
            document.getElementById('saveAccountBtn').classList.remove('d-none');
        };

        // 表单提交事件
        const form = document.getElementById('studentDetailForm');
        form.onsubmit = async function(e) {
            e.preventDefault();
            const studentDbId = document.getElementById('studentDbId').value;
            const newUsername = document.getElementById('editUsername').value.trim();
            const newPassword = document.getElementById('editPassword').value.trim();
            const grade = student.grade;

            if (!newUsername || !newPassword) {
                Swal.fire('提示', '账号和密码不能为空', 'warning');
                return;
            }
            try {
                const res = await fetch('/api/update-student', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        grade,
                        student_db_id: Number(studentDbId),
                        new_username: newUsername,
                        new_password: newPassword
                    })
                });
                const data = await handleApiResponse(res);
                if (data.success) {
                    showSuccessToast('保存成功！');
                    // 只更新当前学生的信息，避免重新加载整个列表
                    const updatedStudent = { ...student, username: newUsername, password: newPassword };
                    showStudentInfo(updatedStudent);
                } else {
                    Swal.fire('保存失败', data.error || '未知错误', 'error');
                }
            } catch (err) {
                Swal.fire('保存失败', err.message, 'error');
            }
        };
    } catch (error) {
        console.error('显示学生信息失败:', error);
        Swal.fire('显示失败', '无法显示学生详细信息', 'error');
    }
}

// 加载学生留言（分实名/匿名）
function loadStudentMessagesForAdmin(student) {
    const grade = student.grade;
    const student_id = student.username; // 登录账号
    fetch(`/api/student-messages?grade=${encodeURIComponent(grade)}&student_id=${encodeURIComponent(student_id)}`)
        .then(res => res.json())
        .then(data => {
            const realNameDiv = document.getElementById('realNameMessages');
            const anonymousDiv = document.getElementById('anonymousMessages');
            if (!data.success || !Array.isArray(data.data)) {
                realNameDiv.innerHTML = '<div class="text-muted">暂无实名留言</div>';
                anonymousDiv.innerHTML = '<div class="text-muted">暂无匿名留言</div>';
                return;
            }
            const realMsgs = data.data.filter(msg => !msg.is_anonymous);
            // 注意：匿名留言在教师管理页面中被隐藏，不显示给教师
            realNameDiv.innerHTML = realMsgs.length ? realMsgs.map(msg => renderStudentMsg(msg)).join('') : '<div class="text-muted">暂无实名留言</div>';
            anonymousDiv.innerHTML = '<div class="text-muted">匿名留言已隐藏</div>';
            const anonymousContainer = anonymousDiv.closest('.col-md-6');
            if (anonymousContainer) {
                anonymousContainer.style.display = 'none';
            }
            const realNameContainer = realNameDiv.closest('.col-md-6');
            if (realNameContainer) {
                realNameContainer.className = 'col-md-12';
            }
        });
}
// 渲染单条学生留言
function renderStudentMsg(msg) {
    return `<div class="student-msg-card d-flex flex-row align-items-start mb-3 p-2 animate__animated animate__fadeIn" style="background:#f8fafc;border-radius:0.75rem;box-shadow:0 2px 12px rgba(82,122,175,0.08);border-left:5px solid var(--primary-color,#4f8cff);min-height:48px;">
        <strong class="msg-author me-3" style="font-size:1.08em;white-space:nowrap;">${msg.name}</strong>
        <div class="student-message-text flex-grow-1" style="word-break:break-all;white-space:pre-line;font-size:1.05em;line-height:1.7;min-width:0;">${msg.message.replace(/\n/g, '<br>')}</div>
        <button class="btn btn-outline-danger btn-sm ms-2" onclick="adminDeleteStudentMessage(${msg.id})" title="删除留言">
            <i class="bi bi-trash"></i>
        </button>
    </div>`;
}
// 删除学生留言（管理员/老师）
window.adminDeleteStudentMessage = async function(id) {
    if (!confirm('确定要删除这条留言吗？')) return;
    const grade = sessionStorage.getItem('grade');
    const res = await fetch(`/api/student-messages/${id}?grade=${encodeURIComponent(grade)}&admin=1`, {
        method: 'DELETE'
    });
    const data = await res.json();
    if (data.success) {
        // 显示成功提示
        Swal.fire({
            title: '删除成功',
            text: '留言已成功删除',
            icon: 'success',
            timer: 1500,
            showConfirmButton: false
        });
        
        // 重新加载留言列表
        const student = window.currentDetailStudent;
        if (student) {
            // 如果当前显示的是单个学生的留言，重新加载该学生的留言
            loadStudentMessagesForAdmin(student);
        } else {
            // 如果当前显示的是全班留言，重新加载全班留言
            const currentClass = sessionStorage.getItem('currentClass');
            if (currentClass) {
                loadClassMessagesForAdmin(currentClass);
            }
        }
    } else {
        Swal.fire('删除失败', data.error || '未知错误', 'error');
    }
}

// 加载教师留言（支持历史、编辑/保存、新增、删除）
function loadTeacherMessageForAdmin(student, allowEditCurrent = false) {
    const box = document.getElementById('teacherMessageBox');
    const grade = student.grade;
    const student_id = student.student_id;
    // 样式变量
    const cardStyle = 'border-0 shadow-sm rounded-4 mb-2 p-2 bg-white position-relative w-100';
    // 1. 教师寄语（当前教师留言）卡片，输入框为textarea，动态高度
    let currentMsgEditHtml = '';
    if (allowEditCurrent) {
        currentMsgEditHtml = `
        <div class="${cardStyle} animate__animated animate__fadeIn mb-4" style="border-left:6px solid #4f8cff;">
            <div class="d-flex align-items-center mb-2">
                <i class="bi bi-quote fs-2 text-primary me-2" style="opacity:.7;"></i>
                <span class="fs-5 fw-bold text-primary">教师寄语</span>
                        </div>
            <textarea class="form-control rounded-3 px-4 py-3 bg-light border border-2 border-primary-subtle shadow-sm fs-5 mb-2 teacher-remark-input" id="currentTeacherMsgInput" placeholder="输入或修改教师寄语，回车或失焦自动保存" style="max-width:600px;min-height:48px;resize:none;overflow:hidden;transition:box-shadow .2s, border-color .2s;">${student.teacher_message || ''}</textarea>
            <div class="text-muted small ms-1">自动保存，无需手动提交</div>
        </div>`;
    }
    // 2. 新增阶段性反馈卡片
    const newMsgHtml = `
        <div class="${cardStyle} d-flex flex-column flex-md-row align-items-md-center gap-2 mb-3" style="border-left:6px solid #5cb85c;">
            <i class="bi bi-chat-left-dots fs-4 text-success me-2"></i>
            <form id="teacherMessageForm" class="d-flex flex-grow-1 align-items-center gap-2 w-100">
                <textarea class="form-control rounded-pill px-3 py-2 bg-light border-0 shadow-sm flex-grow-1" id="newTeacherMessage" placeholder="新增一条阶段性反馈..." style="min-height:32px;resize:none;overflow:hidden;"></textarea>
                <button type="submit" class="btn btn-success rounded-pill px-4 d-flex align-items-center" style="font-weight:600;">
                    <i class="bi bi-plus-circle me-1"></i> 新增
                </button>
            </form>
        </div>`;
    // 3. 阶段性反馈历史卡片区（异步加载）
    box.innerHTML = `
        ${currentMsgEditHtml}
        ${newMsgHtml}
        <div id="teacherMessageHistoryList"></div>
    `;
    // 4. 教师寄语保存逻辑，textarea自适应高度
    if (allowEditCurrent) {
        const textarea = document.getElementById('currentTeacherMsgInput');
        let isSaving = false;
        function autoResizeTA(ta) {
            ta.style.height = 'auto';
            ta.style.height = (ta.scrollHeight) + 'px';
        }
        textarea.addEventListener('input', function() { autoResizeTA(textarea); });
        autoResizeTA(textarea);
        textarea.onfocus = function() {
            textarea.classList.add('border-primary','shadow-lg');
        };
        textarea.onblur = function() {
            textarea.classList.remove('border-primary','shadow-lg');
            saveCurrentMsg();
        };
        textarea.onkeydown = function(ev) {
            if (ev.key === 'Enter' && !ev.shiftKey) {
                ev.preventDefault();
                saveCurrentMsg();
            }
        };
        async function saveCurrentMsg() {
            if (isSaving) return;
            isSaving = true;
            const teacherMessage = textarea.value.trim();
            if (teacherMessage === (student.teacher_message || '')) {
                isSaving = false;
                return;
            }
            if (!teacherMessage) {
                Swal.fire('提示', '教师寄语不能为空', 'info');
                isSaving = false;
                textarea.value = student.teacher_message || '';
                autoResizeTA(textarea);
                return;
            }
            textarea.disabled = true;
            textarea.style.background = '#f8f9fa url("/static/img/loading.gif") no-repeat right center';
            try {
                const res = await fetch('/api/update-student', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        grade,
                        student_db_id: Number(student.id),
                        new_username: student.username,
                        new_password: student.password,
                        teacher_message: teacherMessage
                    })
                });
                const data = await handleApiResponse(res);
                if (data.success) {
                    showSuccessToast('教师寄语已保存！');
                    const updatedStudent = { ...student, teacher_message: teacherMessage };
                    window.currentDetailStudent = updatedStudent;
                    loadTeacherMessageForAdmin(updatedStudent, true);
            } else {
                    Swal.fire('保存失败', data.error || '未知错误', 'error');
                    textarea.value = student.teacher_message || '';
                    autoResizeTA(textarea);
                }
            } catch (err) {
                Swal.fire('保存失败', err.message, 'error');
                textarea.value = student.teacher_message || '';
                autoResizeTA(textarea);
            } finally {
                isSaving = false;
                textarea.disabled = false;
                textarea.style.background = '';
            }
        }
    }
    // 5. 新增阶段性反馈逻辑，textarea自适应高度
    const newMsgTA = document.getElementById('newTeacherMessage');
    function autoResizeTA(ta) {
        ta.style.height = 'auto';
        ta.style.height = (ta.scrollHeight) + 'px';
    }
    newMsgTA.addEventListener('input', function() { autoResizeTA(newMsgTA); });
    autoResizeTA(newMsgTA);
            document.getElementById('teacherMessageForm').onsubmit = async function(e) {
                e.preventDefault();
        const message = newMsgTA.value.trim();
                if (!message) {
            Swal.fire('提示', '反馈内容不能为空', 'info');
                    return;
                }
                const teacher_name = sessionStorage.getItem('username') || '教师';
        const btn = this.querySelector('button[type="submit"]');
        btn.disabled = true;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 新增';
                try {
                    const res = await fetch('/api/teacher-message-history', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            grade,
                            student_id,
                            teacher_name,
                            message
                        })
                    });
                    const data = await res.json();
                    if (data.success) {
                newMsgTA.value = '';
                autoResizeTA(newMsgTA);
                loadTeacherMessageForAdmin(student, true);
                    } else {
                        throw new Error(data.error || '未知错误');
                    }
                } catch (err) {
                    Swal.fire('保存失败', err.message, 'error');
        } finally {
            btn.disabled = false;
            btn.innerHTML = '<i class="bi bi-plus-circle me-1"></i> 新增';
        }
    };
    // 6. 阶段性反馈历史异步加载与渲染
    refreshTeacherMessageHistory(student);
}

// 新增：只刷新历史阶段性反馈区，不刷新教师寄语区
function refreshTeacherMessageHistory(student) {
    const grade = student.grade;
    const student_id = student.student_id;
    const cardStyle = 'border-0 shadow-sm rounded-4 mb-2 p-2 bg-white position-relative w-100';
    fetch(`/api/teacher-message-history?grade=${encodeURIComponent(grade)}&student_id=${encodeURIComponent(student_id)}`)
        .then(res => res.json())
        .then(data => {
            const historyList = document.getElementById('teacherMessageHistoryList');
            if (!data.success || !Array.isArray(data.data) || data.data.length === 0) {
                historyList.innerHTML = '<div class="text-muted">暂无阶段性反馈</div>';
                return;
            }
            historyList.innerHTML = data.data.map(msg => `
                <div class="${cardStyle} d-flex align-items-center gap-3 teacher-msg-item animate__animated animate__fadeIn" data-msg-id="${msg.id}" style="border-left:6px solid #ffb347;transition:box-shadow .2s;box-shadow:0 2px 12px 0 rgba(255,179,71,0.08);background:linear-gradient(90deg,#fffbe6 0,#fff 100%);max-width:100%;">
                    <div class="timeline-dot flex-shrink-0 me-2" style="width:16px;height:16px;border-radius:50%;background:#ffb347;box-shadow:0 0 0 2px #fff;position:relative;"></div>
                    <div class="flex-grow-1 teacher-msg-content teacher-msg-hover position-relative" style="word-break:break-all;white-space:pre-line;cursor:pointer;">
                        <div class="d-flex align-items-center mb-1">
                            <strong class="me-2 text-warning-emphasis" style="font-size:1em;">${msg.teacher_name || '教师'}</strong>
                            <small class="text-muted bg-warning-subtle rounded-pill px-2 py-1 ms-1" style="font-size:0.95em;">${msg.create_time}</small>
                        </div>
                        <div class="teacher-msg-text mt-1 text-dark-emphasis" data-msg-id="${msg.id}" title="点击可编辑" style="background:#fffbe6;border-radius:8px;padding:4px 10px;font-size:1em;">${msg.message.replace(/\n/g, '<br>')}</div>
                        <span class="edit-tip position-absolute start-0 bottom-0 mb-1 ms-2 px-2 py-1 bg-light border rounded-pill text-secondary small" style="display:none;opacity:.85;font-size:12px;">点击可编辑</span>
                    </div>
                    <button class='btn btn-sm btn-outline-danger rounded-pill ms-2 delete-teacher-msg-btn position-absolute top-0 end-0 mt-2 me-2' style="display:none;z-index:2;" onclick='deleteTeacherMessageHistory(${msg.id},true)'>删除</button>
                </div>
            `).join('');
            // 悬浮高亮，显示删除按钮
            historyList.querySelectorAll('.teacher-msg-item').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    card.classList.add('bg-warning-subtle');
                    card.querySelector('.edit-tip').style.display = 'inline-block';
                    const delBtn = card.querySelector('.delete-teacher-msg-btn');
                    if (delBtn) delBtn.style.display = 'inline-block';
                    card.style.boxShadow = '0 4px 24px 0 rgba(255,179,71,0.18)';
                });
                card.addEventListener('mouseleave', function() {
                    card.classList.remove('bg-warning-subtle');
                    card.querySelector('.edit-tip').style.display = 'none';
                    const delBtn = card.querySelector('.delete-teacher-msg-btn');
                    if (delBtn) delBtn.style.display = 'none';
                    card.style.boxShadow = '0 2px 12px 0 rgba(255,179,71,0.08)';
                });
            });
            // 编辑逻辑：直接用textarea自适应高度
            historyList.querySelectorAll('.teacher-msg-text').forEach(div => {
                div.addEventListener('click', function() {
                    if (this.parentElement.querySelector('textarea')) return;
                    const msgId = this.getAttribute('data-msg-id');
                    const oldText = this.innerText.replace(/\n/g, '\n');
                    const textarea = document.createElement('textarea');
                    textarea.className = 'form-control form-control-sm rounded-3 px-3 py-2 border border-warning border-2 shadow-sm animate__animated animate__fadeIn';
                    textarea.value = oldText;
                    textarea.style.maxWidth = '90%';
                    textarea.style.display = 'inline-block';
                    textarea.style.width = '100%';
                    textarea.style.minHeight = '32px';
                    textarea.style.resize = 'none';
                    textarea.style.overflow = 'hidden';
                    textarea.style.marginTop = '2px';
                    function autoResizeTA(ta) {
                        ta.style.height = 'auto';
                        ta.style.height = (ta.scrollHeight) + 'px';
                    }
                    textarea.addEventListener('input', function() { autoResizeTA(textarea); });
                    autoResizeTA(textarea);
                    this.style.display = 'none';
                    this.parentElement.appendChild(textarea);
                    // 编辑时显示保存/取消按钮
                    const btnGroup = document.createElement('div');
                    btnGroup.className = 'btn-group ms-2 animate__animated animate__fadeIn';
                    btnGroup.innerHTML = `
                        <button type="button" class="btn btn-success btn-sm rounded-pill px-3">保存</button>
                        <button type="button" class="btn btn-secondary btn-sm rounded-pill px-3">取消</button>
                    `;
                    this.parentElement.appendChild(btnGroup);
                    // 禁用删除按钮
                    const delBtn = this.parentElement.parentElement.querySelector('.delete-teacher-msg-btn');
                    if (delBtn) delBtn.disabled = true;
                    textarea.focus();
                    let isSaving = false;
                    async function saveEdit() {
                        if (isSaving) return;
                        isSaving = true;
                        const newText = textarea.value.trim();
                        if (!newText || newText === oldText) {
                            textarea.remove();
                            btnGroup.remove();
                            div.style.display = '';
                            if (delBtn) delBtn.disabled = false;
                            return;
                        }
                        textarea.disabled = true;
                        btnGroup.querySelector('button.btn-success').disabled = true;
                        btnGroup.querySelector('button.btn-success').innerHTML = '<span class="spinner-border spinner-border-sm"></span> 保存';
                        try {
                            const res = await fetch(`/api/teacher-message-history/${msgId}?grade=${encodeURIComponent(grade)}`, {
                                method: 'PUT',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ message: newText })
                            });
                            const data = await res.json();
                            if (data.success) {
                                showSuccessToast('反馈已修改！');
                                refreshTeacherMessageHistory(student);
                            } else {
                                Swal.fire('保存失败', data.error || '未知错误', 'error');
                                textarea.remove();
                                btnGroup.remove();
                                div.style.display = '';
                                if (delBtn) delBtn.disabled = false;
                            }
                        } catch (err) {
                            Swal.fire('保存失败', err.message, 'error');
                            textarea.remove();
                            btnGroup.remove();
                            div.style.display = '';
                            if (delBtn) delBtn.disabled = false;
                        }
                    }
                    textarea.addEventListener('keydown', function(ev) {
                        if (ev.key === 'Enter' && !ev.shiftKey) {
                            ev.preventDefault();
                            saveEdit();
                        } else if (ev.key === 'Escape') {
                            textarea.remove();
                            btnGroup.remove();
                            div.style.display = '';
                            if (delBtn) delBtn.disabled = false;
                        }
                    });
                    textarea.addEventListener('input', function() { autoResizeTA(textarea); });
                    textarea.addEventListener('blur', saveEdit);
                    btnGroup.querySelector('button.btn-success').onclick = saveEdit;
                    btnGroup.querySelector('button.btn-secondary').onclick = function() {
                        textarea.remove();
                        btnGroup.remove();
                        div.style.display = '';
                        if (delBtn) delBtn.disabled = false;
                    };
                });
            });
        });
}

/**
 * 删除教师阶段性反馈（支持只刷新历史区）
 * @param {number} id - 反馈ID
 * @param {boolean} onlyHistory - 是否只刷新历史区
 */
window.deleteTeacherMessageHistory = async function(id, onlyHistory=false) {
    if (!confirm('确定要删除这条阶段性反馈吗？')) return;
    const grade = sessionStorage.getItem('grade');
    const student = window.currentDetailStudent;
    const res = await fetch(`/api/teacher-message-history/${id}?grade=${encodeURIComponent(grade)}`, {
        method: 'DELETE'
    });
    const data = await res.json();
    if (data.success) {
        if (onlyHistory && student) {
            refreshTeacherMessageHistory(student);
        } else if (student) {
            loadTeacherMessageForAdmin(student, true);
        }
    } else {
        alert('删除失败：' + (data.error || '未知错误'));
    }
}

// 优化：学生列表（侧边栏）样式与teacher_analyze.js一致
function updateStudentList(studentInfo) {
    const studentListContainer = document.getElementById('studentList');
    if (!studentListContainer) return;
    studentListContainer.innerHTML = '';
    const students = Object.entries(studentInfo).map(([studentId, info]) => ({
        id: studentId,
        name: info.姓名,
        rank: info.排名,
        studentNo: info.学号,
        total: info.总分
    })).sort((a, b) => a.rank - b.rank);
    students.forEach(student => {
        const listItem = document.createElement('a');
        listItem.href = '#';
        listItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center px-3 py-2 mb-1 rounded-3 student-list-item';
        let badgeClass = '';
        if (student.rank <= 3) {
            badgeClass = 'bg-danger';
        } else if (student.rank <= 10) {
            badgeClass = 'bg-warning text-dark';
        } else if (student.rank <= 20) {
            badgeClass = 'bg-primary';
        } else {
            badgeClass = 'bg-secondary';
        }
        listItem.innerHTML = `
            <div class="d-flex align-items-center">
                <span class="badge ${badgeClass} me-2" style="width: 35px;">${student.rank}</span>
                <span class="fw-semibold">${student.name}</span>
                <small class="text-muted ms-2">(${student.studentNo})</small>
                <span class="text-success ms-2" style="font-size:0.95em;">总分: ${student.total ?? '-'}</span>
            </div>
            <i class="bi bi-chevron-right"></i>`;
        listItem.addEventListener('click', (e) => {
            e.preventDefault();
            studentListContainer.querySelectorAll('.list-group-item').forEach(item => item.classList.remove('active'));
            listItem.classList.add('active');
            showStudentInfo(student.id);
        });
        // hover效果
        listItem.addEventListener('mouseenter', () => {
            listItem.classList.add('shadow-sm');
        });
        listItem.addEventListener('mouseleave', () => {
            listItem.classList.remove('shadow-sm');
        });
        studentListContainer.appendChild(listItem);
    });
}

// 显示完整请假原因
window.showFullReason = function(reason, studentName) {
    // 解码换行符
    const decodedReason = reason.replace(/\\n/g, '\n');
    
    // 创建模态框
    const modalHtml = `
        <div class="modal fade leave-reason-modal" id="fullReasonModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-chat-text me-2"></i>${studentName} 的请假原因
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="reason-content">${decodedReason.replace(/\n/g, '<br>')}</div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i>关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 移除已存在的模态框
    const existingModal = document.getElementById('fullReasonModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('fullReasonModal'));
    modal.show();
    
    // 模态框关闭后移除DOM元素
    document.getElementById('fullReasonModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
    
    // 添加进入动画
    setTimeout(() => {
        const modalContent = document.querySelector('.leave-reason-modal .modal-content');
        if (modalContent) {
            modalContent.style.transform = 'scale(1)';
            modalContent.style.opacity = '1';
        }
    }, 100);
};